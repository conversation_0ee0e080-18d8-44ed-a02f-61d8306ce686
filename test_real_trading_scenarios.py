#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实交易场景测试
使用实际的币安交易对规则测试普遍性精度解决方案
"""

import sys
import os
from decimal import Decimal, getcontext
import logging

# 添加策略模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

# 设置高精度计算
getcontext().prec = 50

class MockLogger:
    """模拟日志记录器"""
    def debug(self, msg):
        print(f"DEBUG: {msg}")
    
    def info(self, msg):
        print(f"INFO: {msg}")
    
    def warning(self, msg):
        print(f"WARNING: {msg}")
    
    def error(self, msg):
        print(f"ERROR: {msg}")

class RealTradingTester:
    """真实交易场景测试器"""
    
    def __init__(self):
        self.log = MockLogger()
        
    def safe_format_to_precision(self, value, step_size, param_name="value"):
        """基于 step_size 的精度控制方法"""
        from decimal import Decimal, ROUND_DOWN
        
        if step_size <= 0:
            self.log.warning(f"{param_name} step_size无效: {step_size}, 使用原值")
            return str(float(value))
        
        # 转换为Decimal确保精度
        decimal_value = Decimal(str(value))
        decimal_step = Decimal(str(step_size))
        
        if decimal_value <= 0:
            self.log.warning(f"{param_name}值无效: {value}, 使用原值")
            return str(float(value))
        
        # 向下取整到step_size的倍数
        steps = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
        
        # 确保至少为1步（除非原值为0）
        if steps == 0 and decimal_value > 0:
            steps = Decimal('1')
            self.log.debug(f"{param_name} 调整为最小步长: {decimal_step}")
        
        # 计算格式化后的值（保持Decimal精度）
        formatted_decimal = steps * decimal_step
        
        # 根据step_size确定显示精度（完全基于step_size的小数位数）
        step_str = f"{float(decimal_step):.10f}".rstrip('0').rstrip('.')
        if '.' in step_str:
            # step_size有小数部分，计算其有效小数位数
            decimal_part = step_str.split('.')[1]
            display_precision = len(decimal_part)
        else:
            # step_size是整数，使用整数显示
            display_precision = 0
        
        # 限制最大8位小数（币安API限制）
        display_precision = min(display_precision, 8)
        
        self.log.debug(f"[精度计算] step_size={step_size} -> display_precision={display_precision}")
        
        # 使用Decimal进行精确格式化，避免浮点数舍入误差
        if display_precision > 0:
            # 使用Decimal的quantize方法进行精确舍入
            quantized_decimal = formatted_decimal.quantize(Decimal('0.' + '0' * display_precision))
            result = f"{quantized_decimal:.{display_precision}f}"
        else:
            result = str(int(formatted_decimal))
        
        self.log.debug(f"[精度格式化完成] value={value} -> {result} (precision={display_precision})")
        return result

def test_real_binance_symbols():
    """测试真实的币安交易对"""
    
    print("=" * 80)
    print("真实币安交易对测试")
    print("=" * 80)
    
    tester = RealTradingTester()
    
    # 真实的币安交易对规则（基于实际API数据）
    real_symbols = [
        {
            "symbol": "BTCUSDT",
            "tick_size": "0.01000000",
            "step_size": "0.00001000",
            "test_price": 45123.45,
            "test_quantity": 0.001234567,
            "description": "BTC/USDT - 主流币对"
        },
        {
            "symbol": "ETHUSDT", 
            "tick_size": "0.01000000",
            "step_size": "0.00001000",
            "test_price": 3456.78,
            "test_quantity": 0.012345678,
            "description": "ETH/USDT - 主流币对"
        },
        {
            "symbol": "ADAUSDT",
            "tick_size": "0.00010000", 
            "step_size": "0.10000000",
            "test_price": 0.4567,
            "test_quantity": 123.456789,
            "description": "ADA/USDT - 小价格币种"
        },
        {
            "symbol": "DOGEUSDT",
            "tick_size": "0.00001000",
            "step_size": "1.00000000", 
            "test_price": 0.12345,
            "test_quantity": 1234.56789,
            "description": "DOGE/USDT - 整数数量币种"
        },
        {
            "symbol": "SHIBUSDT",
            "tick_size": "0.00000001",
            "step_size": "1000.00000000",
            "test_price": 0.00001234,
            "test_quantity": 12345678.9,
            "description": "SHIB/USDT - 极小价格币种"
        },
        {
            "symbol": "PEPPUSDT",
            "tick_size": "0.00000001",
            "step_size": "10000.00000000",
            "test_price": 0.00000123,
            "test_quantity": 123456789.12,
            "description": "PEPE/USDT - 超小价格币种"
        }
    ]
    
    success_count = 0
    total_count = len(real_symbols)
    
    for i, symbol_info in enumerate(real_symbols, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}: {symbol_info['description']}")
        print(f"交易对: {symbol_info['symbol']}")
        print(f"{'='*60}")
        
        try:
            # 提取参数
            symbol = symbol_info['symbol']
            tick_size = float(symbol_info['tick_size'])
            step_size = float(symbol_info['step_size'])
            test_price = symbol_info['test_price']
            test_quantity = symbol_info['test_quantity']
            
            print(f"交易规则:")
            print(f"  tick_size: {tick_size}")
            print(f"  step_size: {step_size}")
            print(f"测试数据:")
            print(f"  price: {test_price}")
            print(f"  quantity: {test_quantity}")
            
            # 测试价格格式化
            print(f"\n价格格式化测试:")
            formatted_price = tester.safe_format_to_precision(test_price, tick_size)
            print(f"  输入: {test_price}")
            print(f"  输出: {formatted_price}")
            
            # 测试数量格式化
            print(f"\n数量格式化测试:")
            formatted_quantity = tester.safe_format_to_precision(test_quantity, step_size)
            print(f"  输入: {test_quantity}")
            print(f"  输出: {formatted_quantity}")
            
            # 验证结果
            print(f"\n结果验证:")
            
            # 验证价格精度
            try:
                price_decimal = Decimal(formatted_price)
                tick_decimal = Decimal(str(tick_size))
                
                # 检查价格是否符合 tick_size 规则
                remainder = price_decimal % tick_decimal
                if abs(remainder) < Decimal('1e-10'):  # 考虑浮点精度误差
                    print(f"  OK 价格精度正确: {formatted_price} 符合 tick_size={tick_size}")
                else:
                    print(f"  ! 价格精度警告: {formatted_price} 可能不完全符合 tick_size={tick_size}")
                    
            except Exception as e:
                print(f"  X 价格验证失败: {e}")
            
            # 验证数量精度
            try:
                qty_decimal = Decimal(formatted_quantity)
                step_decimal = Decimal(str(step_size))
                
                # 检查数量是否符合 step_size 规则
                if qty_decimal >= step_decimal:
                    # 计算数量是否为step_size的整数倍
                    steps = qty_decimal / step_decimal
                    # 检查是否为整数倍（允许极小的浮点误差）
                    steps_rounded = steps.quantize(Decimal('1'))
                    if abs(steps - steps_rounded) < Decimal('1e-10'):
                        print(f"  OK 数量精度正确: {formatted_quantity} = {steps_rounded} × {step_size}")
                        success_count += 1
                    else:
                        print(f"  ! 数量精度警告: {formatted_quantity} 不是 step_size={step_size} 的整数倍")
                        print(f"    计算: {qty_decimal} / {step_decimal} = {steps}")
                else:
                    print(f"  ! 数量过小: {formatted_quantity} < step_size={step_size}")
                    
            except Exception as e:
                print(f"  X 数量验证失败: {e}")
            
            # 计算名义价值
            try:
                notional = Decimal(formatted_price) * Decimal(formatted_quantity)
                print(f"  名义价值: {notional} USDT")
                
                # 检查最小名义价值（币安通常要求 >= 5 USDT）
                if notional >= Decimal('5'):
                    print(f"  OK 名义价值符合最小交易要求")
                else:
                    print(f"  ! 名义价值可能低于最小交易要求 (5 USDT)")
                    
            except Exception as e:
                print(f"  X 名义价值计算失败: {e}")
                
        except Exception as e:
            print(f"  ✗ 测试执行失败: {e}")
    
    # 测试总结
    print(f"\n{'='*80}")
    print(f"真实交易对测试总结")
    print(f"{'='*80}")
    print(f"总测试交易对: {total_count}")
    print(f"精度处理正确: {success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    return success_count == total_count

def test_problematic_cases():
    """测试之前出现问题的特殊案例"""
    
    print(f"\n{'='*80}")
    print("问题案例回归测试")
    print(f"{'='*80}")
    
    tester = RealTradingTester()
    
    # 之前出现问题的案例
    problem_cases = [
        {
            "name": "SKYUSDT 精度问题",
            "symbol": "SKYUSDT",
            "price": 0.001234567890123456,
            "quantity": 1234.567890123456,
            "tick_size": 0.000001,
            "step_size": 0.1,
            "description": "之前导致精度错误的案例"
        },
        {
            "name": "PIPPINUSDT 精度问题", 
            "symbol": "PIPPINUSDT",
            "price": 0.000000123456789,
            "quantity": 12345678.123456789,
            "tick_size": 0.000000001,
            "step_size": 1000.0,
            "description": "极小价格导致的精度问题"
        },
        {
            "name": "ORDERUSDT 格式化问题",
            "symbol": "ORDERUSDT", 
            "price": 1.234567890123456,
            "quantity": 0.000123456789,
            "tick_size": 0.000001,
            "step_size": 0.000001,
            "description": "格式化精度不一致问题"
        }
    ]
    
    for case in problem_cases:
        print(f"\n测试: {case['name']}")
        print(f"描述: {case['description']}")
        print(f"参数: price={case['price']}, quantity={case['quantity']}")
        print(f"规则: tick_size={case['tick_size']}, step_size={case['step_size']}")
        
        try:
            # 使用新方法处理
            formatted_price = tester.safe_format_to_precision(case['price'], case['tick_size'])
            formatted_quantity = tester.safe_format_to_precision(case['quantity'], case['step_size'])
            
            print(f"结果: price_str={formatted_price}, qty_str={formatted_quantity}")
            
            # 验证是否解决了问题
            try:
                price_val = float(formatted_price)
                qty_val = float(formatted_quantity)
                notional = price_val * qty_val
                
                print(f"验证: 价格={price_val}, 数量={qty_val}, 名义价值={notional}")
                print("OK 问题案例处理正常")
                
            except Exception as e:
                print(f"X 验证失败: {e}")
                
        except Exception as e:
            print(f"X 处理失败: {e}")

if __name__ == "__main__":
    # 执行真实交易对测试
    main_success = test_real_binance_symbols()
    
    # 执行问题案例回归测试
    test_problematic_cases()
    
    # 最终结果
    print(f"\n{'='*80}")
    if main_success:
        print("🎉 真实交易场景测试完成！普遍性精度解决方案在实际环境中工作正常！")
    else:
        print("❌ 真实交易场景测试发现问题，需要进一步优化")
    print(f"{'='*80}")
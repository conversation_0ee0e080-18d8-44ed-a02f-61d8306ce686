#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整修复方案 - 验证PIPPINUSDT精度问题是否彻底解决
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal, ROUND_DOWN, getcontext

def format_to_tick_precision(price, tick_size):
    """格式化价格到tick精度 - 修复版本"""
    getcontext().prec = 50
    
    price_decimal = Decimal(str(price))
    tick_decimal = Decimal(str(tick_size))
    
    # 计算应该保留的小数位数
    tick_str = f"{tick_decimal:.20f}".rstrip('0')
    if '.' in tick_str:
        decimal_places = len(tick_str.split('.')[1])
    else:
        decimal_places = 0
    
    # 向下舍入到最近的tick_size倍数
    steps = (price_decimal / tick_decimal).quantize(Decimal('1'), rounding=ROUND_DOWN)
    rounded_price = steps * tick_decimal
    
    # 关键修复：如果价格被舍入为0但原价格为正，设为一个最小单位
    if rounded_price == 0 and price_decimal > 0:
        rounded_price = tick_decimal
    
    # 格式化为字符串，保留适当的小数位数
    if decimal_places == 0:
        return str(int(rounded_price))
    else:
        formatted = f"{rounded_price:.{decimal_places}f}"
        return formatted.rstrip('0').rstrip('.')

def format_to_step_precision(qty, step_size):
    """格式化数量到step精度 - 修复版本"""
    getcontext().prec = 50
    
    qty_decimal = Decimal(str(qty))
    step_decimal = Decimal(str(step_size))
    
    # 计算应该保留的小数位数
    step_str = f"{step_decimal:.20f}".rstrip('0')
    if '.' in step_str:
        decimal_places = len(step_str.split('.')[1])
    else:
        decimal_places = 0
    
    # 向下舍入到最近的step_size倍数
    steps = (qty_decimal / step_decimal).quantize(Decimal('1'), rounding=ROUND_DOWN)
    rounded_qty = steps * step_decimal
    
    # 关键修复：如果数量被舍入为0但原数量为正，设为一个最小单位
    if rounded_qty == 0 and qty_decimal > 0:
        rounded_qty = step_decimal
    
    # 格式化为字符串，保留适当的小数位数
    if decimal_places == 0:
        return str(int(rounded_qty))
    else:
        formatted = f"{rounded_qty:.{decimal_places}f}"
        return formatted.rstrip('0').rstrip('.')

def test_pippinusdt_scenarios():
    """测试PIPPINUSDT的各种场景"""
    print("=== 测试PIPPINUSDT精度修复 ===")
    
    # PIPPINUSDT的实际交易规则（基于之前的分析）
    tick_size = 0.00001  # 5位小数
    step_size = 0.001    # 3位小数
    min_qty = 0.001
    min_notional = 5.0
    
    test_cases = [
        # (价格, 数量, 描述)
        (0.4, 236.417, "实际PIPPINUSDT参数"),
        (0.00005, 100000, "极小价格"),
        (0.000001, 5000000, "超小价格"),
        (1.23456, 10.123456, "正常精度"),
        (0.12345, 50.987654, "高精度测试")
    ]
    
    for price, qty, desc in test_cases:
        print(f"\n--- {desc} ---")
        print(f"原始: price={price}, qty={qty}")
        
        # 格式化
        formatted_price = format_to_tick_precision(price, tick_size)
        formatted_qty = format_to_step_precision(qty, step_size)
        
        print(f"格式化: price='{formatted_price}', qty='{formatted_qty}'")
        
        # 验证
        price_float = float(formatted_price)
        qty_float = float(formatted_qty)
        notional = price_float * qty_float
        
        # 检查各项条件
        checks = {
            "价格>0": price_float > 0,
            "数量>0": qty_float > 0,
            "数量>=最小": qty_float >= min_qty,
            "名义价值>=最小": notional >= min_notional,
            "价格精度": len(formatted_price.split('.')[-1]) <= 5 if '.' in formatted_price else True,
            "数量精度": len(formatted_qty.split('.')[-1]) <= 3 if '.' in formatted_qty else True
        }
        
        all_passed = all(checks.values())
        print(f"名义价值: {notional:.6f}")
        
        for check_name, result in checks.items():
            status = "✓" if result else "✗"
            print(f"{status} {check_name}: {result}")
        
        print(f"总体结果: {'✓ 通过' if all_passed else '✗ 失败'}")
        
        if all_passed:
            print("🎉 订单应该成功，不会出现-1111错误")
        else:
            print("❌ 订单可能失败")

def test_symbols_info_structure():
    """测试symbols_info缓存结构"""
    print("\n=== 测试symbols_info缓存结构 ===")
    
    # 模拟修复后的缓存结构
    mock_symbols_info = {
        'PIPPINUSDT': {
            'age_days': 15.5,
            'contractType': 'PERPETUAL',
            'status': 'TRADING',
            'filters': [
                {
                    'filterType': 'PRICE_FILTER',
                    'tickSize': '0.00001'
                },
                {
                    'filterType': 'LOT_SIZE',
                    'stepSize': '0.001',
                    'minQty': '0.001'
                },
                {
                    'filterType': 'MIN_NOTIONAL',
                    'notional': '5.0'
                }
            ]
        }
    }
    
    # 模拟从缓存中提取交易规则的过程
    symbol = 'PIPPINUSDT'
    s_info = mock_symbols_info.get(symbol, {})
    filters = s_info.get('filters', [])
    
    tick_size = None
    step_size = None
    min_qty = None
    min_notional = None
    
    for f in filters:
        ft = f.get('filterType')
        if ft == 'PRICE_FILTER':
            tick_size = float(f.get('tickSize', 0) or 0)
        elif ft == 'LOT_SIZE':
            step_size = float(f.get('stepSize', 0) or 0)
            min_qty = float(f.get('minQty', 0) or 0)
        elif ft == 'MIN_NOTIONAL':
            min_notional = float(f.get('notional', 0) or 0)
    
    print(f"从缓存提取的交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    if all([tick_size, step_size, min_qty, min_notional]):
        print("✓ 成功从缓存中提取完整的交易规则")
        return True
    else:
        print("✗ 缓存中缺少交易规则信息")
        return False

if __name__ == "__main__":
    print("开始测试完整修复方案...")
    
    # 测试精度格式化
    test_pippinusdt_scenarios()
    
    # 测试缓存结构
    cache_ok = test_symbols_info_structure()
    
    print("\n=== 总结 ===")
    if cache_ok:
        print("🎉 修复方案完整，应该能彻底解决-1111精度错误")
        print("✓ 价格格式化修复完成")
        print("✓ 缓存结构修复完成")
        print("✓ 交易规则提取正常")
    else:
        print("❌ 修复方案不完整，需要进一步调试")
    
    print("\n建议:")
    print("1. 重启交易程序以应用修复")
    print("2. 监控PIPPINUSDT的下单日志")
    print("3. 如果仍有问题，检查网络连接和API响应")
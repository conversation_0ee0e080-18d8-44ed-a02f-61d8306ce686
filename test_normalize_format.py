#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试normalize()和format()的输出差异
"""

import decimal as dec

def test_normalize_vs_format():
    """测试normalize()和format()的输出差异"""
    print("=== 测试normalize()和format()的输出差异 ===")
    
    dec.getcontext().prec = 18
    
    # ICNTUSDT交易规则
    tick_size = 0.0001
    step_size = 1.0
    
    # 测试用例
    test_cases = [
        (0.2412, 41.0),
        (0.24127964, 412.8819157720892),
        (0.2412, 30.75),
        (0.2412, 20.5),
        (0.2412, 10.25),
    ]
    
    for i, (price, qty) in enumerate(test_cases):
        print(f"\n--- 测试用例 {i+1} ---")
        print(f"输入: price={price}, qty={qty}")
        
        # 价格格式化 - 旧方法（备份文件中的）
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        price_str_old = format(price_dec.normalize(), 'f')
        
        # 数量格式化 - 旧方法（备份文件中的）
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(1.0)))
        qty_str_old = format(qty_dec.normalize(), 'f')
        
        print(f"旧方法输出: price_str='{price_str_old}', qty_str='{qty_str_old}'")
        print(f"旧方法类型: price_str={type(price_str_old)}, qty_str={type(qty_str_old)}")
        
        # 检查是否包含科学计数法
        if 'e' in price_str_old.lower():
            print(f"⚠️  旧方法价格包含科学计数法: {price_str_old}")
        if 'e' in qty_str_old.lower():
            print(f"⚠️  旧方法数量包含科学计数法: {qty_str_old}")
        
        # 新方法（当前策略中的）
        def get_decimal_places(value):
            if value == 0:
                return 0
            str_val = f"{value:.20f}".rstrip('0')
            if '.' in str_val:
                return len(str_val.split('.')[-1])
            return 0
        
        tick_decimals = get_decimal_places(tick_size)
        step_decimals = get_decimal_places(step_size)
        
        price_str_new = f"{float(price_dec):.{tick_decimals}f}".rstrip('0').rstrip('.')
        if not price_str_new or price_str_new == '':
            price_str_new = f"{float(price_dec):.{max(1, tick_decimals)}f}"
        
        qty_str_new = f"{float(qty_dec):.{step_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str_new or qty_str_new == '':
            qty_str_new = f"{float(qty_dec):.{max(1, step_decimals)}f}"
        
        print(f"新方法输出: price_str='{price_str_new}', qty_str='{qty_str_new}'")
        print(f"新方法类型: price_str={type(price_str_new)}, qty_str={type(qty_str_new)}")
        
        # 检查是否包含科学计数法
        if 'e' in price_str_new.lower():
            print(f"⚠️  新方法价格包含科学计数法: {price_str_new}")
        if 'e' in qty_str_new.lower():
            print(f"⚠️  新方法数量包含科学计数法: {qty_str_new}")
        
        # 比较差异
        if price_str_old != price_str_new:
            print(f"🔍 价格格式化差异: 旧='{price_str_old}' vs 新='{price_str_new}'")
        if qty_str_old != qty_str_new:
            print(f"🔍 数量格式化差异: 旧='{qty_str_old}' vs 新='{qty_str_new}'")

if __name__ == '__main__':
    test_normalize_vs_format()
import requests
import time
import hmac
import hashlib
import urllib.parse
import socket
import warnings
import logging
from typing import Dict, Any, Optional

class HttpClient:
    def __init__(self, api_key: str, api_secret: str, base_url: str, verify_ssl: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = base_url.rstrip('/')
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        self.log = logging.getLogger('HttpClient')
        
        # 时间戳校正相关
        self.time_offset = 0  # 服务器时间与本地时间的偏差（毫秒）
        self.last_time_sync = 0  # 上次同步时间的时间戳
        self.time_sync_interval = 1800  # 30分钟同步一次服务器时间（缩短间隔）
        self.safety_margin = 500  # 安全边距（毫秒）
        self.sync_retry_count = 3  # 同步重试次数
        
        # 禁用所有SSL警告
        import urllib3
        warnings.filterwarnings('ignore', category=urllib3.exceptions.InsecureRequestWarning)
        warnings.filterwarnings('ignore', category=UserWarning, module='urllib3')
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 配置代理
        self._setup_proxy()
        
        # 测试代理连接
        if not self._test_proxy_connection():
            self.log.warning("代理连接测试失败，尝试直连模式")
            self.session.proxies = {}
        
        # 初始化时同步服务器时间
        self._sync_server_time()
    
    def _setup_proxy(self):
        """根据IP地址强制设置代理模式（修复版）"""
        # 获取本地IP
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
        except:
            local_ip = "127.0.0.1"

        # 强制代理逻辑（使用测试成功的代理）
        if local_ip.startswith("192.168."):
            # 本地网络，强制使用代理
            self.proxy_url = "http://127.0.0.1:7897"  # 使用测试成功的代理
            self.use_proxy = True
            self.log.info(f"强制代理模式: {self.proxy_url} (本地网络: {local_ip})")
            
            # 设置session代理
            self.session.proxies.update({
                'http': self.proxy_url,
                'https': self.proxy_url
            })
        else:
            # 公网IP，强制直连
            self.proxy_url = None
            self.use_proxy = False
            self.log.info(f"强制直连模式 (公网IP: {local_ip})")

        # 测试连接（改进版）
        if self.use_proxy:
            try:
                # 使用session测试连接
                test_response = self.session.get("https://httpbin.org/ip", timeout=10)
                if test_response.status_code == 200:
                    self.log.info("代理连接测试成功")
                else:
                    self.log.warning("代理连接测试失败，尝试直连模式")
                    self.use_proxy = False
                    self.session.proxies = {}
            except Exception as e:
                self.log.warning(f"代理连接测试失败: {e}")
                self.log.warning("代理连接测试失败，尝试直连模式")
                self.use_proxy = False
                self.session.proxies = {}

    def _test_proxy_connection(self):
        """测试代理连接是否可用"""
        try:
            # 测试连接
            test_url = "https://httpbin.org/ip"
            if hasattr(self, 'use_proxy') and self.use_proxy:
                response = requests.get(test_url, proxies={"http": self.proxy_url, "https": self.proxy_url}, timeout=5, verify=self.verify_ssl)
            else:
                response = requests.get(test_url, timeout=5, verify=self.verify_ssl)
            
            return response.status_code == 200
        except Exception as e:
            self.log.warning(f"代理连接测试失败: {e}")
            return False
        
    def _sign_request(self, params: Dict[str, Any]) -> str:
        """生成签名"""
        query_string = urllib.parse.urlencode(params)
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _sync_server_time(self):
        """同步服务器时间，校正本地时间戳偏差（修复版）"""
        for attempt in range(self.sync_retry_count):
            try:
                # 记录请求开始时间
                request_start = time.time() * 1000
                
                # 获取服务器时间（使用session，已配置代理）
                response = self.session.get(
                    f"{self.base_url}/fapi/v1/time", 
                    verify=self.verify_ssl, 
                    timeout=15  # 增加超时时间
                )
                
                # 记录请求结束时间
                request_end = time.time() * 1000
                
                if response.status_code == 200:
                    server_time = response.json()['serverTime']
                    
                    # 计算网络延迟并校正服务器时间
                    network_delay = request_end - request_start
                    adjusted_server_time = server_time + (network_delay / 2)
                    
                    # 计算时间偏差
                    local_time = request_end
                    self.time_offset = adjusted_server_time - local_time
                    self.last_time_sync = time.time()
                    
                    if abs(self.time_offset) > 1000:  # 偏差超过1秒时记录警告
                        self.log.warning(f"时间戳偏差较大: {self.time_offset:.0f}ms，网络延迟: {network_delay:.0f}ms，已自动校正")
                    else:
                        self.log.info(f"服务器时间同步完成，偏差: {self.time_offset:.0f}ms，网络延迟: {network_delay:.0f}ms")
                    return  # 同步成功，退出重试循环
                else:
                    self.log.warning(f"获取服务器时间失败 (尝试 {attempt+1}/{self.sync_retry_count}): {response.status_code}")
                    
            except Exception as e:
                self.log.warning(f"同步服务器时间异常 (尝试 {attempt+1}/{self.sync_retry_count}): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.sync_retry_count - 1:
                time.sleep(1)
        
        # 所有重试都失败，使用保守的时间戳策略
        self.log.error("服务器时间同步失败，将使用保守的时间戳策略")
        self.time_offset = 1000  # 1秒的安全边距
        self.last_time_sync = time.time()
    
    def _get_corrected_timestamp(self) -> int:
        """获取校正后的时间戳（增强版，包含安全边距）"""
        try:
            # 定期重新同步服务器时间
            if time.time() - self.last_time_sync > self.time_sync_interval:
                self._sync_server_time()
            
            # 确保time_offset是有效数值
            if not isinstance(self.time_offset, (int, float)) or self.time_offset != self.time_offset:  # 检查NaN
                self.log.warning("time_offset无效，重置为0")
                self.time_offset = 0
            
            # 确保safety_margin是有效数值
            if not isinstance(self.safety_margin, (int, float)) or self.safety_margin < 0:
                self.log.warning("safety_margin无效，重置为500")
                self.safety_margin = 500
            
            # 获取当前时间戳
            current_time = int(time.time() * 1000)
            
            # 返回校正后的时间戳，减去安全边距
            corrected_timestamp = current_time + int(self.time_offset) - int(self.safety_margin)
            
            # 确保时间戳不会是负数或过小
            min_timestamp = current_time - 60000  # 最多比当前时间早1分钟
            max_timestamp = current_time + 5000   # 最多比当前时间晚5秒
            
            result = max(min_timestamp, min(corrected_timestamp, max_timestamp))
            
            # 验证结果是否为有效的正整数
            if not isinstance(result, int) or result <= 0:
                self.log.error(f"生成的时间戳无效: {result}，使用当前时间")
                result = current_time - 1000  # 使用当前时间减1秒作为安全值
            
            return result
            
        except Exception as e:
            self.log.error(f"获取时间戳异常: {e}，使用当前时间")
            return int(time.time() * 1000) - 1000  # 返回当前时间减1秒作为安全值
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """发送GET请求（带重试机制）"""
        url = self.base_url + endpoint
        params = params or {}
        
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 每次重试都生成新的时间戳，避免时间戳过期
                timestamp = self._get_corrected_timestamp()
                
                # 验证timestamp是否有效
                if not isinstance(timestamp, int) or timestamp <= 0:
                    self.log.error(f"生成的timestamp无效: {timestamp}")
                    timestamp = int(time.time() * 1000) - 1000
                
                params.update({
                    'timestamp': timestamp,
                    'recvWindow': 60000  # 增加到60秒，最大允许值
                })
                
                # 验证关键参数
                if 'timestamp' not in params or params['timestamp'] is None:
                    self.log.error("timestamp参数缺失，强制添加")
                    params['timestamp'] = int(time.time() * 1000) - 1000
                
                params['signature'] = self._sign_request(params)
                
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }
                
                # 设置代理
                if hasattr(self, 'use_proxy') and self.use_proxy:
                    proxies = {"http": self.proxy_url, "https": self.proxy_url}
                else:
                    proxies = {}
                
                response = self.session.get(url, params=params, headers=headers, 
                                           proxies=proxies, verify=self.verify_ssl, timeout=15)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    # 记录详细的错误响应内容
                    try:
                        error_response = response.text
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code} - 响应内容: {error_response}")
                        
                        # 如果是时间戳错误，立即重新同步服务器时间
                        if "1021" in error_response or "ahead of the server's time" in error_response:
                            self.log.info("检测到时间戳错误，立即重新同步服务器时间")
                            self._sync_server_time()
                            # 强制使用更保守的时间戳
                            self.safety_margin = min(self.safety_margin + 200, 2000)  # 增加安全边距，最多2秒
                        
                        # 如果是timestamp参数错误，记录详细信息
                        if "1102" in error_response or "timestamp" in error_response.lower():
                            self.log.error(f"timestamp参数错误，当前参数: {params}")
                            
                    except:
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                    
            except requests.exceptions.Timeout:
                self.log.warning(f"请求超时 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except requests.exceptions.ConnectionError:
                self.log.warning(f"连接错误 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                self.log.warning(f"请求异常 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
        
        self.log.error(f"所有重试失败: {url}")
        return None
    
    def post(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """发送POST请求（带重试机制）"""
        url = self.base_url + endpoint
        params = params or {}
        
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 每次重试都生成新的时间戳，避免时间戳过期
                timestamp = self._get_corrected_timestamp()
                
                # 验证timestamp是否有效
                if not isinstance(timestamp, int) or timestamp <= 0:
                    self.log.error(f"生成的timestamp无效: {timestamp}")
                    timestamp = int(time.time() * 1000) - 1000
                
                params.update({
                    'timestamp': timestamp,
                    'recvWindow': 60000  # 增加到60秒，最大允许值
                })
                
                # 验证关键参数
                if 'timestamp' not in params or params['timestamp'] is None:
                    self.log.error("timestamp参数缺失，强制添加")
                    params['timestamp'] = int(time.time() * 1000) - 1000
                
                params['signature'] = self._sign_request(params)
                
                headers = {
                    'X-MBX-APIKEY': self.api_key,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
                
                # 设置代理
                if hasattr(self, 'use_proxy') and self.use_proxy:
                    proxies = {"http": self.proxy_url, "https": self.proxy_url}
                else:
                    proxies = {}
                
                response = self.session.post(url, data=params, headers=headers, 
                                            proxies=proxies, verify=self.verify_ssl, timeout=15)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    # 记录详细的错误响应内容
                    try:
                        error_response = response.text
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code} - 响应内容: {error_response}")
                        
                        # 如果是时间戳错误，立即重新同步服务器时间
                        if "1021" in error_response or "ahead of the server's time" in error_response:
                            self.log.info("检测到时间戳错误，立即重新同步服务器时间")
                            self._sync_server_time()
                            # 强制使用更保守的时间戳
                            self.safety_margin = min(self.safety_margin + 200, 2000)  # 增加安全边距，最多2秒
                        
                        # 如果是timestamp参数错误，记录详细信息
                        if "1102" in error_response or "timestamp" in error_response.lower():
                            self.log.error(f"timestamp参数错误，当前参数: {params}")
                            
                    except:
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                    
            except requests.exceptions.Timeout:
                self.log.warning(f"请求超时 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except requests.exceptions.ConnectionError:
                self.log.warning(f"连接错误 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                self.log.warning(f"请求异常 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
        
        self.log.error(f"所有重试失败: {url}")
        return None
    
    def delete(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """发送DELETE请求（带重试机制）"""
        url = self.base_url + endpoint
        params = params or {}
        
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 每次重试都生成新的时间戳，避免时间戳过期
                params.update({
                    'timestamp': self._get_corrected_timestamp(),
                    'recvWindow': 60000  # 增加到60秒，最大允许值
                })
                params['signature'] = self._sign_request(params)
                
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }
                
                # 设置代理
                if hasattr(self, 'use_proxy') and self.use_proxy:
                    proxies = {"http": self.proxy_url, "https": self.proxy_url}
                else:
                    proxies = {}
                
                response = self.session.delete(url, params=params, headers=headers, 
                                               proxies=proxies, verify=self.verify_ssl, timeout=15)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    # 记录详细的错误响应内容
                    try:
                        error_response = response.text
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code} - 响应内容: {error_response}")
                        
                        # 如果是时间戳错误，立即重新同步服务器时间
                        if "1021" in error_response or "ahead of the server's time" in error_response:
                            self.log.info("检测到时间戳错误，立即重新同步服务器时间")
                            self._sync_server_time()
                            # 强制使用更保守的时间戳
                            self.safety_margin = min(self.safety_margin + 200, 2000)  # 增加安全边距，最多2秒
                            
                    except:
                        self.log.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {url} - 状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                    
            except requests.exceptions.Timeout:
                self.log.warning(f"请求超时 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except requests.exceptions.ConnectionError:
                self.log.warning(f"连接错误 (尝试 {attempt+1}/{max_retries}): {url}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                self.log.warning(f"请求异常 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
        
        self.log.error(f"所有重试失败: {url}")
        return None
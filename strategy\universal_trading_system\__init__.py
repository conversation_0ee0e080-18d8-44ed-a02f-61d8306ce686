"""
通用交易系统 - Universal Trading System
Universal Trading Solution for 500+ Cryptocurrency Pairs

这是一个专为500+加密货币交易对设计的通用交易解决方案，具备以下核心特性：

核心特性：
1. 🎯 智能币种分类系统 - 根据市值、流动性、波动性自动分类
2. ⚙️ 动态参数配置矩阵 - 每个分类都有专门的交易参数
3. ✅ 统一验证框架 - 标准化的价格、保证金、规则验证
4. 🛡️ 自适应风控系统 - 基于实时表现动态调整风控参数
5. 📊 性能监控反馈 - 实时监控并自动优化交易策略

系统架构：
- classifier/: 币种智能分类系统
- config/: 动态参数配置矩阵
- validation/: 统一验证框架
- risk/: 自适应风控系统
- monitor/: 性能监控反馈机制
- core/: 通用交易引擎核心

使用方式：
```python
from universal_trading_system import UniversalTradingEngine, OrderRequest

# 初始化交易引擎
engine = UniversalTradingEngine()
await engine.start_engine()

# 创建订单（支持任意币对）
order = OrderRequest(
    symbol='BTCUSDT',  # 或任何其他币对
    side='BUY',
    quantity=0.001
)

# 提交订单（自动分类、验证、风控、监控）
response = await engine.submit_order(order)
```

设计优势：
✨ 广泛适用性 - 支持500+币对，无需针对特定币种编程
🚀 高度自动化 - 自动分类、参数配置、风控调整
🔒 风险可控 - 多层风控机制，实时监控异常
📈 持续优化 - 基于历史表现自动优化参数

作者: Universal Trading System Team
版本: 1.0.0
"""

# 核心模块导入
from .core.universal_trading_engine import (
    UniversalTradingEngine,
    OrderRequest,
    OrderResponse,
    OrderStatus
)

# 分类系统
from .classifier.symbol_classifier import (
    SymbolClassifier,
    SymbolTier,
    SymbolMetrics
)

# 参数配置
from .config.parameter_matrix import (
    ParameterMatrix,
    TradingParameters
)

# 验证框架
from .validation.unified_validator import (
    UnifiedValidator,
    ValidationResult,
    OrderValidationContext
)

# 风控系统
from .risk.adaptive_risk_manager import (
    AdaptiveRiskManager,
    RiskLevel,
    RiskAction,
    RiskMetrics
)

# 监控系统
from .monitor.performance_monitor import (
    PerformanceMonitor,
    PerformanceGrade,
    TradeRecord,
    SymbolPerformance
)

# 版本信息
__version__ = "1.0.0"
__author__ = "Universal Trading System Team"
__description__ = "Universal Trading Solution for 500+ Cryptocurrency Pairs"

# 导出的主要类
__all__ = [
    # 核心引擎
    'UniversalTradingEngine',
    'OrderRequest',
    'OrderResponse',
    'OrderStatus',
    
    # 分类系统
    'SymbolClassifier',
    'SymbolTier',
    'SymbolMetrics',
    
    # 参数配置
    'ParameterMatrix',
    'TradingParameters',
    
    # 验证框架
    'UnifiedValidator',
    'ValidationResult',
    'OrderValidationContext',
    
    # 风控系统
    'AdaptiveRiskManager',
    'RiskLevel',
    'RiskAction',
    'RiskMetrics',
    
    # 监控系统
    'PerformanceMonitor',
    'PerformanceGrade',
    'TradeRecord',
    'SymbolPerformance',
]

# 快速开始示例
def quick_start_example():
    """
    快速开始示例
    演示如何使用通用交易系统
    """
    example_code = '''
import asyncio
from universal_trading_system import UniversalTradingEngine, OrderRequest

async def main():
    # 1. 初始化交易引擎
    engine = UniversalTradingEngine()
    
    # 2. 启动引擎
    await engine.start_engine()
    
    # 3. 创建订单请求（支持任意币对）
    orders = [
        OrderRequest(symbol='BTCUSDT', side='BUY', quantity=0.001),
        OrderRequest(symbol='ETHUSDT', side='BUY', quantity=0.01),
        OrderRequest(symbol='ADAUSDT', side='BUY', quantity=100),
        # ... 可以是任何币对
    ]
    
    # 4. 批量提交订单
    for order in orders:
        response = await engine.submit_order(order)
        print(f"{order.symbol}: {response.message}")
    
    # 5. 查看引擎状态
    status = engine.get_engine_status()
    print(f"处理订单: {status['total_orders_processed']}")
    print(f"成功率: {status['success_rate']:.2%}")
    
    # 6. 停止引擎
    await engine.stop_engine()

# 运行示例
asyncio.run(main())
    '''
    return example_code

# 系统信息
def get_system_info():
    """获取系统信息"""
    return {
        'name': 'Universal Trading System',
        'version': __version__,
        'description': __description__,
        'author': __author__,
        'features': [
            '智能币种分类系统',
            '动态参数配置矩阵', 
            '统一验证框架',
            '自适应风控系统',
            '性能监控反馈机制'
        ],
        'supported_pairs': '500+',
        'architecture': [
            'classifier - 币种分类',
            'config - 参数配置',
            'validation - 订单验证',
            'risk - 风险控制',
            'monitor - 性能监控',
            'core - 交易引擎'
        ]
    }

if __name__ == "__main__":
    # 显示系统信息
    info = get_system_info()
    print(f"=== {info['name']} v{info['version']} ===")
    print(f"描述: {info['description']}")
    print(f"支持币对: {info['supported_pairs']}")
    print("\n核心特性:")
    for feature in info['features']:
        print(f"  ✓ {feature}")
    
    print("\n快速开始示例:")
    print(quick_start_example())
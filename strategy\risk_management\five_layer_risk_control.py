"""
五重风控机制核心代码模块
Five-Layer Risk Control System Core Module

提取自 MakerChannelEnhanced 策略的风控精华
包含：保本止损、移动止损、降档复位、Top3筛选、止血贴

作者：基于 maker_channel_enhanced.py 提取
版本：1.0
日期：2024-10-09
"""

import time
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class RiskConfig:
    """风控配置参数"""
    # 保本止损配置
    breakeven_trigger_pct: float = 0.05  # 5%浮盈触发保本
    breakeven_stop_pct: float = 0.004    # 保本止损设在成本+0.4%
    
    # 阶梯式移动止损配置
    # 常规行情模式（默认）
    trailing_profit_step_normal: float = 0.10    # 常规模式盈利步长（10%）
    trailing_stop_ratio_normal: float = 0.70     # 常规模式止损比例（保留70%利润）

    # 极端行情模式（高波动环境）
    trailing_profit_step_extreme: float = 0.05   # 极端模式盈利步长（5%）
    trailing_stop_ratio_extreme: float = 0.80    # 极端模式止损比例（保留80%利润）

    # 极端行情判定标准
    extreme_market_atr_multiplier: float = 2.5   # ATR > 标准差 × 2.5
    extreme_market_daily_gain: float = 0.15      # 单日涨幅 > 15%

    # 兼容性参数（保留原有接口）
    trailing_trigger_pct: float = 0.05   # 最小启用阈值（5%浮盈）
    trailing_stop_pct: float = 0.01      # 已废弃，保留兼容性
    
    # 降档复位配置
    downgrade_ratio: float = 0.5         # 降档比例50%
    atr_period: int = 14                 # ATR计算周期
    min_period: int = 20                 # 最低价计算周期
    std_period: int = 60                 # 标准差计算周期
    depth_downgrade_threshold: int = 80000    # 降档深度阈值
    depth_reset_threshold: int = 200000       # 复位深度阈值
    atr_volatility_multiplier: float = 2.0   # ATR波动倍数
    atr_reset_multiplier: float = 1.5        # ATR复位倍数
    
    # 止血贴配置
    max_stops_per_period: int = 2        # 时间窗口内最大止损次数
    freeze_hours: int = 4                # 冷冻时间（小时）
    
    # Top3筛选配置
    refresh_interval_minutes: int = 60   # Top1刷新间隔（分钟）
    score_threshold: float = 7.0         # 评分门槛


class FiveLayerRiskControl:
    """
    五重风控系统核心类
    
    功能模块：
    1. 保本止损 - 确保盈利单不变亏损单
    2. 移动止损 - 锁定利润防止大幅回撤  
    3. 降档复位 - 动态调整仓位应对波动
    4. Top3筛选 - 只交易最优质币种
    5. 止血贴 - 防止频繁止损过度亏损
    """
    
    def __init__(self, config: RiskConfig = None, logger: logging.Logger = None):
        self.config = config or RiskConfig()
        self.log = logger or logging.getLogger(__name__)
        
        # 止血贴缓存：{symbol: {'count': int, 'ts': float}}
        self.stop_cache: Dict[str, Dict] = {}
        
        # 当前持仓信息
        self.position: Optional[Dict] = None
        
        # Top1选择缓存
        self.selected_symbol: Optional[str] = None
        self.last_refresh_time: Optional[float] = None
        
        self.log.info("五重风控系统初始化完成")

    # ==================== 1. 保本止损模块 ====================
    
    def check_breakeven_stop(self, symbol: str, current_price: float) -> Tuple[bool, Optional[float]]:
        """
        检查是否需要设置保本止损
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
            
        Returns:
            (是否需要设置, 新的止损价格)
        """
        if not self.position:
            return False, None
            
        entry_price = self.position['entry']
        profit_pct = (current_price - entry_price) / entry_price
        
        # 浮盈达到5%且未设置保本止损
        if (profit_pct >= self.config.breakeven_trigger_pct and 
            self.position.get('breakeven', 0) == 0):
            
            new_stop_price = entry_price * (1 + self.config.breakeven_stop_pct)
            self.position['breakeven'] = 1
            
            self.log.info(f"{symbol} 保本止损触发: 浮盈{profit_pct:.2%} → 止损价{new_stop_price:.6f}")
            return True, new_stop_price
            
        return False, None

    # ==================== 2. 阶梯式移动止损模块 ====================

    def detect_extreme_market(self, symbol: str, current_price: float, market_data: Dict) -> bool:
        """
        检测是否为极端行情环境

        Args:
            symbol: 交易对符号
            current_price: 当前价格
            market_data: 市场数据

        Returns:
            是否为极端行情
        """
        try:
            # 条件1：ATR > 60日标准差 × 2.5
            atr = market_data.get('atr', 0)
            close_std = market_data.get('close_std', 0)

            atr_extreme = False
            if close_std > 0:
                atr_extreme = atr > close_std * self.config.extreme_market_atr_multiplier

            # 条件2：单日涨幅 > 15%
            daily_gain_extreme = False
            if self.position:
                entry_price = self.position['entry']
                daily_gain = (current_price - entry_price) / entry_price
                daily_gain_extreme = daily_gain > self.config.extreme_market_daily_gain

            # 满足任一条件即为极端行情
            is_extreme = atr_extreme or daily_gain_extreme

            if is_extreme:
                reason = []
                if atr_extreme:
                    reason.append(f"ATR({atr:.2f}) > 标准差×{self.config.extreme_market_atr_multiplier}")
                if daily_gain_extreme:
                    reason.append(f"单日涨幅({daily_gain:.1%}) > {self.config.extreme_market_daily_gain:.1%}")

                self.log.info(f"{symbol} 检测到极端行情: {', '.join(reason)}")

            return is_extreme

        except Exception as e:
            self.log.error(f"{symbol} 极端行情检测失败: {e}")
            return False
    
    def check_trailing_stop(self, symbol: str, current_price: float, market_data: Dict = None) -> Tuple[bool, Optional[float]]:
        """
        阶梯式移动止损检查

        Args:
            symbol: 交易对符号
            current_price: 当前价格
            market_data: 市场数据（用于极端行情检测）

        Returns:
            (是否需要更新, 新的止损价格)
        """
        if not self.position:
            return False, None

        entry_price = self.position['entry']
        profit_pct = (current_price - entry_price) / entry_price

        # 最小启用阈值检查
        if profit_pct < self.config.trailing_trigger_pct:
            return False, None

        # 检测市场模式
        is_extreme_market = False
        if market_data:
            is_extreme_market = self.detect_extreme_market(symbol, current_price, market_data)

        # 根据市场模式选择参数
        if is_extreme_market:
            profit_step = self.config.trailing_profit_step_extreme
            stop_ratio = self.config.trailing_stop_ratio_extreme
            mode_name = "极端模式"
        else:
            profit_step = self.config.trailing_profit_step_normal
            stop_ratio = self.config.trailing_stop_ratio_normal
            mode_name = "常规模式"

        # 计算当前应该达到的阶梯级别
        current_level = int(profit_pct / profit_step)

        # 如果还没有达到第一个阶梯，不启用移动止损
        if current_level < 1:
            return False, None

        # 计算阶梯式止损价格
        # 每个阶梯保留该阶梯利润的指定比例
        protected_profit_pct = current_level * profit_step * stop_ratio
        new_stop_price = entry_price * (1 + protected_profit_pct)

        # 获取当前止损价格
        current_stop = self.position.get('trail_stop', 0)

        # 只有新止损价更高时才更新（向上移动）
        if new_stop_price > current_stop:
            # 更新持仓信息
            self.position['trail_stop'] = new_stop_price
            self.position['trailing_level'] = current_level
            self.position['trailing_mode'] = mode_name

            # 计算保护的利润比例
            protected_gain_pct = (new_stop_price - entry_price) / entry_price

            self.log.info(
                f"{symbol} 阶梯式移动止损更新 [{mode_name}]:\n"
                f"  当前盈利: {profit_pct:.1%} (第{current_level}阶梯)\n"
                f"  止损价格: {current_stop:.6f} → {new_stop_price:.6f}\n"
                f"  保护利润: {protected_gain_pct:.1%} (保留{stop_ratio:.0%})"
            )

            return True, new_stop_price

        return False, None

    # ==================== 3. 降档复位模块 ====================
    
    def check_position_scaling(self, symbol: str, current_price: float, 
                             market_data: Dict) -> Tuple[str, Optional[float]]:
        """
        检查是否需要降档或复位
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
            market_data: 市场数据 {'atr': float, 'low_min': float, 'high_max': float, 
                                'close_std': float, 'depth': float}
            
        Returns:
            ('none'|'downgrade'|'reset', 操作数量)
        """
        if not self.position:
            return 'none', None
            
        atr = market_data.get('atr', 0)
        low_min = market_data.get('low_min', 0)  
        high_max = market_data.get('high_max', 0)
        close_std = market_data.get('close_std', 0)
        depth = market_data.get('depth', 0)
        
        current_qty = self.position['qty']
        
        # 降档条件检查
        downgrade_conditions = [
            current_price < low_min - atr,  # 跌破支撑
            depth < self.config.depth_downgrade_threshold,  # 深度不足
            atr > close_std * self.config.atr_volatility_multiplier  # 波动过大
        ]
        
        if any(downgrade_conditions) and self.position.get('exit_pct', 0) == 0:
            exit_size = current_qty * self.config.downgrade_ratio
            self.position['exit_pct'] = self.config.downgrade_ratio
            self.log.info(f"{symbol} 降档触发: 减仓{self.config.downgrade_ratio:.0%}")
            return 'downgrade', exit_size
            
        # 复位条件检查  
        reset_conditions = [
            current_price > high_max,  # 突破阻力
            depth > self.config.depth_reset_threshold,  # 深度充足
            atr < close_std * self.config.atr_reset_multiplier  # 波动缓解
        ]
        
        if (all(reset_conditions) and self.position.get('exit_pct', 0) > 0):
            # 计算复位数量
            original_nominal = self.position.get('original_nominal', 0)
            exit_pct = self.position['exit_pct']
            reset_size = original_nominal * exit_pct / current_price
            self.position['exit_pct'] = 0
            self.log.info(f"{symbol} 复位触发: 买回{exit_pct:.0%}仓位")
            return 'reset', reset_size
            
        return 'none', None

    # ==================== 4. Top3筛选模块 ====================
    
    def should_refresh_selection(self) -> bool:
        """检查是否需要刷新Top1选择"""
        if self.last_refresh_time is None:
            return True
            
        elapsed_minutes = (time.time() - self.last_refresh_time) / 60
        return elapsed_minutes >= self.config.refresh_interval_minutes
    
    def update_top_selection(self, symbol: str, score: float) -> bool:
        """
        更新Top1选择
        
        Args:
            symbol: 币种符号
            score: 综合评分
            
        Returns:
            是否发生了变化
        """
        if score < self.config.score_threshold:
            self.log.info(f"评分{score:.2f}低于门槛{self.config.score_threshold}")
            return False
            
        changed = self.selected_symbol != symbol
        if changed:
            old_symbol = self.selected_symbol
            self.selected_symbol = symbol
            self.last_refresh_time = time.time()
            self.log.info(f"Top1选择更新: {old_symbol} → {symbol} (评分:{score:.2f})")
            
        return changed

    # ==================== 5. 止血贴模块 ====================
    
    def log_stop_loss(self, symbol: str, stop_type: str) -> None:
        """
        记录止损事件
        
        Args:
            symbol: 交易对符号
            stop_type: 止损类型 ('breakeven', 'trailing', 'manual', etc.)
        """
        now = time.time()
        record = self.stop_cache.setdefault(symbol, {'count': 0, 'ts': now})
        
        # 如果在冷冻期内，累加计数；否则重置
        if now - record['ts'] < self.config.freeze_hours * 3600:
            record['count'] += 1
        else:
            record.update({'count': 1, 'ts': now})
            
        self.log.info(f"{symbol} {stop_type}止损 累计{record['count']}次")
        
        # 检查是否需要冷冻
        if record['count'] >= self.config.max_stops_per_period:
            freeze_until = now + self.config.freeze_hours * 3600
            self.log.warning(f"{symbol} 触发止血贴: {self.config.freeze_hours}小时内{record['count']}次止损，冷冻至{time.ctime(freeze_until)}")
    
    def can_open_position(self, symbol: str) -> bool:
        """
        检查是否允许开仓（止血贴检查）
        
        Args:
            symbol: 交易对符号
            
        Returns:
            是否允许开仓
        """
        record = self.stop_cache.get(symbol)
        if not record:
            return True
            
        # 检查是否在冷冻期且超过止损限制
        now = time.time()
        in_freeze_period = now - record['ts'] < self.config.freeze_hours * 3600
        exceeded_limit = record['count'] >= self.config.max_stops_per_period
        
        if in_freeze_period and exceeded_limit:
            remaining_hours = (record['ts'] + self.config.freeze_hours * 3600 - now) / 3600
            self.log.warning(f"{symbol} 仍在冷冻期，剩余{remaining_hours:.1f}小时")
            return False
            
        return True

    # ==================== 统一风控接口 ====================
    
    def monitor_position(self, symbol: str, current_price: float, market_data: Dict) -> Dict[str, Any]:
        """
        统一持仓监控接口
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格  
            market_data: 市场数据
            
        Returns:
            风控决策结果
        """
        if not self.position:
            return {'action': 'none'}
            
        result = {'action': 'none', 'details': []}
        
        # 1. 检查保本止损
        breakeven_needed, breakeven_price = self.check_breakeven_stop(symbol, current_price)
        if breakeven_needed:
            result['details'].append({
                'type': 'breakeven',
                'stop_price': breakeven_price
            })
        
        # 2. 检查阶梯式移动止损
        trailing_needed, trailing_price = self.check_trailing_stop(symbol, current_price, market_data)
        if trailing_needed:
            result['details'].append({
                'type': 'trailing',
                'stop_price': trailing_price,
                'trailing_level': self.position.get('trailing_level', 0),
                'trailing_mode': self.position.get('trailing_mode', '常规模式')
            })
        
        # 3. 检查降档复位
        scaling_action, scaling_qty = self.check_position_scaling(symbol, current_price, market_data)
        if scaling_action != 'none':
            result['details'].append({
                'type': 'scaling',
                'action': scaling_action,
                'quantity': scaling_qty
            })
        
        # 4. 检查止损触发
        stop_price = max(
            self.position.get('stop', 0),
            self.position.get('trail_stop', 0)
        )
        
        if current_price <= stop_price and stop_price > 0:
            result['action'] = 'stop_loss'
            result['stop_price'] = stop_price
            result['stop_type'] = 'breakeven/trail'
            
        return result
    
    def set_position(self, symbol: str, entry_price: float, quantity: float, nominal_value: float):
        """设置当前持仓信息"""
        self.position = {
            'symbol': symbol,
            'entry': entry_price,
            'qty': quantity,
            'original_nominal': nominal_value,
            'breakeven': 0,
            'trail_stop': 0,
            'stop': entry_price * 0.97,  # 默认3%止损
            'exit_pct': 0,
            # 阶梯式移动止损新增字段
            'trailing_level': 0,         # 当前阶梯级别
            'trailing_mode': '常规模式'   # 移动止损模式
        }
        self.log.info(f"设置持仓: {symbol} @{entry_price:.6f} 数量:{quantity:.6f} (阶梯式移动止损已启用)")
    
    def clear_position(self):
        """清除持仓信息"""
        if self.position:
            symbol = self.position['symbol']
            self.log.info(f"清除持仓: {symbol}")
        self.position = None


# ==================== 使用示例 ====================

def example_usage():
    """使用示例"""
    import logging
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建风控实例
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%最小启用阈值
        trailing_profit_step_normal=0.10,     # 常规模式：每10%盈利一个阶梯
        trailing_stop_ratio_normal=0.70,      # 常规模式：保留70%利润
        trailing_profit_step_extreme=0.05,    # 极端模式：每5%盈利一个阶梯
        trailing_stop_ratio_extreme=0.80,     # 极端模式：保留80%利润
        max_stops_per_period=2,               # 4小时内最多2次止损
        freeze_hours=4                        # 冷冻4小时
    )
    
    risk_control = FiveLayerRiskControl(config, logger)
    
    # 设置持仓
    risk_control.set_position('BTCUSDT', 50000.0, 0.1, 5000.0)
    
    # 模拟价格变化和风控监控
    market_data = {
        'atr': 500.0,
        'low_min': 49000.0,
        'high_max': 51000.0, 
        'close_std': 300.0,
        'depth': 150000.0
    }
    
    # 价格上涨到52500（5%盈利）
    result = risk_control.monitor_position('BTCUSDT', 52500.0, market_data)
    print(f"监控结果: {result}")
    
    # 检查是否可以开新仓
    can_open = risk_control.can_open_position('BTCUSDT')
    print(f"可以开仓: {can_open}")


# ==================== 辅助工具函数 ====================

class MarketDataCalculator:
    """市场数据计算工具类"""

    @staticmethod
    def calculate_atr(high_series, low_series, close_series, period: int = 14):
        """计算平均真实波幅(ATR)"""
        import pandas as pd

        tr = pd.DataFrame({
            'hl': high_series - low_series,
            'hc': abs(high_series - close_series.shift(1)),
            'lc': abs(low_series - close_series.shift(1))
        }).max(axis=1)

        return tr.rolling(window=period).mean()

    @staticmethod
    def prepare_market_data(df, atr_period: int = 14, min_period: int = 20, std_period: int = 60):
        """
        准备市场数据用于风控计算

        Args:
            df: K线数据DataFrame，包含 'h', 'l', 'c' 列
            atr_period: ATR计算周期
            min_period: 最低价计算周期
            std_period: 标准差计算周期

        Returns:
            Dict: 包含计算结果的字典
        """
        if len(df) < max(atr_period, min_period, std_period):
            return None

        # 计算ATR
        atr_series = MarketDataCalculator.calculate_atr(
            df['h'], df['l'], df['c'], atr_period
        )

        # 计算其他指标
        low_min_series = df['l'].rolling(min_period).min()
        high_max_series = df['h'].rolling(min_period).max()
        close_std_series = df['c'].rolling(std_period).std()

        # 返回最新值
        return {
            'atr': atr_series.iloc[-1] if len(atr_series.dropna()) > 0 else 0,
            'low_min': low_min_series.iloc[-1] if len(low_min_series.dropna()) > 0 else 0,
            'high_max': high_max_series.iloc[-1] if len(high_max_series.dropna()) > 0 else 0,
            'close_std': close_std_series.iloc[-1] if len(close_std_series.dropna()) > 0 else 0
        }


class RiskControlIntegrator:
    """风控系统集成器 - 用于现有策略的快速集成"""

    def __init__(self, existing_strategy, risk_config: RiskConfig = None):
        """
        初始化集成器

        Args:
            existing_strategy: 现有策略实例
            risk_config: 风控配置
        """
        self.strategy = existing_strategy
        self.risk_control = FiveLayerRiskControl(risk_config, existing_strategy.log)

        # 保存原始方法的引用
        self.original_monitor = getattr(existing_strategy, 'monitor_position', None)
        self.original_can_open = getattr(existing_strategy, 'can_open', None)

        # 替换策略的方法
        existing_strategy.monitor_position = self._enhanced_monitor_position
        existing_strategy.can_open = self._enhanced_can_open
        existing_strategy.log_stop = self._enhanced_log_stop

        self.strategy.log.info("五重风控系统已集成到现有策略")

    def _enhanced_monitor_position(self, symbol: str, current_price: float):
        """增强的持仓监控方法"""
        # 准备市场数据
        try:
            df = self.strategy.get_klines(symbol, '15m', 100)
            if df is not None and len(df) >= 60:
                market_data = MarketDataCalculator.prepare_market_data(df)
                if market_data:
                    # 获取深度数据
                    market_data['depth'] = self.strategy.get_depth01pct(symbol)

                    # 执行五重风控监控
                    result = self.risk_control.monitor_position(symbol, current_price, market_data)

                    # 处理风控决策
                    self._handle_risk_decisions(symbol, current_price, result)
        except Exception as e:
            self.strategy.log.error(f"风控监控失败 {symbol}: {e}")

        # 调用原始监控方法（如果存在）
        if self.original_monitor:
            try:
                self.original_monitor(symbol, current_price)
            except Exception as e:
                self.strategy.log.error(f"原始监控方法失败: {e}")

    def _enhanced_can_open(self, symbol: str) -> bool:
        """增强的开仓检查方法"""
        # 先检查止血贴
        if not self.risk_control.can_open_position(symbol):
            return False

        # 调用原始检查方法（如果存在）
        if self.original_can_open:
            return self.original_can_open(symbol)

        return True

    def _enhanced_log_stop(self, symbol: str, stop_type: str):
        """增强的止损记录方法"""
        self.risk_control.log_stop_loss(symbol, stop_type)

    def _handle_risk_decisions(self, symbol: str, current_price: float, result: Dict[str, Any]):
        """处理风控决策"""
        if result['action'] == 'stop_loss':
            # 触发止损
            self.strategy.close_position(symbol, current_price)
            self.risk_control.log_stop_loss(symbol, result['stop_type'])
            self.risk_control.clear_position()
            return

        # 处理其他风控动作
        for detail in result.get('details', []):
            if detail['type'] == 'breakeven':
                self._update_stop_loss(symbol, detail['stop_price'])
            elif detail['type'] == 'trailing':
                self._update_stop_loss(symbol, detail['stop_price'])
            elif detail['type'] == 'scaling':
                self._handle_position_scaling(symbol, current_price, detail)

    def _update_stop_loss(self, symbol: str, stop_price: float):
        """更新止损价格"""
        if hasattr(self.strategy, 'update_stop_loss'):
            self.strategy.update_stop_loss(symbol, stop_price)
        elif hasattr(self.strategy, 'pos') and self.strategy.pos:
            self.strategy.pos['stop'] = stop_price

    def _handle_position_scaling(self, symbol: str, current_price: float, detail: Dict):
        """处理仓位缩放"""
        action = detail['action']
        quantity = detail['quantity']

        if action == 'downgrade':
            # 降档：卖出部分仓位
            if hasattr(self.strategy, 'place_maker_order'):
                self.strategy.place_maker_order(symbol, 'SELL', quantity, current_price * 0.999)
        elif action == 'reset':
            # 复位：买回仓位
            if hasattr(self.strategy, 'place_maker_order'):
                self.strategy.place_maker_order(symbol, 'BUY', quantity, current_price * 1.001)

    def set_position(self, symbol: str, entry_price: float, quantity: float, nominal_value: float):
        """设置持仓信息"""
        self.risk_control.set_position(symbol, entry_price, quantity, nominal_value)

    def clear_position(self):
        """清除持仓信息"""
        self.risk_control.clear_position()


# ==================== 快速集成示例 ====================

def integrate_with_existing_strategy(strategy_instance):
    """
    快速集成示例

    Args:
        strategy_instance: 现有策略实例
    """
    # 创建风控配置
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%最小启用阈值
        trailing_profit_step_normal=0.10,     # 常规模式：每10%盈利一个阶梯
        trailing_stop_ratio_normal=0.70,      # 常规模式：保留70%利润
        trailing_profit_step_extreme=0.05,    # 极端模式：每5%盈利一个阶梯
        trailing_stop_ratio_extreme=0.80,     # 极端模式：保留80%利润
        max_stops_per_period=2,               # 4小时内最多2次止损
        freeze_hours=4                        # 冷冻4小时
    )

    # 集成风控系统
    integrator = RiskControlIntegrator(strategy_instance, config)

    # 返回集成器，用于手动控制
    return integrator


if __name__ == "__main__":
    example_usage()

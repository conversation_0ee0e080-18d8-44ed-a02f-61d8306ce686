import requests
import time
import hmac
import hashlib
import urllib.parse
import json
import yaml

# 从config.yaml读取API配置
with open("config/config.yaml", "r", encoding="utf-8") as f:
    config = yaml.safe_load(f)

api_key = config["api_key"]
api_secret = config["api_secret"]
base_url = "https://fapi.binance.com"

# 代理设置（与策略保持一致）
proxy_url = "http://127.0.0.1:7897"
proxies = {
    "http": proxy_url,
    "https": proxy_url
}

def sign_request(params):
    query_string = urllib.parse.urlencode(params)
    signature = hmac.new(
        api_secret.encode("utf-8"),
        query_string.encode("utf-8"),
        hashlib.sha256
    ).hexdigest()
    return signature

def test_account_info():
    """测试账户信息"""
    endpoint = "/fapi/v2/account"
    params = {
        "timestamp": int(time.time() * 1000),
        "recvWindow": 5000
    }
    params["signature"] = sign_request(params)
    
    headers = {
        "X-MBX-APIKEY": api_key
    }
    
    try:
        response = requests.get(base_url + endpoint, params=params, headers=headers, proxies=proxies, timeout=10)
        print(f"Account info status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Total balance: {data.get('totalWalletBalance', 'N/A')} USDT")
            print(f"Available balance: {data.get('availableBalance', 'N/A')} USDT")
            print(f"Can trade: {data.get('canTrade', 'N/A')}")
            return True
        else:
            print(f"Error response: {response.text}")
            return False
    except Exception as e:
        print(f"Request exception: {e}")
        return False

def test_exchange_info():
    """测试交易规则获取"""
    try:
        response = requests.get(base_url + "/fapi/v1/exchangeInfo", params={"symbol": "BTCUSDT"}, proxies=proxies, timeout=10)
        print(f"Exchange info status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            symbol_info = data["symbols"][0]
            print(f"Symbol status: {symbol_info.get('status', 'N/A')}")
            
            # 获取过滤器
            for filter_info in symbol_info.get("filters", []):
                if filter_info["filterType"] == "PRICE_FILTER":
                    print(f"Price tick size: {filter_info['tickSize']}")
                elif filter_info["filterType"] == "LOT_SIZE":
                    print(f"Quantity step size: {filter_info['stepSize']}")
                    print(f"Min quantity: {filter_info['minQty']}")
            return True
        else:
            print(f"Error response: {response.text}")
            return False
    except Exception as e:
        print(f"Request exception: {e}")
        return False

def test_simple_order():
    """测试一个简单的下单请求（不会实际成交）"""
    endpoint = "/fapi/v1/order/test"  # 测试下单接口
    data = {
        "symbol": "BTCUSDT",
        "side": "BUY",
        "type": "LIMIT",
        "quantity": "0.001",  # 使用最小精度 0.001
        "price": "10000",     # 名义价值 = 0.001 * 10000 = 10 USDT
        "timeInForce": "GTC",
        "timestamp": int(time.time() * 1000),
        "recvWindow": 5000
    }
    data["signature"] = sign_request(data)
    
    headers = {
        "X-MBX-APIKEY": api_key,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        response = requests.post(base_url + endpoint, data=data, headers=headers, proxies=proxies, timeout=10)
        print(f"Test order status: {response.status_code}")
        if response.status_code == 200:
            print("Test order success")
            return True
        else:
            print(f"Test order failed: {response.text}")
            return False
    except Exception as e:
        print(f"Request exception: {e}")
        return False

if __name__ == "__main__":
    print("=== Binance API Connection Test ===")
    print("1. Testing account info...")
    account_ok = test_account_info()
    
    print("\n2. Testing exchange info...")
    exchange_ok = test_exchange_info()
    
    print("\n3. Testing order permission...")
    order_ok = test_simple_order()
    
    print(f"\n=== Test Results ===")
    print(f"Account info: {'OK' if account_ok else 'FAIL'}")
    print(f"Exchange info: {'OK' if exchange_ok else 'FAIL'}")
    print(f"Order permission: {'OK' if order_ok else 'FAIL'}")
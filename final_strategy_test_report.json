{"timestamp": "2025-10-02T09:14:17.409349", "test_name": "Final Strategy Integrity Test", "scenarios": {"complete_trading_cycle": {"passed": true, "success_rate": "100.0%", "steps": [{"step": "选币", "success": true, "details": "Selected: TESTUSDT"}, {"step": "突破检测", "success": true, "details": "Price: 1.075 > High: 1.07, Volume: 2000.0 > Threshold: 1500.0"}, {"step": "开仓", "success": true, "details": "Entry: 1.075, Qty: 883.72"}, {"step": "止盈平仓", "success": true, "details": "Orders cleaned up successfully"}, {"step": "止损平仓", "success": true, "details": "Stop loss executed and orders cleaned"}, {"step": "移动止损", "success": true, "details": "Trailing stop updated for 6% profit"}]}, "edge_cases": {"passed": true, "success_rate": "100.0%", "cases": [{"case": "余额不足", "success": true, "details": "Balance: 5.0"}, {"case": "API错误处理", "success": true, "details": "API error handled gracefully"}, {"case": "数量精度极限", "success": true, "details": "Tiny: 0.0, Large: 999999.99"}]}}, "summary": {"total_scenarios": 2, "passed_scenarios": 2, "failed_scenarios": 0, "overall_success": true, "success_rate": "100.0%"}}
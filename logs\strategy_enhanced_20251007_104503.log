2025-10-07 10:45:03,363 [root] [INFO] 增强策略日志文件: logs/strategy_enhanced_20251007_104503.log
2025-10-07 10:45:03,374 [HttpClient] [INFO] 强制代理模式: http://127.0.0.1:7897 (本地网络: 192.168.1.188)
2025-10-07 10:45:03,374 [HttpClient] [INFO] 强制代理模式: http://127.0.0.1:7897 (本地网络: 192.168.1.188)
2025-10-07 10:45:07,333 [HttpClient] [INFO] 代理连接测试成功
2025-10-07 10:45:07,333 [HttpClient] [INFO] 代理连接测试成功
2025-10-07 10:45:17,290 [HttpClient] [WARNING] 时间戳偏差较大: 1696ms，网络延迟: 4366ms，已自动校正
2025-10-07 10:45:17,290 [HttpClient] [WARNING] 时间戳偏差较大: 1696ms，网络延迟: 4366ms，已自动校正
2025-10-07 10:45:17,290 [strategy.rate_limiter] [INFO] ApiRateLimiter initialized: 15 requests per 30s
2025-10-07 10:45:17,290 [strategy.rate_limiter] [INFO] ApiRateLimiter initialized: 10 requests per 20s
2025-10-07 10:45:17,290 [strategy.order_queue] [INFO] Worker worker-0 started
2025-10-07 10:45:17,290 [strategy.order_queue] [INFO] Retry worker started
2025-10-07 10:45:17,290 [strategy.order_queue] [INFO] EnhancedOrderQueue initialized with 1 workers, queue size: 1000
2025-10-07 10:45:17,293 [strategy.order_queue] [INFO] OrderOperationQueue initialized with enhanced backend
2025-10-07 10:45:17,293 [strategy.rate_limiter] [INFO] TieredRateLimiter initialized with 3 tiers: ['order', 'query', 'general']
2025-10-07 10:45:17,293 [strategy.order_queue] [INFO] Worker worker-0 started
2025-10-07 10:45:17,294 [strategy.order_queue] [INFO] Worker worker-1 started
2025-10-07 10:45:17,294 [strategy.order_queue] [INFO] Retry worker started
2025-10-07 10:45:17,295 [strategy.order_queue] [INFO] EnhancedOrderQueue initialized with 2 workers, queue size: 1000
2025-10-07 10:45:17,295 [MakerChannelEnhanced] [INFO] ✓ 成功启用增强的限流器和订单队列
2025-10-07 10:45:17,302 [root] [INFO] === 增强版策略启动 ===
2025-10-07 10:45:17,303 [root] [INFO] 功能特性:
2025-10-07 10:45:17,303 [root] [INFO]   - 四级缓存引擎 (TTL+LRU+内存红线)
2025-10-07 10:45:17,303 [root] [INFO]   - 双引擎币种加载 (00:10冷启 + WebSocket实时补票)
2025-10-07 10:45:17,304 [root] [INFO]   - 3mTick合成Bar
2025-10-07 10:45:17,304 [root] [INFO]   - 0.38%回踩限价单
2025-10-07 10:45:17,304 [root] [INFO]   - 四重风控 (保本+移动+降档+Top3+止血贴)
2025-10-07 10:45:17,308 [root] [INFO]   - 龙头池筛选 (24h涨幅前25+成交额前25)
2025-10-07 10:45:17,313 [root] [INFO]   - 增强评分系统 (0-10分)
2025-10-07 10:45:17,324 [root] [INFO] === 策略开始运行 ===
2025-10-07 10:45:17,325 [MakerChannelEnhanced] [INFO] 增强极简版启动
2025-10-07 10:45:17,333 [MakerChannelEnhanced] [INFO] 快照导出已跳过（配置 snapshot_export_enabled=False）
2025-10-07 10:45:17,336 [MakerChannelEnhanced] [INFO] 开始获取24h行情数据...
2025-10-07 10:45:21,267 [MakerChannelEnhanced] [INFO] 获取到 598 个行情数据
2025-10-07 10:45:21,269 [MakerChannelEnhanced] [INFO] 开始加载币种信息...
2025-10-07 10:45:24,466 [MakerChannelEnhanced] [INFO] 用户中断策略执行

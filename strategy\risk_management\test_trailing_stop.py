"""
阶梯式移动止损功能测试
Test for Advanced Trailing Stop Functionality
"""

import logging
from five_layer_risk_control import FiveLayerRiskControl, RiskConfig

def test_trailing_stop():
    """测试阶梯式移动止损功能"""
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)
    
    print("="*60)
    print("阶梯式移动止损功能测试")
    print("="*60)
    
    # 创建配置
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%最小启用阈值
        trailing_profit_step_normal=0.10,     # 常规模式：每10%盈利一个阶梯
        trailing_stop_ratio_normal=0.70,      # 常规模式：保留70%利润
        trailing_profit_step_extreme=0.05,    # 极端模式：每5%盈利一个阶梯
        trailing_stop_ratio_extreme=0.80,     # 极端模式：保留80%利润
    )
    
    # 创建风控实例
    risk_control = FiveLayerRiskControl(config, logger)
    
    # 设置持仓
    symbol = 'BTCUSDT'
    entry_price = 50000.0
    quantity = 0.1
    risk_control.set_position(symbol, entry_price, quantity, entry_price * quantity)
    
    print(f"\n持仓设置：{symbol} @{entry_price} 数量:{quantity}")
    print(f"配置：常规模式每{config.trailing_profit_step_normal:.0%}阶梯，保留{config.trailing_stop_ratio_normal:.0%}利润")
    
    # 测试价格序列
    test_prices = [
        (50000, "入场价"),
        (52500, "+5% (触发保本止损)"),
        (55000, "+10% (第1阶梯)"),
        (60000, "+20% (第2阶梯)"),
        (65000, "+30% (第3阶梯)")
    ]
    
    print("\n价格变化测试：")
    print("-" * 60)
    
    for current_price, description in test_prices:
        print(f"\n价格: {current_price} {description}")
        
        # 模拟市场数据
        market_data = {
            'atr': current_price * 0.02,
            'low_min': current_price * 0.95,
            'high_max': current_price * 1.05,
            'close_std': current_price * 0.015,
            'depth': 150000.0
        }
        
        # 执行风控监控
        result = risk_control.monitor_position(symbol, current_price, market_data)
        
        # 显示结果
        profit_pct = (current_price - entry_price) / entry_price
        current_stop = risk_control.position.get('trail_stop', 0)
        trailing_level = risk_control.position.get('trailing_level', 0)
        
        print(f"  盈利: {profit_pct:.1%}")
        print(f"  阶梯级别: 第{trailing_level}阶梯")
        print(f"  止损价格: {current_stop:.0f}")
        
        if current_stop > 0:
            protected_gain = (current_stop - entry_price) / entry_price
            print(f"  保护利润: {protected_gain:.1%}")
        
        # 显示风控动作
        for detail in result.get('details', []):
            if detail['type'] == 'breakeven':
                print(f"  ✅ 保本止损已激活")
            elif detail['type'] == 'trailing':
                mode = detail.get('trailing_mode', '常规模式')
                level = detail.get('trailing_level', 0)
                print(f"  ✅ 阶梯式移动止损已更新 [{mode}] 第{level}阶梯")
    
    print("\n" + "="*60)
    print("测试完成！")
    
    # 验证最终状态
    final_position = risk_control.position
    if final_position:
        print(f"最终止损价格: {final_position.get('trail_stop', 0):.0f}")
        print(f"最终阶梯级别: 第{final_position.get('trailing_level', 0)}阶梯")
        print(f"移动止损模式: {final_position.get('trailing_mode', '常规模式')}")
    
    return True

if __name__ == "__main__":
    test_trailing_stop()

#!/usr/bin/env python3
# PIPPINUSDT 精度问题修复代码

def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化，使用Decimal确保精度"""
    from decimal import Decimal, ROUND_DOWN
    
    def format_to_precision(value, precision_step):
        """根据精度步长格式化数值"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出 - 修复精度问题
            if decimal_places > 0:
                # 使用格式化字符串，确保不丢失精度
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 去除末尾的0，但保留必要的小数位
                if '.' in formatted:
                    # 分割整数和小数部分
                    int_part, dec_part = formatted.split('.')
                    # 去除小数部分末尾的0
                    dec_part = dec_part.rstrip('0')
                    if dec_part:
                        formatted = f"{int_part}.{dec_part}"
                    else:
                        formatted = int_part
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            # 如果Decimal处理失败，回退到简单字符串格式化
            return str(value)
    
    try:
        # 格式化价格
        price_str = format_to_precision(price, tick_size)
        
        # 格式化数量，确保不小于最小数量
        if min_qty and float(qty) < float(min_qty):
            qty = min_qty
        qty_str = format_to_precision(qty, step_size)
        
        # 最终验证和清理
        if not price_str or price_str.strip() in ['', '.', '0.']:
            price_str = str(float(price))  # 确保有效的浮点字符串
        
        if not qty_str or qty_str.strip() in ['', '.', '0.']:
            qty_str = str(float(qty))  # 确保有效的浮点字符串
        
        # 最终精度检查
        if tick_size and '.' in str(tick_size):
            max_decimals = len(str(tick_size).split('.')[1])
            if '.' in price_str:
                actual_decimals = len(price_str.split('.')[1])
                if actual_decimals > max_decimals:
                    # 重新格式化以符合精度要求
                    price_str = f"{float(price_str):.{max_decimals}f}".rstrip('0').rstrip('.')
        
        return price_str, qty_str
        
    except Exception as e:
        # 最后的回退方案
        return str(float(price)), str(float(qty))
2025-10-02 12:03:49 | INFO     | __init__             | 185  | 🚀 STRATEGY_START: LightNewCoinBreakout Enhanced Logging Version
2025-10-02 12:03:49 | INFO     | __init__             | 186  | 📅 START_TIME: 2025-10-02 04:03:49 UTC
2025-10-02 12:03:49 | INFO     | __init__             | 187  | 📊 LOG_LEVEL: INFO
2025-10-02 12:03:49 | INFO     | __init__             | 188  | 📁 LOG_FILE: logs/strategy_enhanced.log
2025-10-02 12:03:49 | INFO     | __init__             | 191  | 🎯 STARTUP_COIN_SELECTION: Executing initial coin selection
2025-10-02 12:03:49 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 12:03:49 | INFO     | pick                 | 273  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 12:03:49 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 12:04:10 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 12:04:10 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 12:04:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.517s
2025-10-02 12:04:10 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 12:04:10 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 275, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002103BC51310>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 12:04:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.549s
2025-10-02 12:04:10 | WARNING  | __init__             | 197  | ⚠️ STARTUP_PICK_FAILED: No suitable coin found at startup
2025-10-02 12:04:10 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 12:04:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: run()
2025-10-02 12:04:10 | INFO     | run                  | 700  | 🚀 STRATEGY_MAIN_LOOP: LightNewCoinBreakout Fixed Version start
2025-10-02 12:04:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:04:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:04 UTC
2025-10-02 12:04:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:04:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:04:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:04 UTC
2025-10-02 12:04:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:05:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:05:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:05 UTC
2025-10-02 12:05:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:05:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:05:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:05 UTC
2025-10-02 12:05:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:06:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:06:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:06 UTC
2025-10-02 12:06:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 12:06:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:06:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:06 UTC
2025-10-02 12:06:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:07:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:07:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:07 UTC
2025-10-02 12:07:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:07:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:07:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:07 UTC
2025-10-02 12:07:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:08:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:08:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:08 UTC
2025-10-02 12:08:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:08:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:08:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:08 UTC
2025-10-02 12:08:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:09:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:09:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:09 UTC
2025-10-02 12:09:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:09:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:09:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:09 UTC
2025-10-02 12:09:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:10:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:10:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:10 UTC
2025-10-02 12:10:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:10:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:10:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:10 UTC
2025-10-02 12:10:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 12:11:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:11:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:11 UTC
2025-10-02 12:11:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:11:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:11:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:11 UTC
2025-10-02 12:11:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:12:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:12:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:12 UTC
2025-10-02 12:12:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:12:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:12:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:12 UTC
2025-10-02 12:12:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:13:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:13:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:13 UTC
2025-10-02 12:13:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:13:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:13:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:13 UTC
2025-10-02 12:13:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:14:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:14:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:14 UTC
2025-10-02 12:14:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:14:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:14:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:14 UTC
2025-10-02 12:14:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:15:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:15:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:15 UTC
2025-10-02 12:15:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:15:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:15:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:15 UTC
2025-10-02 12:15:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:16:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:16:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:16 UTC
2025-10-02 12:16:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:16:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:16:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:16 UTC
2025-10-02 12:16:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:17:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:17:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:17 UTC
2025-10-02 12:17:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:17:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:17:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:17 UTC
2025-10-02 12:17:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:18:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:18:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:18 UTC
2025-10-02 12:18:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:18:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:18:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:18 UTC
2025-10-02 12:18:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:19:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:19:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:19 UTC
2025-10-02 12:19:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:19:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:19:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:19 UTC
2025-10-02 12:19:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:20:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:20:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:20 UTC
2025-10-02 12:20:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:20:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:20:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:20 UTC
2025-10-02 12:20:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:21:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:21:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:21 UTC
2025-10-02 12:21:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:21:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:21:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:21 UTC
2025-10-02 12:21:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:22:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:22:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:22 UTC
2025-10-02 12:22:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:22:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:22:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:22 UTC
2025-10-02 12:22:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:23:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:23:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:23 UTC
2025-10-02 12:23:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:23:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:23:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:23 UTC
2025-10-02 12:23:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:24:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:24:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:24 UTC
2025-10-02 12:24:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:24:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:24:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:24 UTC
2025-10-02 12:24:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:25:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:25:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:25 UTC
2025-10-02 12:25:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:25:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:25:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:25 UTC
2025-10-02 12:25:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.006s
2025-10-02 12:26:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:26:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:26 UTC
2025-10-02 12:26:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:26:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:26:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:26 UTC
2025-10-02 12:26:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:27:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:27:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:27 UTC
2025-10-02 12:27:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:27:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:27:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:27 UTC
2025-10-02 12:27:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:28:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:28:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:28 UTC
2025-10-02 12:28:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:28:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:28:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:28 UTC
2025-10-02 12:28:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:29:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:29:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:29 UTC
2025-10-02 12:29:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:29:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:29:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:29 UTC
2025-10-02 12:29:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:30:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:30:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:30 UTC
2025-10-02 12:30:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:30:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:30:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:30 UTC
2025-10-02 12:30:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:31:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:31:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:31 UTC
2025-10-02 12:31:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:31:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:31:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:31 UTC
2025-10-02 12:31:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:32:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:32:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:32 UTC
2025-10-02 12:32:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 12:32:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:32:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:32 UTC
2025-10-02 12:32:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:33:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:33:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:33 UTC
2025-10-02 12:33:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:33:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:33:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:33 UTC
2025-10-02 12:33:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:34:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:34:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:34 UTC
2025-10-02 12:34:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:34:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:34:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:34 UTC
2025-10-02 12:34:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:35:10 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:35:10 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:35 UTC
2025-10-02 12:35:10 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:35:40 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:35:40 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:35 UTC
2025-10-02 12:35:40 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.026s
2025-10-02 12:36:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:36:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:36 UTC
2025-10-02 12:36:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:36:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:36:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:36 UTC
2025-10-02 12:36:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:37:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:37:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:37 UTC
2025-10-02 12:37:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:37:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:37:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:37 UTC
2025-10-02 12:37:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:38:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:38:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:38 UTC
2025-10-02 12:38:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:38:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:38:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:38 UTC
2025-10-02 12:38:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:39:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:39:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:39 UTC
2025-10-02 12:39:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:39:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:39:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:39 UTC
2025-10-02 12:39:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:40:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:40:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:40 UTC
2025-10-02 12:40:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:40:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:40:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:40 UTC
2025-10-02 12:40:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:41:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:41:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:41 UTC
2025-10-02 12:41:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:41:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:41:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:41 UTC
2025-10-02 12:41:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 12:42:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:42:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:42 UTC
2025-10-02 12:42:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 12:42:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:42:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:42 UTC
2025-10-02 12:42:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:43:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:43:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:43 UTC
2025-10-02 12:43:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:43:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:43:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:43 UTC
2025-10-02 12:43:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 12:44:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:44:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:44 UTC
2025-10-02 12:44:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:44:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:44:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:44 UTC
2025-10-02 12:44:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:45:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:45:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:45 UTC
2025-10-02 12:45:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:45:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:45:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:45 UTC
2025-10-02 12:45:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:46:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:46:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:46 UTC
2025-10-02 12:46:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.133s
2025-10-02 12:46:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:46:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:46 UTC
2025-10-02 12:46:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:47:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:47:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:47 UTC
2025-10-02 12:47:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:47:41 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:47:41 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:47 UTC
2025-10-02 12:47:41 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 12:48:11 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:48:11 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:48 UTC
2025-10-02 12:48:11 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 12:50:49 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:50:49 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:50 UTC
2025-10-02 12:50:49 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.518s
2025-10-02 12:51:20 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 12:51:20 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 04:51 UTC
2025-10-02 12:51:20 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 13:56:18 | INFO     | __init__             | 194  | 🚀 STRATEGY_START: LightNewCoinBreakout Enhanced Logging Version
2025-10-02 13:56:18 | INFO     | __init__             | 195  | 📅 START_TIME: 2025-10-02 05:56:18 UTC
2025-10-02 13:56:18 | INFO     | __init__             | 196  | 📊 LOG_LEVEL: INFO
2025-10-02 13:56:18 | INFO     | __init__             | 197  | 📁 LOG_FILE: logs/strategy_enhanced.log
2025-10-02 13:56:18 | INFO     | __init__             | 200  | 🎯 STARTUP_COIN_SELECTION: Executing initial coin selection
2025-10-02 13:56:18 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 13:56:18 | INFO     | pick                 | 282  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 13:56:18 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 13:56:39 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 13:56:39 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 13:56:39 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.073s
2025-10-02 13:56:39 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 13:56:39 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 284, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384578273&signature=69a3e933edbb8ddabd86f142427372f47218abf388f4f3449ace8f45cc19957e (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F342F34850>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 13:56:39 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.142s
2025-10-02 13:56:39 | WARNING  | __init__             | 206  | ⚠️ STARTUP_PICK_FAILED: No suitable coin found at startup
2025-10-02 13:56:39 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 13:56:39 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: run()
2025-10-02 13:56:39 | INFO     | run                  | 709  | 🚀 STRATEGY_MAIN_LOOP: LightNewCoinBreakout Fixed Version start
2025-10-02 13:56:39 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:56:39 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:56 UTC
2025-10-02 13:56:39 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 13:57:09 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:57:09 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:57 UTC
2025-10-02 13:57:09 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 13:57:39 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:57:39 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:57 UTC
2025-10-02 13:57:39 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 13:58:13 | INFO     | __init__             | 194  | 🚀 STRATEGY_START: LightNewCoinBreakout Enhanced Logging Version
2025-10-02 13:58:13 | INFO     | __init__             | 195  | 📅 START_TIME: 2025-10-02 05:58:13 UTC
2025-10-02 13:58:13 | INFO     | __init__             | 196  | 📊 LOG_LEVEL: INFO
2025-10-02 13:58:13 | INFO     | __init__             | 197  | 📁 LOG_FILE: logs/strategy_enhanced.log
2025-10-02 13:58:13 | INFO     | __init__             | 200  | 🎯 STARTUP_COIN_SELECTION: Executing initial coin selection
2025-10-02 13:58:13 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 13:58:13 | INFO     | pick                 | 282  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 13:58:13 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 13:58:35 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 13:58:35 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 13:58:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.064s
2025-10-02 13:58:35 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 13:58:35 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 284, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384693963&signature=051018965a5d605ee758652f5675753c717d6b43134b740ab2ccb7e91e626c2a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B371790>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 13:58:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.092s
2025-10-02 13:58:35 | WARNING  | __init__             | 206  | ⚠️ STARTUP_PICK_FAILED: No suitable coin found at startup
2025-10-02 13:58:35 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 13:58:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: run()
2025-10-02 13:58:35 | INFO     | run                  | 709  | 🚀 STRATEGY_MAIN_LOOP: LightNewCoinBreakout Fixed Version start
2025-10-02 13:58:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:58:35 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:58 UTC
2025-10-02 13:58:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 13:59:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:59:05 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:59 UTC
2025-10-02 13:59:05 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 13:59:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 13:59:35 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 05:59 UTC
2025-10-02 13:59:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:00:05 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:00 UTC
2025-10-02 14:00:05 | INFO     | daily                | 445  | 🕘 COIN_SELECTION_TIME: 欧洲早盘 (北京时间 14:00)
2025-10-02 14:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: reset_position()
2025-10-02 14:00:05 | INFO     | reset_position       | 532  | 🔄 POSITION_RESET: Clearing all position data
2025-10-02 14:00:05 | INFO     | log_state_change     | 90   | 🔄 STATE_CHANGE: position_state | {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} → {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} | Context: position reset
2025-10-02 14:00:05 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  reset_position | Result: completed | Duration: 0.009s
2025-10-02 14:00:05 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 14:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 14:00:05 | INFO     | pick                 | 282  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 14:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 14:00:26 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 14:00:26 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 14:00:26 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.058s
2025-10-02 14:00:26 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 14:00:26 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 284, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384805123&signature=41825c87274d6b74d8dab6c92147a637571c1fb305ad1e24ac6ef16daa2ca510 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D5CD0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 14:00:26 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.073s
2025-10-02 14:00:26 | WARNING  | daily                | 467  | ⚠️ NO_COIN_SELECTED: No suitable coin found during 欧洲早盘 (北京时间 14:00)
2025-10-02 14:00:26 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 21.112s
2025-10-02 14:00:56 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:00:56 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:00 UTC
2025-10-02 14:00:56 | INFO     | daily                | 445  | 🕘 COIN_SELECTION_TIME: 欧洲早盘 (北京时间 14:00)
2025-10-02 14:00:56 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: reset_position()
2025-10-02 14:00:56 | INFO     | reset_position       | 532  | 🔄 POSITION_RESET: Clearing all position data
2025-10-02 14:00:56 | INFO     | log_state_change     | 90   | 🔄 STATE_CHANGE: position_state | {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} → {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} | Context: position reset
2025-10-02 14:00:56 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  reset_position | Result: completed | Duration: 0.031s
2025-10-02 14:00:56 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 14:00:56 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 14:00:56 | INFO     | pick                 | 282  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 14:00:56 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 14:01:17 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 14:01:17 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 14:01:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.067s
2025-10-02 14:01:17 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 14:01:17 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 284, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 129, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr?timestamp=1759384856343&signature=a7ac793359319534dca61d1d7dac4cadf53edc020067541bfd7041aa5571e1ce (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001630B3D7550>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 14:01:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.124s
2025-10-02 14:01:17 | WARNING  | daily                | 467  | ⚠️ NO_COIN_SELECTED: No suitable coin found during 欧洲早盘 (北京时间 14:00)
2025-10-02 14:01:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 21.234s
2025-10-02 14:01:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:01:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:01 UTC
2025-10-02 14:01:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.012s
2025-10-02 14:02:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:02:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:02 UTC
2025-10-02 14:02:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:02:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:02:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:02 UTC
2025-10-02 14:02:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:03:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:03:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:03 UTC
2025-10-02 14:03:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:03:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:03:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:03 UTC
2025-10-02 14:03:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:04:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:04:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:04 UTC
2025-10-02 14:04:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:04:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:04:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:04 UTC
2025-10-02 14:04:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.008s
2025-10-02 14:05:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:05:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:05 UTC
2025-10-02 14:05:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:05:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:05:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:05 UTC
2025-10-02 14:05:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:06:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:06:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:06 UTC
2025-10-02 14:06:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:06:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:06:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:06 UTC
2025-10-02 14:06:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:07:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:07:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:07 UTC
2025-10-02 14:07:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:07:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:07:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:07 UTC
2025-10-02 14:07:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:08:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:08:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:08 UTC
2025-10-02 14:08:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:08:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:08:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:08 UTC
2025-10-02 14:08:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:09:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:09:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:09 UTC
2025-10-02 14:09:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.001s
2025-10-02 14:09:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:09:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:09 UTC
2025-10-02 14:09:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:10:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:10:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:10 UTC
2025-10-02 14:10:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:10:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:10:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:10 UTC
2025-10-02 14:10:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:11:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:11:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:11 UTC
2025-10-02 14:11:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:11:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:11:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:11 UTC
2025-10-02 14:11:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:12:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:12:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:12 UTC
2025-10-02 14:12:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:12:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:12:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:12 UTC
2025-10-02 14:12:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:13:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:13:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:13 UTC
2025-10-02 14:13:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.005s
2025-10-02 14:13:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:13:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:13 UTC
2025-10-02 14:13:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed
2025-10-02 14:14:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:14:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:14 UTC
2025-10-02 14:14:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:14:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:14:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:14 UTC
2025-10-02 14:14:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.009s
2025-10-02 14:15:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:15:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:15 UTC
2025-10-02 14:15:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:15:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:15:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:15 UTC
2025-10-02 14:15:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.003s
2025-10-02 14:16:17 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:16:17 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:16 UTC
2025-10-02 14:16:17 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 14:16:47 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 14:16:47 | INFO     | daily                | 421  | ⏰ DAILY_CHECK: Current time 06:16 UTC
2025-10-02 14:16:47 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.002s
2025-10-02 14:17:05 | INFO     | run                  | 847  | ⏹️ USER_INTERRUPT: Strategy stopped by user
2025-10-02 14:17:05 | INFO     | run                  | 855  | 🏁 STRATEGY_STOP: 2025-10-02 06:17:05 UTC
2025-10-02 14:17:05 | INFO     | run                  | 856  | ⏱️ TOTAL_RUNTIME: 0:18:51.361530
2025-10-02 14:17:05 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  run | Result: user_interrupt | Duration: 1110.231s

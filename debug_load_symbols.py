#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import json
import logging
from http_client import HttpClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def test_load_symbols():
    """测试币种加载功能"""
    try:
        # 加载配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        with open('config/config.json', 'r', encoding='utf-8') as f:
            json_config = json.load(f)
        
        # 初始化HttpClient
        http = HttpClient(
            config['api_key'], 
            config['api_secret'], 
            config.get('base_url', 'https://fapi.binance.com'),
            config.get('verify_ssl', True)
        )
        
        print("开始测试币种加载...")
        
        # 测试exchangeInfo接口
        print("调用 /fapi/v1/exchangeInfo...")
        exchange_info = http.get('/fapi/v1/exchangeInfo')
        
        if exchange_info:
            symbols = exchange_info.get('symbols', [])
            print(f"成功获取 {len(symbols)} 个币种信息")
            
            # 过滤USDT永续合约
            usdt_perpetual = [
                s for s in symbols 
                if s.get('symbol', '').endswith('USDT') 
                and s.get('contractType') == 'PERPETUAL' 
                and s.get('status') == 'TRADING'
            ]
            print(f"USDT永续合约数量: {len(usdt_perpetual)}")
            
            # 显示前5个币种
            for i, symbol in enumerate(usdt_perpetual[:5]):
                print(f"  {i+1}. {symbol['symbol']} - 状态: {symbol['status']}")
                
        else:
            print("获取币种信息失败!")
            
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_load_symbols()
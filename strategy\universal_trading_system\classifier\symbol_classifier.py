"""
币种智能分类系统
Universal Symbol Classifier for 500+ Trading Pairs

功能：
1. 根据多维度指标自动分类币种
2. 支持动态更新分类结果
3. 提供分类查询和统计接口
"""

import time
import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class SymbolTier(Enum):
    """币种分类等级"""
    TIER_1 = "蓝筹币"      # 蓝筹币：BTC、ETH等
    TIER_2 = "主流币"      # 主流币：前50市值
    TIER_3 = "新兴币"      # 新兴币：50-200市值
    TIER_4 = "小币种"      # 小币种：200+市值
    TIER_5 = "新币种"      # 新币种：上线<30天

@dataclass
class SymbolMetrics:
    """币种指标数据"""
    symbol: str
    market_cap_rank: int = 999
    listing_days: int = 0
    volume_24h: float = 0.0
    price_change_24h: float = 0.0
    volatility_7d: float = 0.0
    depth_1pct: float = 0.0
    holders_count: int = 0
    last_update: float = 0.0

class SymbolClassifier:
    """币种智能分类器"""
    
    def __init__(self, http_client=None):
        self.http = http_client
        self.logger = logging.getLogger(__name__)
        
        # 分类缓存
        self.symbol_tiers: Dict[str, SymbolTier] = {}
        self.symbol_metrics: Dict[str, SymbolMetrics] = {}
        
        # 分类规则配置
        self.tier_rules = {
            SymbolTier.TIER_1: {
                'market_cap_rank': (1, 10),
                'listing_days': (365, float('inf')),
                'volume_24h_min': 100_000_000,  # 1亿美元
                'volatility_7d_max': 0.15,      # 15%
            },
            SymbolTier.TIER_2: {
                'market_cap_rank': (11, 50),
                'listing_days': (90, float('inf')),
                'volume_24h_min': 10_000_000,   # 1000万美元
                'volatility_7d_max': 0.25,      # 25%
            },
            SymbolTier.TIER_3: {
                'market_cap_rank': (51, 200),
                'listing_days': (30, float('inf')),
                'volume_24h_min': 1_000_000,    # 100万美元
                'volatility_7d_max': 0.40,      # 40%
            },
            SymbolTier.TIER_4: {
                'market_cap_rank': (201, float('inf')),
                'listing_days': (30, float('inf')),
                'volume_24h_min': 100_000,      # 10万美元
                'volatility_7d_max': 0.60,      # 60%
            },
            SymbolTier.TIER_5: {
                'listing_days': (0, 30),        # 新币种：上线<30天
                'volume_24h_min': 0,
                'volatility_7d_max': 1.0,       # 100%
            }
        }
        
        # 预定义蓝筹币列表
        self.tier1_symbols = {
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
            'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
        }
        
        self.cache_duration = 3600  # 缓存1小时
        
    def get_symbol_tier(self, symbol: str) -> SymbolTier:
        """获取币种分类等级"""
        # 检查缓存
        if symbol in self.symbol_tiers:
            metrics = self.symbol_metrics.get(symbol)
            if metrics and time.time() - metrics.last_update < self.cache_duration:
                return self.symbol_tiers[symbol]
        
        # 重新分类
        tier = self._classify_symbol(symbol)
        self.symbol_tiers[symbol] = tier
        
        self.logger.info(f"币种分类: {symbol} -> {tier.value}")
        return tier
    
    def _classify_symbol(self, symbol: str) -> SymbolTier:
        """执行币种分类逻辑"""
        try:
            # 获取币种指标数据
            metrics = self._fetch_symbol_metrics(symbol)
            self.symbol_metrics[symbol] = metrics
            
            # 优先检查是否为预定义蓝筹币
            if symbol in self.tier1_symbols:
                return SymbolTier.TIER_1
            
            # 按优先级检查分类规则
            for tier, rules in self.tier_rules.items():
                if self._match_tier_rules(metrics, rules):
                    return tier
            
            # 默认分类为小币种
            return SymbolTier.TIER_4
            
        except Exception as e:
            self.logger.error(f"分类失败 {symbol}: {e}")
            return SymbolTier.TIER_4  # 默认保守分类
    
    def _fetch_symbol_metrics(self, symbol: str) -> SymbolMetrics:
        """获取币种指标数据"""
        metrics = SymbolMetrics(symbol=symbol, last_update=time.time())
        
        try:
            # 获取24小时统计数据
            if self.http:
                ticker = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                if ticker:
                    metrics.volume_24h = float(ticker.get('volume', 0)) * float(ticker.get('lastPrice', 0))
                    metrics.price_change_24h = float(ticker.get('priceChangePercent', 0)) / 100
            
            # 获取市值排名（模拟数据，实际可接入CoinGecko等API）
            metrics.market_cap_rank = self._estimate_market_cap_rank(symbol, metrics.volume_24h)
            
            # 估算上线天数
            metrics.listing_days = self._estimate_listing_days(symbol)
            
            # 计算7日波动率（简化计算）
            metrics.volatility_7d = abs(metrics.price_change_24h) * 2  # 简化估算
            
            # 获取订单簿深度
            metrics.depth_1pct = self._get_depth_1pct(symbol)
            
        except Exception as e:
            self.logger.warning(f"获取指标数据失败 {symbol}: {e}")
        
        return metrics
    
    def _match_tier_rules(self, metrics: SymbolMetrics, rules: Dict) -> bool:
        """检查是否匹配分类规则"""
        # 检查市值排名
        if 'market_cap_rank' in rules:
            min_rank, max_rank = rules['market_cap_rank']
            if not (min_rank <= metrics.market_cap_rank <= max_rank):
                return False
        
        # 检查上线天数
        if 'listing_days' in rules:
            min_days, max_days = rules['listing_days']
            if not (min_days <= metrics.listing_days <= max_days):
                return False
        
        # 检查24小时交易量
        if 'volume_24h_min' in rules:
            if metrics.volume_24h < rules['volume_24h_min']:
                return False
        
        # 检查7日波动率
        if 'volatility_7d_max' in rules:
            if metrics.volatility_7d > rules['volatility_7d_max']:
                return False
        
        return True
    
    def _estimate_market_cap_rank(self, symbol: str, volume_24h: float) -> int:
        """估算市值排名（基于交易量）"""
        # 蓝筹币
        if symbol in self.tier1_symbols:
            return 5
        
        # 基于交易量估算排名
        if volume_24h > 100_000_000:    # >1亿
            return 15
        elif volume_24h > 50_000_000:   # >5000万
            return 35
        elif volume_24h > 10_000_000:   # >1000万
            return 80
        elif volume_24h > 1_000_000:    # >100万
            return 150
        else:
            return 300
    
    def _estimate_listing_days(self, symbol: str) -> int:
        """估算上线天数"""
        # 知名老币
        old_symbols = {
            'BTCUSDT': 3000, 'ETHUSDT': 2500, 'BNBUSDT': 2000,
            'ADAUSDT': 1500, 'XRPUSDT': 2800, 'DOTUSDT': 800
        }
        
        if symbol in old_symbols:
            return old_symbols[symbol]
        
        # 基于交易量估算（简化逻辑）
        metrics = self.symbol_metrics.get(symbol)
        if metrics and metrics.volume_24h > 50_000_000:
            return 500  # 大交易量通常是老币
        elif metrics and metrics.volume_24h > 10_000_000:
            return 200
        elif metrics and metrics.volume_24h > 1_000_000:
            return 100
        else:
            return 30   # 默认认为是新币
    
    def _get_depth_1pct(self, symbol: str) -> float:
        """获取1%深度（订单簿深度）"""
        try:
            if self.http:
                depth = self.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 100})
                if depth and 'bids' in depth and 'asks' in depth:
                    # 简化计算：取前10档深度
                    bid_depth = sum(float(bid[1]) for bid in depth['bids'][:10])
                    ask_depth = sum(float(ask[1]) for ask in depth['asks'][:10])
                    return (bid_depth + ask_depth) / 2
        except Exception as e:
            self.logger.warning(f"获取深度失败 {symbol}: {e}")
        
        return 0.0
    
    def get_tier_symbols(self, tier: SymbolTier) -> List[str]:
        """获取指定分类的所有币种"""
        return [symbol for symbol, symbol_tier in self.symbol_tiers.items() 
                if symbol_tier == tier]
    
    def get_classification_stats(self) -> Dict[str, int]:
        """获取分类统计信息"""
        stats = {}
        for tier in SymbolTier:
            stats[tier.value] = len(self.get_tier_symbols(tier))
        return stats
    
    def update_symbol_classification(self, symbol: str) -> SymbolTier:
        """强制更新指定币种的分类"""
        if symbol in self.symbol_tiers:
            del self.symbol_tiers[symbol]
        if symbol in self.symbol_metrics:
            del self.symbol_metrics[symbol]
        
        return self.get_symbol_tier(symbol)
    
    def batch_classify_symbols(self, symbols: List[str]) -> Dict[str, SymbolTier]:
        """批量分类币种"""
        results = {}
        for symbol in symbols:
            try:
                results[symbol] = self.get_symbol_tier(symbol)
            except Exception as e:
                self.logger.error(f"批量分类失败 {symbol}: {e}")
                results[symbol] = SymbolTier.TIER_4
        
        return results
    
    def export_classification_data(self, filepath: str):
        """导出分类数据到文件"""
        data = {
            'tiers': {symbol: tier.value for symbol, tier in self.symbol_tiers.items()},
            'metrics': {symbol: {
                'market_cap_rank': metrics.market_cap_rank,
                'listing_days': metrics.listing_days,
                'volume_24h': metrics.volume_24h,
                'volatility_7d': metrics.volatility_7d,
                'last_update': metrics.last_update
            } for symbol, metrics in self.symbol_metrics.items()},
            'stats': self.get_classification_stats(),
            'export_time': time.time()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"分类数据已导出到: {filepath}")

# 使用示例
if __name__ == "__main__":
    # 初始化分类器
    classifier = SymbolClassifier()
    
    # 测试币种分类
    test_symbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT', 'SHIBUSDT', 'NEWCOINUSDT']
    
    for symbol in test_symbols:
        tier = classifier.get_symbol_tier(symbol)
        print(f"{symbol}: {tier.value}")
    
    # 获取分类统计
    stats = classifier.get_classification_stats()
    print(f"\n分类统计: {stats}")
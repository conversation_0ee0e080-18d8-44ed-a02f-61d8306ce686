# -*- coding: utf-8 -*-
"""
日志功能测试脚本
测试策略的日志系统是否正常工作，包括不同日志级别、文件输出、异常处理等
"""
import os
import sys
import time
import logging
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 添加策略目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

def test_logging_configuration():
    """测试日志配置功能"""
    print("🧪 测试1: 日志配置功能")
    
    # 创建临时日志目录
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, 'test_strategy.log')
    
    try:
        from maker_channel_light_fixed import setup_logger
        
        # 测试不同日志级别
        for level in ['DEBUG', 'INFO', 'WARNING', 'ERROR']:
            print(f"  ✓ 测试日志级别: {level}")
            logger = setup_logger(level, log_file)
            assert logger.level == getattr(logging, level)
            
        # 测试文件输出
        logger = setup_logger('INFO', log_file)
        test_message = "测试日志消息"
        logging.info(test_message)
        
        # 检查日志文件是否创建并包含消息
        assert os.path.exists(log_file), "日志文件未创建"
        
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert test_message in content, "日志消息未写入文件"
            
        print("  ✅ 日志配置功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 日志配置功能测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_logging_functions():
    """测试日志辅助函数"""
    print("🧪 测试2: 日志辅助函数")
    
    try:
        from maker_channel_light_fixed import (
            log_function_entry, log_function_exit, log_exception,
            log_state_change, log_order_operation, log_status_summary
        )
        
        # 创建模拟策略实例
        class MockStrategy:
            def __init__(self):
                self.symbol = 'BTCUSDT'
                self.entry = 50000.0
                self.qty = 0.001
                self.stop_order_id = '12345'
                self.take_profit_order_id = '67890'
                self.max_profit = 5.5
                self.position_opened_time = None
        
        mock_strategy = MockStrategy()
        
        # 测试各种日志函数
        print("  ✓ 测试函数入口日志")
        log_function_entry("test_function", param1="value1", param2=123)
        
        print("  ✓ 测试函数出口日志")
        log_function_exit("test_function", result="success", duration=1.234)
        
        print("  ✓ 测试异常日志")
        try:
            raise ValueError("测试异常")
        except Exception as e:
            log_exception("test_function", e, context="测试上下文")
        
        print("  ✓ 测试状态变化日志")
        log_state_change("price", 49000, 50000, "价格更新")
        
        print("  ✓ 测试订单操作日志")
        order_data = {'symbol': 'BTCUSDT', 'side': 'BUY', 'quantity': 0.001}
        log_order_operation("CREATE", "BTCUSDT", order_data, {'orderId': '12345'})
        
        print("  ✓ 测试状态摘要日志")
        log_status_summary(mock_strategy)
        
        print("  ✅ 日志辅助函数测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 日志辅助函数测试失败: {e}")
        return False

def test_strategy_logging_integration():
    """测试策略日志集成"""
    print("🧪 测试3: 策略日志集成")
    
    # 创建临时日志目录
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, 'strategy_integration_test.log')
    
    try:
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        # 模拟API调用
        with patch('maker_channel_light_fixed.get') as mock_get, \
             patch('maker_channel_light_fixed.post') as mock_post:
            
            # 设置模拟返回值
            mock_get.return_value = {'price': '50000.0'}
            mock_post.return_value = {'orderId': '12345', 'status': 'FILLED'}
            
            # 创建策略实例
            print("  ✓ 创建策略实例")
            strategy = LightNewCoinBreakout(log_level='DEBUG', log_file=log_file)
            
            # 测试各种方法的日志输出
            print("  ✓ 测试格式化函数日志")
            formatted_qty = strategy.format_quantity(0.123456789, 0.001)
            formatted_price = strategy.format_price(50000.123456, 0.01)
            
            print("  ✓ 测试重置持仓日志")
            strategy.reset_position()
            
            # 检查日志文件内容
            assert os.path.exists(log_file), "策略日志文件未创建"
            
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查关键日志消息
                assert "STRATEGY_START" in content, "策略启动日志缺失"
                assert "ENTRY:" in content, "函数入口日志缺失"
                assert "EXIT:" in content, "函数出口日志缺失"
                assert "STATUS_SUMMARY" in content, "状态摘要日志缺失"
                
        print("  ✅ 策略日志集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 策略日志集成测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_log_rotation():
    """测试日志轮转功能"""
    print("🧪 测试4: 日志轮转功能")
    
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, 'rotation_test.log')
    
    try:
        from maker_channel_light_fixed import setup_logger
        
        # 设置小的文件大小限制以触发轮转
        logger = setup_logger('INFO', log_file, max_bytes=1024, backup_count=3)
        
        # 写入大量日志以触发轮转
        for i in range(100):
            logging.info(f"测试日志轮转消息 {i} - " + "x" * 50)
            
        # 检查是否生成了轮转文件
        log_files = [f for f in os.listdir(temp_dir) if f.startswith('rotation_test.log')]
        
        print(f"  ✓ 生成的日志文件: {log_files}")
        assert len(log_files) > 1, "日志轮转未生效"
        
        print("  ✅ 日志轮转功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 日志轮转功能测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_exception_logging():
    """测试异常日志记录"""
    print("🧪 测试5: 异常日志记录")
    
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, 'exception_test.log')
    
    try:
        from maker_channel_light_fixed import setup_logger, log_exception
        
        logger = setup_logger('ERROR', log_file)
        
        # 测试不同类型的异常
        exceptions_to_test = [
            ValueError("测试值错误"),
            KeyError("missing_key"),
            RuntimeError("运行时错误"),
            ConnectionError("网络连接错误")
        ]
        
        for i, exc in enumerate(exceptions_to_test):
            try:
                raise exc
            except Exception as e:
                log_exception(f"test_function_{i}", e, context=f"测试上下文_{i}")
        
        # 检查异常日志
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            assert "EXCEPTION" in content, "异常日志标记缺失"
            assert "TRACEBACK" in content, "异常堆栈缺失"
            assert "测试值错误" in content, "异常消息缺失"
            assert "测试上下文" in content, "异常上下文缺失"
        
        print("  ✅ 异常日志记录测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 异常日志记录测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def main():
    """运行所有日志功能测试"""
    print("🚀 开始日志功能完整性测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        test_logging_configuration,
        test_logging_functions,
        test_strategy_logging_integration,
        test_log_rotation,
        test_exception_logging
    ]
    
    for test_func in tests:
        try:
            result = test_func()
            test_results.append(result)
            print()
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 执行失败: {e}")
            test_results.append(False)
            print()
    
    # 汇总结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ 通过测试: {passed}/{total}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 所有日志功能测试通过！日志系统工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查日志系统配置。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
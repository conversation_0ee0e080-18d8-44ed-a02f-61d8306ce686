{"test_suite": "全面修复效果验证", "start_time": "2025-10-02T08:24:12.787863", "tests": {"trading_rules_acquisition": {"test_name": "交易规则获取", "start_time": "2025-10-02T08:24:12.789864", "symbols_tested": ["BTCUSDT", "ETHUSDT", "PLAYUSDT", "PROVEUSDT", "PIPPINUSDT", "ADAUSDT", "DOTUSDT"], "success_count": 7, "failure_count": 0, "details": {"BTCUSDT": {"status": "SUCCESS", "rules": {"tick_size": 0.01, "step_size": 0.001, "min_qty": 0.001, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "ETHUSDT": {"status": "SUCCESS", "rules": {"tick_size": 0.01, "step_size": 0.001, "min_qty": 0.001, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "PLAYUSDT": {"status": "SUCCESS", "rules": {"tick_size": 1e-05, "step_size": 1.0, "min_qty": 1.0, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "PROVEUSDT": {"status": "SUCCESS", "rules": {"tick_size": 0.0001, "step_size": 1.0, "min_qty": 1.0, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "PIPPINUSDT": {"status": "SUCCESS", "rules": {"tick_size": 1e-05, "step_size": 0.001, "min_qty": 0.001, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "ADAUSDT": {"status": "SUCCESS", "rules": {"tick_size": 0.0001, "step_size": 1.0, "min_qty": 1.0, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}, "DOTUSDT": {"status": "SUCCESS", "rules": {"tick_size": 0.001, "step_size": 0.1, "min_qty": 0.1, "min_notional": 5.0}, "validation": {"tick_size_valid": true, "step_size_valid": true, "min_qty_valid": true, "min_notional_valid": true, "all_valid": true}}}, "end_time": "2025-10-02T08:24:12.796862", "success_rate": 100.0}, "precision_handling": {"test_name": "精度处理", "start_time": "2025-10-02T08:24:12.797863", "test_cases": [{"case_id": 1, "input_value": 50123.456789, "precision": 0.01, "expected": 50123.45, "type": "price", "actual": 50123.456789, "success": false}, {"case_id": 2, "input_value": 50123.454, "precision": 0.01, "expected": 50123.45, "type": "price", "actual": 50123.454, "success": false}, {"case_id": 3, "input_value": 1.2345e-05, "precision": 1e-06, "expected": 1.2e-05, "type": "price", "actual": 1.2345e-05, "success": false}, {"case_id": 4, "input_value": 1.23456789, "precision": 0.0001, "expected": 1.2345, "type": "price", "actual": 1.23456789, "success": false}, {"case_id": 5, "input_value": 10.123456, "precision": 0.001, "expected": 10.123, "type": "quantity", "actual": 10.123456, "success": false}, {"case_id": 6, "input_value": 0.000123456, "precision": 1e-06, "expected": 0.000123, "type": "quantity", "actual": 0.000123456, "success": false}, {"case_id": 7, "input_value": 100.999, "precision": 1.0, "expected": 100.0, "type": "quantity", "actual": 100.999, "success": false}], "success_count": 0, "failure_count": 7, "end_time": "2025-10-02T08:24:12.802897", "success_rate": 0.0}, "monitoring_system": {"test_name": "监控系统", "start_time": "2025-10-02T08:24:12.803896", "components_tested": ["trading_rules_events", "order_execution_events", "error_pattern_analysis", "health_report"], "success_count": 4, "failure_count": 0, "details": {"trading_rules_events": "SUCCESS", "order_execution_events": "SUCCESS", "error_pattern_analysis": "SUCCESS", "patterns_found": 1, "health_report": "SUCCESS", "health_status": "CRITICAL"}, "end_time": "2025-10-02T08:24:17.851936", "success_rate": 100.0}, "retry_mechanism": {"test_name": "智能重试机制", "start_time": "2025-10-02T08:24:17.851936", "test_scenarios": [{"error_code": -1111, "attempt": 1, "expected_action": "refresh_rules", "actual_action": "refresh_rules", "success": true}, {"error_code": -1022, "attempt": 1, "expected_action": "refresh_timestamp", "actual_action": "refresh_timestamp", "success": true}, {"error_code": -2010, "attempt": 1, "expected_action": "normal_retry", "actual_action": "normal_retry", "success": true}, {"error_code": -1111, "attempt": 3, "expected_action": "skip", "actual_action": "skip", "success": true}, {"error_code": -9999, "attempt": 1, "expected_action": "normal_retry", "actual_action": "normal_retry", "success": true}], "success_count": 5, "failure_count": 0, "end_time": "2025-10-02T08:24:17.851936", "success_rate": 100.0}, "integration_compatibility": {"test_name": "集成兼容性", "start_time": "2025-10-02T08:24:17.851936", "compatibility_checks": [{"check_name": "emergency_fix_import", "status": "SUCCESS"}, {"check_name": "system_monitoring_import", "status": "SUCCESS"}, {"check_name": "config_file_access", "status": "SUCCESS"}, {"check_name": "log_directory_access", "status": "SUCCESS"}, {"check_name": "strategy_file_syntax", "status": "SUCCESS"}], "success_count": 5, "failure_count": 0, "details": {}, "end_time": "2025-10-02T08:24:17.913450", "success_rate": 100.0}}, "summary": {"total_tests": 28, "total_success": 21, "total_failures": 7, "overall_success_rate": 75.0, "status": "FAILED"}, "end_time": "2025-10-02T08:24:17.914449", "duration": "0:00:05.126586"}
2025-10-02 09:59:43 | INFO     | __init__             | 185  | 🚀 STRATEGY_START: LightNewCoinBreakout Enhanced Logging Version
2025-10-02 09:59:43 | INFO     | __init__             | 186  | 📅 START_TIME: 2025-10-02 01:59:43 UTC
2025-10-02 09:59:43 | INFO     | __init__             | 187  | 📊 LOG_LEVEL: INFO
2025-10-02 09:59:43 | INFO     | __init__             | 188  | 📁 LOG_FILE: logs/strategy_enhanced.log
2025-10-02 09:59:43 | INFO     | __init__             | 191  | 🎯 STARTUP_COIN_SELECTION: Executing initial coin selection
2025-10-02 09:59:43 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: pick()
2025-10-02 09:59:43 | INFO     | pick                 | 273  | 🔍 COIN_SELECTION: Starting daily coin selection process
2025-10-02 09:59:43 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/ticker/24hr, params={})
2025-10-02 10:00:04 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/ticker/24hr
2025-10-02 10:00:04 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 10:00:04 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.073s
2025-10-02 10:00:04 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in pick: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 10:00:04 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 275, in pick
    tickers = get('/fapi/v1/ticker/24hr')
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/ticker/24hr (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E10ED0>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 10:00:05 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  pick | Duration: 21.120s
2025-10-02 10:00:05 | WARNING  | __init__             | 197  | ⚠️ STARTUP_PICK_FAILED: No suitable coin found at startup
2025-10-02 10:00:05 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 10:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: run()
2025-10-02 10:00:05 | INFO     | run                  | 700  | 🚀 STRATEGY_MAIN_LOOP: LightNewCoinBreakout Fixed Version start
2025-10-02 10:00:05 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 10:00:05 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 02:00 UTC
2025-10-02 10:00:05 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.009s
2025-10-02 10:00:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: reset_position()
2025-10-02 10:00:35 | INFO     | reset_position       | 523  | 🔄 POSITION_RESET: Clearing all position data
2025-10-02 10:00:35 | INFO     | log_state_change     | 90   | 🔄 STATE_CHANGE: position_state | {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} → {'symbol': None, 'entry': None, 'qty': None, 'stop_order_id': None, 'take_profit_order_id': None, 'position_opened_time': None, 'max_profit': 0} | Context: position reset
2025-10-02 10:00:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  reset_position | Result: completed | Duration: 0.004s
2025-10-02 10:00:35 | INFO     | log_status_summary   | 109  | 📊 STATUS_SUMMARY: {'symbol': None, 'entry_price': None, 'quantity': None, 'stop_order_id': None, 'take_profit_order_id': None, 'max_profit': 0, 'position_opened_time': None}
2025-10-02 10:00:35 | INFO     | check_manual_intervention | 492  | 🎯 MANUAL_INTERVENTION: Symbol changed from None to BTCUSDT
2025-10-02 10:00:35 | INFO     | log_state_change     | 90   | 🔄 STATE_CHANGE: symbol | None → BTCUSDT | Context: manual intervention
2025-10-02 10:00:35 | INFO     | check_manual_intervention | 497  | 🗑️ MANUAL_FILE_REMOVED: manual_symbol.txt deleted after processing
2025-10-02 10:00:35 | INFO     | run                  | 709  | 🎯 MANUAL_INTERVENTION_PROCESSED: Symbol changed by manual intervention
2025-10-02 10:00:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: daily()
2025-10-02 10:00:35 | INFO     | daily                | 412  | ⏰ DAILY_CHECK: Current time 02:00 UTC
2025-10-02 10:00:35 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  daily | Result: completed | Duration: 0.004s
2025-10-02 10:00:35 | INFO     | run                  | 728  | 🔍 BREAKOUT_CHECK: Analyzing BTCUSDT for breakout signals
2025-10-02 10:00:35 | INFO     | log_function_entry   | 73   | 🔵 ENTRY: API_GET(path=/fapi/v1/klines, params={'symbol': 'BTCUSDT', 'interval': '3m', 'limit': 21})
2025-10-02 10:00:56 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in API_GET: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: URL: https://fapi.binance.com/fapi/v1/klines
2025-10-02 10:00:56 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 10:00:56 | INFO     | log_function_exit    | 79   | 🔴 EXIT:  API_GET | Result: FAILED | Duration: 21.055s
2025-10-02 10:00:56 | ERROR    | log_exception        | 84   | ❌ EXCEPTION in main_loop: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')) | Context: symbol=BTCUSDT
2025-10-02 10:00:56 | ERROR    | log_exception        | 85   | 📋 TRACEBACK:
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 95, in create_connection
    raise err
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 714, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 403, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 1053, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\connectionpool.py", line 798, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 730, in run
    kl = get('/fapi/v1/klines', {'symbol': self.symbol, 'interval': '3m', 'limit': 21})
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\allmac\strategy\maker_channel_light_fixed.py", line 120, in get
    r = session.get(url).json()
        ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\requests\adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))

2025-10-02 10:00:56 | ERROR    | run                  | 855  | ❌ MAIN_LOOP_ERROR: HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/klines?symbol=BTCUSDT&interval=3m&limit=21 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EA62E7D410>, 'Connection to fapi.binance.com timed out. (connect timeout=None)'))
2025-10-02 10:00:56 | INFO     | run                  | 856  | ⏳ ERROR_RECOVERY: Sleeping 60s before retry

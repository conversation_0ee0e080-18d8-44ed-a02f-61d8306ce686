"""
五重风控机制基础集成示例
Basic Integration Example for Five-Layer Risk Control

演示如何将风控系统集成到现有策略中
"""

import logging
import time
import pandas as pd
from typing import Dict, Any, Optional

# 导入风控模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from five_layer_risk_control import (
    FiveLayerRiskControl, 
    RiskConfig, 
    RiskControlIntegrator,
    MarketDataCalculator
)


class SimpleStrategy:
    """
    简单策略示例 - 演示风控集成
    """
    
    def __init__(self):
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.log = logging.getLogger(__name__)
        
        # 模拟的持仓信息
        self.pos = None
        
        # 模拟的市场数据
        self.market_data_cache = {}
        
        self.log.info("简单策略初始化完成")
    
    def get_klines(self, symbol: str, interval: str, limit: int) -> pd.DataFrame:
        """模拟获取K线数据"""
        # 这里应该是真实的API调用
        # 为了演示，我们生成模拟数据
        import numpy as np
        
        dates = pd.date_range(start='2024-01-01', periods=limit, freq='15T')
        base_price = 50000.0 if 'BTC' in symbol else 3000.0
        
        # 生成模拟价格数据
        np.random.seed(42)  # 确保可重复
        price_changes = np.random.normal(0, 0.02, limit)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        df = pd.DataFrame({
            'o': prices,
            'h': [p * 1.01 for p in prices],  # 高价
            'l': [p * 0.99 for p in prices],  # 低价
            'c': prices,                      # 收盘价
            'v': np.random.uniform(100, 1000, limit)  # 成交量
        }, index=dates)
        
        return df
    
    def get_depth01pct(self, symbol: str) -> float:
        """模拟获取市场深度"""
        # 模拟深度数据
        if 'BTC' in symbol:
            return 150000.0
        else:
            return 80000.0
    
    def place_maker_order(self, symbol: str, side: str, quantity: float, price: float):
        """模拟下单"""
        self.log.info(f"模拟下单: {side} {quantity:.6f} {symbol} @ {price:.2f}")
        return {'orderId': f"mock_{int(time.time())}", 'status': 'FILLED'}
    
    def update_stop_loss(self, symbol: str, stop_price: float):
        """模拟更新止损"""
        self.log.info(f"模拟更新止损: {symbol} 止损价 → {stop_price:.6f}")
        if self.pos:
            self.pos['stop'] = stop_price
    
    def close_position(self, symbol: str, price: float):
        """模拟平仓"""
        if self.pos:
            qty = self.pos['qty']
            self.place_maker_order(symbol, 'SELL', qty, price * 0.999)
            self.log.info(f"模拟平仓: {symbol} 数量:{qty:.6f} 价格:{price:.2f}")
            self.pos = None
    
    def open_position(self, symbol: str, price: float, quantity: float):
        """模拟开仓"""
        self.pos = {
            'symbol': symbol,
            'entry': price,
            'qty': quantity,
            'original_nominal': price * quantity,
            'breakeven': 0,
            'trail_stop': 0,
            'stop': price * 0.97,  # 默认3%止损
            'exit_pct': 0
        }
        self.place_maker_order(symbol, 'BUY', quantity, price * 1.001)
        self.log.info(f"模拟开仓: {symbol} @{price:.2f} 数量:{quantity:.6f}")


def example_manual_integration():
    """示例1：手动集成风控系统"""
    print("\n" + "="*60)
    print("示例1：手动集成风控系统")
    print("="*60)
    
    # 创建策略实例
    strategy = SimpleStrategy()
    
    # 创建风控配置
    config = RiskConfig(
        breakeven_trigger_pct=0.05,    # 5%触发保本
        trailing_trigger_pct=0.02,     # 2%启用移动止损
        max_stops_per_period=2,        # 4小时内最多2次止损
        freeze_hours=4                 # 冷冻4小时
    )
    
    # 初始化风控系统
    risk_control = FiveLayerRiskControl(config, strategy.log)
    
    # 模拟交易流程
    symbol = 'BTCUSDT'
    entry_price = 50000.0
    quantity = 0.1
    nominal_value = entry_price * quantity
    
    # 1. 开仓前检查
    if risk_control.can_open_position(symbol):
        strategy.log.info(f"✓ {symbol} 通过止血贴检查，可以开仓")
        
        # 2. 开仓
        strategy.open_position(symbol, entry_price, quantity)
        risk_control.set_position(symbol, entry_price, quantity, nominal_value)
        
        # 3. 模拟价格变化和风控监控
        test_prices = [50000, 51000, 52500, 54000, 52000, 50500]  # 模拟价格序列
        
        for current_price in test_prices:
            strategy.log.info(f"\n--- 价格更新: {current_price} ---")
            
            # 准备市场数据
            df = strategy.get_klines(symbol, '15m', 100)
            market_data = MarketDataCalculator.prepare_market_data(df)
            if market_data:
                market_data['depth'] = strategy.get_depth01pct(symbol)
                
                # 执行风控监控
                result = risk_control.monitor_position(symbol, current_price, market_data)
                
                # 处理风控决策
                if result['action'] == 'stop_loss':
                    strategy.log.info(f"🛑 触发止损: {result['stop_type']}")
                    strategy.close_position(symbol, current_price)
                    risk_control.log_stop_loss(symbol, result['stop_type'])
                    risk_control.clear_position()
                    break
                
                # 处理其他风控动作
                for detail in result.get('details', []):
                    if detail['type'] == 'breakeven':
                        strategy.update_stop_loss(symbol, detail['stop_price'])
                    elif detail['type'] == 'trailing':
                        strategy.update_stop_loss(symbol, detail['stop_price'])
                    elif detail['type'] == 'scaling':
                        action = detail['action']
                        qty = detail['quantity']
                        if action == 'downgrade':
                            strategy.log.info(f"📉 降档: 卖出{qty:.6f}")
                        elif action == 'reset':
                            strategy.log.info(f"📈 复位: 买回{qty:.6f}")
            
            time.sleep(0.1)  # 模拟时间间隔
    else:
        strategy.log.warning(f"❌ {symbol} 被止血贴冷冻，无法开仓")


def example_integrator_usage():
    """示例2：使用集成器快速集成"""
    print("\n" + "="*60)
    print("示例2：使用集成器快速集成")
    print("="*60)
    
    # 创建策略实例
    strategy = SimpleStrategy()
    
    # 使用集成器快速集成风控
    integrator = RiskControlIntegrator(strategy)
    
    # 模拟交易
    symbol = 'ETHUSDT'
    entry_price = 3000.0
    quantity = 1.0
    
    # 开仓
    strategy.open_position(symbol, entry_price, quantity)
    integrator.set_position(symbol, entry_price, quantity, entry_price * quantity)
    
    # 模拟价格变化（集成器会自动处理风控）
    test_prices = [3000, 3150, 3300, 3100, 2900]
    
    for current_price in test_prices:
        strategy.log.info(f"\n--- 价格更新: {current_price} ---")
        
        # 集成器会自动调用增强的monitor_position方法
        if hasattr(strategy, 'monitor_position'):
            strategy.monitor_position(symbol, current_price)
        
        time.sleep(0.1)


def example_stop_loss_limiter():
    """示例3：止血贴机制演示"""
    print("\n" + "="*60)
    print("示例3：止血贴机制演示")
    print("="*60)
    
    strategy = SimpleStrategy()
    config = RiskConfig(max_stops_per_period=2, freeze_hours=4)
    risk_control = FiveLayerRiskControl(config, strategy.log)
    
    symbol = 'ADAUSDT'
    
    # 模拟多次止损
    for i in range(5):
        strategy.log.info(f"\n--- 第{i+1}次尝试开仓 ---")
        
        if risk_control.can_open_position(symbol):
            strategy.log.info(f"✓ 可以开仓")
            
            # 模拟开仓后立即止损
            risk_control.log_stop_loss(symbol, f'test_stop_{i+1}')
            
        else:
            strategy.log.warning(f"❌ 被止血贴冷冻，无法开仓")
            break


def example_top1_selection():
    """示例4：Top1选择机制演示"""
    print("\n" + "="*60)
    print("示例4：Top1选择机制演示")
    print("="*60)
    
    strategy = SimpleStrategy()
    config = RiskConfig(score_threshold=7.0)
    risk_control = FiveLayerRiskControl(config, strategy.log)
    
    # 模拟不同币种的评分
    candidates = [
        ('BTCUSDT', 8.5),
        ('ETHUSDT', 7.8),
        ('ADAUSDT', 6.2),  # 低于门槛
        ('SOLUSDT', 9.1),  # 最高分
        ('DOTUSDT', 7.5)
    ]
    
    for symbol, score in candidates:
        strategy.log.info(f"\n--- 评估 {symbol} (评分: {score}) ---")
        
        changed = risk_control.update_top_selection(symbol, score)
        if changed:
            strategy.log.info(f"🏆 Top1选择已更新")
        
        current_top = risk_control.selected_symbol
        strategy.log.info(f"当前Top1: {current_top}")


if __name__ == "__main__":
    print("五重风控机制集成示例")
    print("Five-Layer Risk Control Integration Examples")
    
    # 运行所有示例
    example_manual_integration()
    example_integrator_usage()
    example_stop_loss_limiter()
    example_top1_selection()
    
    print("\n" + "="*60)
    print("所有示例运行完成！")
    print("="*60)

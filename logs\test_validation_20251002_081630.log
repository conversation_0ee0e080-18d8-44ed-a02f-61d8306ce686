2025-10-02 08:16:30,042 - TestValidation - INFO - 🚀 开始全面测试验证修复效果
2025-10-02 08:16:30,043 - TestValidation - INFO - 🎯 开始运行全面测试验证
2025-10-02 08:16:30,044 - TestValidation - INFO - 
============================================================
2025-10-02 08:16:30,044 - TestValidation - INFO - 📋 测试1: 交易规则获取功能
2025-10-02 08:16:30,044 - TestValidation - INFO -   测试符号: BTCUSDT
2025-10-02 08:16:30,045 - TestValidation - INFO -     ✅ BTCUSDT 规则获取成功: {'tick_size': 0.01, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:16:30,046 - TestValidation - INFO -   测试符号: ETHUSDT
2025-10-02 08:16:30,047 - TestValidation - INFO -     ✅ ETHUSDT 规则获取成功: {'tick_size': 0.01, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:16:30,047 - TestValidation - INFO -   测试符号: PLAYUSDT
2025-10-02 08:16:30,048 - TestValidation - INFO -     ✅ PLAYUSDT 规则获取成功: {'tick_size': 1e-05, 'step_size': 1.0, 'min_qty': 1.0, 'min_notional': 5.0}
2025-10-02 08:16:30,049 - TestValidation - INFO -   测试符号: PROVEUSDT
2025-10-02 08:16:30,049 - TestValidation - INFO -     ✅ PROVEUSDT 规则获取成功: {'tick_size': 0.0001, 'step_size': 1.0, 'min_qty': 1.0, 'min_notional': 5.0}
2025-10-02 08:16:30,050 - TestValidation - INFO -   测试符号: PIPPINUSDT
2025-10-02 08:16:30,051 - TestValidation - INFO -     ✅ PIPPINUSDT 规则获取成功: {'tick_size': 1e-05, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:16:30,051 - TestValidation - INFO -   测试符号: ADAUSDT
2025-10-02 08:16:30,052 - TestValidation - ERROR -     ❌ ADAUSDT 规则获取失败
2025-10-02 08:16:30,052 - TestValidation - INFO -   测试符号: DOTUSDT
2025-10-02 08:16:30,053 - TestValidation - ERROR -     ❌ DOTUSDT 规则获取失败
2025-10-02 08:16:30,057 - TestValidation - INFO - 📊 交易规则获取测试完成 - 成功率: 71.4%
2025-10-02 08:16:30,058 - TestValidation - INFO - 
============================================================
2025-10-02 08:16:30,058 - TestValidation - INFO - 🔢 测试2: 精度处理功能
2025-10-02 08:16:30,059 - TestValidation - ERROR -     ❌ 测试用例 1 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_price'
2025-10-02 08:16:30,059 - TestValidation - ERROR -     ❌ 测试用例 2 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_price'
2025-10-02 08:16:30,060 - TestValidation - ERROR -     ❌ 测试用例 3 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_price'
2025-10-02 08:16:30,060 - TestValidation - ERROR -     ❌ 测试用例 4 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_price'
2025-10-02 08:16:30,060 - TestValidation - ERROR -     ❌ 测试用例 5 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_qty'
2025-10-02 08:16:30,061 - TestValidation - ERROR -     ❌ 测试用例 6 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_qty'
2025-10-02 08:16:30,061 - TestValidation - ERROR -     ❌ 测试用例 7 异常: 'EmergencyTradingRulesFix' object has no attribute '_round_qty'
2025-10-02 08:16:30,061 - TestValidation - INFO - 📊 精度处理测试完成 - 成功率: 0.0%
2025-10-02 08:16:30,062 - TestValidation - INFO - 
============================================================
2025-10-02 08:16:30,062 - TestValidation - INFO - 📊 测试3: 监控系统功能
2025-10-02 08:16:30,063 - TestValidation - INFO -   测试监控组件1: 交易规则事件记录
2025-10-02 08:16:30,104 - TestValidation - INFO -     ✅ 交易规则事件记录正常
2025-10-02 08:16:30,105 - TestValidation - INFO -   测试监控组件2: 订单执行事件记录
2025-10-02 08:16:30,108 - TestValidation - INFO -     ✅ 订单执行事件记录正常
2025-10-02 08:16:30,108 - TestValidation - INFO -   测试监控组件3: 错误模式分析
2025-10-02 08:16:30,129 - TestValidation - ERROR -     ❌ 错误模式分析失败: 'SystemMonitoringEnhanced' object has no attribute 'analyze_error_patterns'
2025-10-02 08:16:30,131 - TestValidation - INFO -   测试监控组件4: 健康报告生成
2025-10-02 08:16:30,131 - TestValidation - ERROR -     ❌ 健康报告生成失败：缺少必要字段
2025-10-02 08:16:35,147 - TestValidation - INFO - 📊 监控系统测试完成 - 成功率: 50.0%
2025-10-02 08:16:35,147 - TestValidation - INFO - 
============================================================
2025-10-02 08:16:35,147 - TestValidation - INFO - 🔄 测试4: 智能重试机制
2025-10-02 08:16:35,147 - TestValidation - INFO -     ✅ 错误码 -1111, 尝试 1: refresh_rules
2025-10-02 08:16:35,147 - TestValidation - INFO -     ✅ 错误码 -1022, 尝试 1: refresh_timestamp
2025-10-02 08:16:35,147 - TestValidation - INFO -     ✅ 错误码 -2010, 尝试 1: normal_retry
2025-10-02 08:16:35,155 - TestValidation - INFO -     ✅ 错误码 -1111, 尝试 3: skip
2025-10-02 08:16:35,156 - TestValidation - INFO -     ✅ 错误码 -9999, 尝试 1: normal_retry
2025-10-02 08:16:35,157 - TestValidation - INFO - 📊 智能重试机制测试完成 - 成功率: 100.0%
2025-10-02 08:16:35,158 - TestValidation - INFO - 
============================================================
2025-10-02 08:16:35,176 - TestValidation - INFO - 🔗 测试5: 集成兼容性
2025-10-02 08:16:35,176 - TestValidation - INFO -     ✅ 紧急修复模块导入成功
2025-10-02 08:16:35,176 - TestValidation - INFO -     ✅ 系统监控模块导入成功
2025-10-02 08:16:35,180 - TestValidation - INFO -     ✅ 配置文件访问正常
2025-10-02 08:16:35,187 - TestValidation - INFO -     ✅ 日志目录访问正常
2025-10-02 08:16:35,263 - TestValidation - INFO -     ✅ 策略文件语法检查通过
2025-10-02 08:16:35,263 - TestValidation - INFO - 📊 集成兼容性测试完成 - 成功率: 100.0%
2025-10-02 08:16:35,263 - TestValidation - INFO - 📄 测试结果已保存到: logs/test_validation_results_20251002_081635.json
2025-10-02 08:16:35,263 - TestValidation - INFO - 
================================================================================
2025-10-02 08:16:35,263 - TestValidation - INFO - 🎯 全面测试验证总结报告
2025-10-02 08:16:35,263 - TestValidation - INFO - ================================================================================
2025-10-02 08:16:35,273 - TestValidation - INFO - 📊 总体统计:
2025-10-02 08:16:35,273 - TestValidation - INFO -    总测试数: 28
2025-10-02 08:16:35,273 - TestValidation - INFO -    成功数: 17
2025-10-02 08:16:35,274 - TestValidation - INFO -    失败数: 11
2025-10-02 08:16:35,274 - TestValidation - INFO -    成功率: 60.7%
2025-10-02 08:16:35,274 - TestValidation - INFO -    总体状态: FAILED
2025-10-02 08:16:35,274 - TestValidation - INFO - 
📋 各项测试详情:
2025-10-02 08:16:35,275 - TestValidation - INFO -    ❌ 交易规则获取: 71.4%
2025-10-02 08:16:35,275 - TestValidation - INFO -    ❌ 精度处理: 0.0%
2025-10-02 08:16:35,275 - TestValidation - INFO -    ❌ 监控系统: 50.0%
2025-10-02 08:16:35,275 - TestValidation - INFO -    ✅ 智能重试机制: 100.0%
2025-10-02 08:16:35,275 - TestValidation - INFO -    ✅ 集成兼容性: 100.0%
2025-10-02 08:16:35,276 - TestValidation - INFO - 
💡 建议:
2025-10-02 08:16:35,276 - TestValidation - INFO -    ⚠️ 修复方案需要进一步优化，请检查失败的测试项
2025-10-02 08:16:35,276 - TestValidation - INFO - ================================================================================

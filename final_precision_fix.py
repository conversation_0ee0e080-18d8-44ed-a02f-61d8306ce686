#!/usr/bin/env python3
"""
最终精度修复方案
解决PIPPINUSDT下单时价格被格式化为0的根本问题
"""

from decimal import Decimal, ROUND_DOWN

def analyze_root_cause():
    """分析根本原因"""
    print("=== 根本原因分析 ===")
    
    # 模拟PIPPINUSDT的实际情况
    price = 0.4
    tick_size = 0.00001  # 1e-05
    
    print(f"价格: {price}")
    print(f"tick_size: {tick_size}")
    
    # 计算向下取整
    decimal_value = Decimal(str(price))
    decimal_step = Decimal(str(tick_size))
    
    print(f"decimal_value: {decimal_value}")
    print(f"decimal_step: {decimal_step}")
    
    # 计算步数
    steps = decimal_value / decimal_step
    print(f"steps (price/tick_size): {steps}")
    
    # 向下取整
    steps_rounded = steps.quantize(Decimal('1'), rounding=ROUND_DOWN)
    print(f"steps_rounded: {steps_rounded}")
    
    # 计算最终价格
    rounded = steps_rounded * decimal_step
    print(f"rounded price: {rounded}")
    
    # 这里是问题所在！0.4 / 0.00001 = 40000，但是由于浮点精度问题...
    print(f"float(rounded): {float(rounded)}")
    
    # 检查是否为0
    if rounded == 0:
        print("❌ 价格被错误地格式化为0！")
    else:
        print("✅ 价格格式化正常")

def create_robust_fix():
    """创建健壮的修复方案"""
    print("\n=== 健壮的修复方案 ===")
    
    def format_order_params_robust(price, qty, tick_size, step_size, min_qty):
        """健壮的下单参数格式化函数"""
        from decimal import Decimal, ROUND_DOWN, getcontext
        
        # 设置足够的精度
        getcontext().prec = 28
        
        def safe_format_to_precision(value, precision_step, param_name=""):
            """安全的精度格式化函数"""
            if not precision_step or precision_step <= 0:
                return str(value)
            
            try:
                # 转换为Decimal，确保精度
                decimal_value = Decimal(str(value))
                decimal_step = Decimal(str(precision_step))
                
                # 检查输入值
                if decimal_value <= 0:
                    return str(value)
                
                # 计算步数
                steps = decimal_value / decimal_step
                
                # 向下取整到最近的步长倍数
                steps_rounded = steps.quantize(Decimal('1'), rounding=ROUND_DOWN)
                
                # 如果向下取整后为0，但原值大于0，则设为1步
                if steps_rounded == 0 and decimal_value > 0:
                    steps_rounded = Decimal('1')
                
                # 计算最终值
                rounded = steps_rounded * decimal_step
                
                # 计算小数位数
                step_str = str(precision_step)
                if '.' in step_str:
                    decimal_places = len(step_str.split('.')[1])
                else:
                    decimal_places = 0
                
                # 格式化输出
                if decimal_places > 0:
                    # 使用固定精度格式化
                    formatted = f"{float(rounded):.{decimal_places}f}"
                    # 去除不必要的末尾0，但保留至少一位有效数字
                    if '.' in formatted:
                        formatted = formatted.rstrip('0')
                        if formatted.endswith('.'):
                            formatted = formatted[:-1]
                        # 确保不为空
                        if not formatted:
                            formatted = f"{float(rounded):.{decimal_places}f}"
                    return formatted
                else:
                    return str(int(float(rounded)))
                    
            except Exception as e:
                print(f"格式化{param_name}失败: {e}, 使用原值")
                return str(value)
        
        try:
            # 格式化价格
            price_str = safe_format_to_precision(price, tick_size, "价格")
            
            # 格式化数量，确保不小于最小数量
            actual_qty = qty
            if min_qty and float(qty) < float(min_qty):
                actual_qty = min_qty
            qty_str = safe_format_to_precision(actual_qty, step_size, "数量")
            
            # 最终安全检查
            try:
                price_val = float(price_str)
                qty_val = float(qty_str)
                
                # 确保价格和数量都大于0
                if price_val <= 0:
                    print(f"警告：价格格式化异常 {price} -> {price_str}，使用原值")
                    price_str = str(float(price))
                
                if qty_val <= 0:
                    print(f"警告：数量格式化异常 {qty} -> {qty_str}，使用原值")
                    qty_str = str(float(actual_qty))
                    
            except ValueError:
                print(f"警告：格式化结果无法转换为数字，使用原值")
                price_str = str(float(price))
                qty_str = str(float(actual_qty))
            
            return price_str, qty_str
            
        except Exception as e:
            print(f"格式化失败: {e}，使用原值")
            return str(float(price)), str(float(qty))
    
    return format_order_params_robust

def test_robust_fix():
    """测试健壮的修复方案"""
    print("\n=== 测试健壮修复方案 ===")
    
    format_func = create_robust_fix()
    
    # 测试用例
    test_cases = [
        # (price, qty, tick_size, step_size, min_qty, description)
        (0.4, 236.417, 0.00001, 0.001, 0.001, "PIPPINUSDT实际参数"),
        (0.00005, 1000.0, 0.00001, 0.001, 0.001, "极小价格"),
        (0.000001, 5000000.0, 0.00001, 0.001, 0.001, "超小价格"),
        (1.23456, 50.0, 0.0001, 0.01, 0.01, "正常价格"),
        (0.123456789, 100.0, 0.0001, 0.01, 0.01, "高精度价格"),
    ]
    
    for price, qty, tick_size, step_size, min_qty, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: price={price}, qty={qty}")
        print(f"规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}")
        
        price_str, qty_str = format_func(price, qty, tick_size, step_size, min_qty)
        print(f"输出: price_str='{price_str}', qty_str='{qty_str}'")
        
        # 验证结果
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            notional = price_val * qty_val
            
            print(f"验证: price_val={price_val}, qty_val={qty_val}")
            print(f"名义价值: {notional:.6f} USDT")
            
            # 检查精度
            if '.' in price_str:
                price_decimals = len(price_str.split('.')[1])
                tick_decimals = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
                print(f"价格精度: {price_decimals} (要求≤{tick_decimals})")
                
                if price_decimals <= tick_decimals:
                    print("✅ 价格精度正确")
                else:
                    print("❌ 价格精度超限")
            
            if '.' in qty_str:
                qty_decimals = len(qty_str.split('.')[1])
                step_decimals = len(str(step_size).split('.')[1]) if '.' in str(step_size) else 0
                print(f"数量精度: {qty_decimals} (要求≤{step_decimals})")
                
                if qty_decimals <= step_decimals:
                    print("✅ 数量精度正确")
                else:
                    print("❌ 数量精度超限")
            
            # 检查值的有效性
            if price_val > 0 and qty_val > 0 and notional >= 5.0:
                print("✅ 所有检查通过，应该可以成功下单")
            else:
                issues = []
                if price_val <= 0:
                    issues.append("价格≤0")
                if qty_val <= 0:
                    issues.append("数量≤0")
                if notional < 5.0:
                    issues.append("名义价值<5")
                print(f"⚠️  存在问题: {', '.join(issues)}")
                
        except Exception as e:
            print(f"❌ 验证失败: {e}")

def generate_final_code():
    """生成最终的修复代码"""
    print("\n=== 最终修复代码 ===")
    
    final_code = '''
def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
    """健壮的下单参数格式化，修复价格为0的bug"""
    from decimal import Decimal, ROUND_DOWN, getcontext
    
    # 设置足够的精度
    getcontext().prec = 28
    
    def safe_format_to_precision(value, precision_step, param_name=""):
        """安全的精度格式化函数"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 转换为Decimal，确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 检查输入值
            if decimal_value <= 0:
                return str(value)
            
            # 计算步数
            steps = decimal_value / decimal_step
            
            # 向下取整到最近的步长倍数
            steps_rounded = steps.quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 关键修复：如果向下取整后为0，但原值大于0，则设为1步
            if steps_rounded == 0 and decimal_value > 0:
                steps_rounded = Decimal('1')
            
            # 计算最终值
            rounded = steps_rounded * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出
            if decimal_places > 0:
                # 使用固定精度格式化
                formatted = f"{float(rounded):.{decimal_places}f}"
                # 去除不必要的末尾0，但保留至少一位有效数字
                if '.' in formatted:
                    formatted = formatted.rstrip('0')
                    if formatted.endswith('.'):
                        formatted = formatted[:-1]
                    # 确保不为空
                    if not formatted:
                        formatted = f"{float(rounded):.{decimal_places}f}"
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            self.log.warning(f"格式化{param_name}失败: {e}, 使用原值")
            return str(value)
    
    try:
        # 格式化价格
        price_str = safe_format_to_precision(price, tick_size, "价格")
        
        # 格式化数量，确保不小于最小数量
        actual_qty = qty
        if min_qty and float(qty) < float(min_qty):
            actual_qty = min_qty
        qty_str = safe_format_to_precision(actual_qty, step_size, "数量")
        
        # 最终安全检查
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            
            # 确保价格和数量都大于0
            if price_val <= 0:
                self.log.warning(f"价格格式化异常 {price} -> {price_str}，使用原值")
                price_str = str(float(price))
            
            if qty_val <= 0:
                self.log.warning(f"数量格式化异常 {qty} -> {qty_str}，使用原值")
                qty_str = str(float(actual_qty))
                
        except ValueError:
            self.log.warning(f"格式化结果无法转换为数字，使用原值")
            price_str = str(float(price))
            qty_str = str(float(actual_qty))
        
        return price_str, qty_str
        
    except Exception as e:
        self.log.error(f"格式化失败: {e}，使用原值")
        return str(float(price)), str(float(qty))
'''
    
    print("最终修复代码：")
    print(final_code)
    
    return final_code

if __name__ == '__main__':
    analyze_root_cause()
    test_robust_fix()
    generate_final_code()
    
    print("\n=== 修复总结 ===")
    print("1. 根本问题：向下取整时，小于1个tick_size的价格被格式化为0")
    print("2. 修复方案：确保格式化后的值至少为1个精度步长")
    print("3. 安全措施：添加多层验证和回退机制")
    print("4. 建议：立即应用此修复到maker_channel_enhanced.py")
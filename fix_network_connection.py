#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接修复脚本
解决代理连接失败和服务器时间同步异常问题
"""

import requests
import time
import json
import yaml
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

def load_config():
    """加载配置文件"""
    try:
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 成功加载 config.yaml")
        return config
    except Exception as e:
        print(f"❌ 加载 config.yaml 失败: {e}")
        try:
            with open('config/config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 成功加载 config.json")
            return config
        except Exception as e2:
            print(f"❌ 加载 config.json 失败: {e2}")
            return None

def test_direct_connection():
    """测试直连币安API"""
    print("\n🔍 测试直连币安API...")
    
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    try:
        response = session.get('https://fapi.binance.com/fapi/v1/time', timeout=10)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            time_diff = abs(server_time - local_time)
            print(f"✅ 直连成功")
            print(f"   服务器时间: {server_time}")
            print(f"   本地时间: {local_time}")
            print(f"   时间差: {time_diff}ms")
            return True, time_diff
        else:
            print(f"❌ 直连失败，状态码: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ 直连异常: {e}")
        return False, None

def test_proxy_connection(proxy_url):
    """测试代理连接币安API"""
    print(f"\n🔍 测试代理连接币安API: {proxy_url}")
    
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    session = requests.Session()
    session.proxies.update(proxies)
    
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    try:
        response = session.get('https://fapi.binance.com/fapi/v1/time', timeout=15)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            time_diff = abs(server_time - local_time)
            print(f"✅ 代理连接成功")
            print(f"   服务器时间: {server_time}")
            print(f"   本地时间: {local_time}")
            print(f"   时间差: {time_diff}ms")
            return True, time_diff
        else:
            print(f"❌ 代理连接失败，状态码: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ 代理连接异常: {e}")
        return False, None

def test_alternative_proxies():
    """测试备用代理"""
    print("\n🔍 测试备用代理...")
    
    # 常见的本地代理端口
    proxy_ports = [7890, 7891, 7892, 7893, 7894, 7895, 7896, 7897, 1080, 8080]
    
    for port in proxy_ports:
        proxy_url = f"http://127.0.0.1:{port}"
        print(f"\n   测试代理: {proxy_url}")
        
        try:
            # 快速测试代理是否可用
            proxies = {'http': proxy_url, 'https': proxy_url}
            response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 代理 {proxy_url} 可用")
                # 测试币安连接
                success, time_diff = test_proxy_connection(proxy_url)
                if success:
                    return proxy_url, time_diff
            else:
                print(f"   ❌ 代理 {proxy_url} 不可用")
        except Exception as e:
            print(f"   ❌ 代理 {proxy_url} 测试失败: {e}")
    
    return None, None

def generate_http_client_fix(best_proxy=None, time_offset=0):
    """生成HttpClient修复代码"""
    print("\n📝 生成HttpClient修复代码...")
    
    fix_code = '''
# HttpClient网络连接修复代码
# 将以下代码添加到http_client.py的__init__方法中

def __init__(self, api_key, api_secret, base_url='https://fapi.binance.com', proxy_url=None, force_proxy=False):
    self.api_key = api_key
    self.api_secret = api_secret
    self.base_url = base_url
    self.server_time_offset = 0
    self.last_sync_time = 0
    
    # 网络连接修复
    self.session = requests.Session()
    
    # 设置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    self.session.mount("http://", adapter)
    self.session.mount("https://", adapter)
    
    # 代理配置修复
'''
    
    if best_proxy:
        fix_code += f'''    # 使用测试成功的代理
    self.proxy_url = "{best_proxy}"
    self.session.proxies.update({{
        'http': self.proxy_url,
        'https': self.proxy_url
    }})
    print(f"[HttpClient] 使用代理: {{self.proxy_url}}")
'''
    else:
        fix_code += '''    # 直连模式
    self.proxy_url = None
    print("[HttpClient] 使用直连模式")
'''
    
    if time_offset:
        fix_code += f'''    
    # 时间偏移修复
    self.server_time_offset = {time_offset}
    print(f"[HttpClient] 设置时间偏移: {{self.server_time_offset}}ms")
'''
    
    fix_code += '''
    # 同步服务器时间（修复版）
    self.sync_server_time_fixed()

def sync_server_time_fixed(self):
    """修复版服务器时间同步"""
    try:
        response = self.session.get(f'{self.base_url}/fapi/v1/time', timeout=15)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            self.last_sync_time = time.time()
            print(f"[HttpClient] 服务器时间同步成功，偏移: {self.server_time_offset}ms")
            return True
        else:
            print(f"[HttpClient] 服务器时间同步失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"[HttpClient] 服务器时间同步异常: {e}")
        # 使用保守的时间戳策略
        self.server_time_offset = 1000  # 1秒的安全边距
        print(f"[HttpClient] 使用保守时间戳策略，偏移: {self.server_time_offset}ms")
        return False

def get_server_time(self):
    """获取服务器时间（修复版）"""
    # 如果距离上次同步超过5分钟，重新同步
    if time.time() - self.last_sync_time > 300:
        self.sync_server_time_fixed()
    
    return int(time.time() * 1000) + self.server_time_offset
'''
    
    return fix_code

def main():
    """主函数"""
    print("🚀 网络连接修复脚本启动")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        print("❌ 无法加载配置文件，退出")
        return
    
    # 测试直连
    direct_success, direct_time_diff = test_direct_connection()
    
    # 测试配置中的代理
    proxy_success = False
    proxy_time_diff = None
    config_proxy = config.get('proxy_url')
    
    if config_proxy:
        proxy_success, proxy_time_diff = test_proxy_connection(config_proxy)
    
    # 如果配置的代理失败，测试备用代理
    best_proxy = None
    best_time_diff = None
    
    if not proxy_success:
        best_proxy, best_time_diff = test_alternative_proxies()
    else:
        best_proxy = config_proxy
        best_time_diff = proxy_time_diff
    
    # 选择最佳连接方式
    print("\n" + "=" * 50)
    print("📊 连接测试总结:")
    
    if direct_success:
        print(f"✅ 直连可用，时间差: {direct_time_diff}ms")
    else:
        print("❌ 直连不可用")
    
    if best_proxy:
        print(f"✅ 代理可用: {best_proxy}，时间差: {best_time_diff}ms")
    else:
        print("❌ 代理不可用")
    
    # 选择最佳方案
    if direct_success and (not best_proxy or direct_time_diff < best_time_diff):
        print("\n🎯 推荐使用直连模式")
        fix_code = generate_http_client_fix(None, direct_time_diff)
        recommended_proxy = None
    elif best_proxy:
        print(f"\n🎯 推荐使用代理: {best_proxy}")
        fix_code = generate_http_client_fix(best_proxy, best_time_diff)
        recommended_proxy = best_proxy
    else:
        print("\n⚠️ 所有连接方式都失败，生成保守修复方案")
        fix_code = generate_http_client_fix(None, 1000)  # 1秒安全边距
        recommended_proxy = None
    
    # 保存修复代码
    with open('http_client_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_code)
    
    print(f"\n💾 修复代码已保存到: http_client_fix.py")
    
    # 更新配置文件建议
    print("\n📋 配置文件更新建议:")
    if recommended_proxy:
        print(f"   在config.yaml中设置: proxy_url: '{recommended_proxy}'")
    else:
        print("   在config.yaml中设置: proxy_url: null  # 使用直连")
    
    print("\n🔧 应用修复:")
    print("1. 查看生成的 http_client_fix.py 文件")
    print("2. 将修复代码应用到 http_client.py")
    print("3. 重启策略程序测试")
    
    print("\n✅ 网络连接修复脚本完成")

if __name__ == "__main__":
    main()
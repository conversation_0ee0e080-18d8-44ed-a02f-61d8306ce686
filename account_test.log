2025-09-30 22:41:52,863 [INFO] 开始账户API测试...
2025-09-30 22:41:52,865 [ERROR] 未找到API密钥配置，无法进行账户API测试
2025-09-30 22:42:26,715 [INFO] 开始账户API测试...
2025-09-30 22:42:26,726 [INFO] API Key: nALsAPYz...U6IlI20s
2025-09-30 22:42:26,727 [ERROR] HTTP客户端初始化失败: HttpClient.__init__() got an unexpected keyword argument 'proxy_url'
2025-09-30 22:45:55,831 [INFO] 开始账户API测试...
2025-09-30 22:45:55,838 [INFO] API Key: nALsAPYz...U6IlI20s
2025-09-30 22:45:55,851 [INFO] 强制代理模式: http://127.0.0.1:7897 (本地网络: *************)
2025-09-30 22:46:07,023 [WARNING] 代理连接测试失败: HTTPSConnectionPool(host='httpbin.org', port=443): Read timed out. (read timeout=10)
2025-09-30 22:46:07,063 [WARNING] 代理连接测试失败，尝试直连模式
2025-09-30 22:46:23,919 [WARNING] 同步服务器时间异常 (尝试 1/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C88150>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:46:39,925 [WARNING] 同步服务器时间异常 (尝试 2/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C88B50>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:46:55,935 [WARNING] 同步服务器时间异常 (尝试 3/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C8AFD0>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:46:55,943 [ERROR] 服务器时间同步失败，将使用保守的时间戳策略
2025-09-30 22:46:55,974 [INFO] HTTP客户端初始化成功
2025-09-30 22:46:56,038 [INFO] ============================================================
2025-09-30 22:46:56,143 [INFO] 1. 测试服务器时间同步
2025-09-30 22:46:56,160 [INFO] ============================================================
2025-09-30 22:47:11,218 [WARNING] 同步服务器时间异常 (尝试 1/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C8BE90>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:47:27,231 [WARNING] 同步服务器时间异常 (尝试 2/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C94DD0>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:47:43,245 [WARNING] 同步服务器时间异常 (尝试 3/3): HTTPSConnectionPool(host='fapi.binance.com', port=443): Max retries exceeded with url: /fapi/v1/time (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001C557C95D10>, 'Connection to fapi.binance.com timed out. (connect timeout=15)'))
2025-09-30 22:47:43,252 [ERROR] 服务器时间同步失败，将使用保守的时间戳策略
2025-09-30 22:47:43,253 [ERROR] ✗ 服务器时间同步失败
2025-09-30 22:47:43,254 [INFO] ============================================================
2025-09-30 22:47:43,254 [INFO] 2. 测试账户余额获取
2025-09-30 22:47:43,255 [INFO] ============================================================
2025-09-30 22:47:58,270 [WARNING] 请求超时 (尝试 1/3): https://fapi.binance.com/fapi/v2/account
2025-09-30 22:48:14,281 [WARNING] 请求超时 (尝试 2/3): https://fapi.binance.com/fapi/v2/account
2025-09-30 22:48:31,317 [WARNING] 请求超时 (尝试 3/3): https://fapi.binance.com/fapi/v2/account
2025-09-30 22:48:31,318 [ERROR] 所有重试失败: https://fapi.binance.com/fapi/v2/account
2025-09-30 22:48:31,318 [ERROR] ✗ 账户余额获取失败
2025-09-30 22:48:31,319 [ERROR]   响应内容: None
2025-09-30 22:48:31,321 [INFO] ============================================================
2025-09-30 22:48:31,321 [INFO] 3. 测试获取当前委托
2025-09-30 22:48:31,322 [INFO] ============================================================
2025-09-30 22:48:46,332 [WARNING] 请求超时 (尝试 1/3): https://fapi.binance.com/fapi/v1/openOrders
2025-09-30 22:49:02,369 [WARNING] 请求超时 (尝试 2/3): https://fapi.binance.com/fapi/v1/openOrders
2025-09-30 22:49:19,386 [WARNING] 请求超时 (尝试 3/3): https://fapi.binance.com/fapi/v1/openOrders
2025-09-30 22:49:19,387 [ERROR] 所有重试失败: https://fapi.binance.com/fapi/v1/openOrders
2025-09-30 22:49:19,387 [ERROR] ✗ 当前委托获取失败
2025-09-30 22:49:19,387 [ERROR]   响应内容: None
2025-09-30 22:49:19,389 [INFO] ============================================================
2025-09-30 22:49:19,389 [INFO] 4. 测试交易状态
2025-09-30 22:49:19,390 [INFO] ============================================================
2025-09-30 22:49:34,402 [WARNING] 请求超时 (尝试 1/3): https://fapi.binance.com/fapi/v1/apiTradingStatus
2025-09-30 22:49:50,418 [WARNING] 请求超时 (尝试 2/3): https://fapi.binance.com/fapi/v1/apiTradingStatus
2025-09-30 22:50:07,441 [WARNING] 请求超时 (尝试 3/3): https://fapi.binance.com/fapi/v1/apiTradingStatus
2025-09-30 22:50:07,442 [ERROR] 所有重试失败: https://fapi.binance.com/fapi/v1/apiTradingStatus
2025-09-30 22:50:07,442 [ERROR] ✗ 交易状态获取失败
2025-09-30 22:50:07,442 [ERROR]   响应内容: None
2025-09-30 22:50:07,443 [INFO] ============================================================
2025-09-30 22:50:07,443 [INFO] 账户API测试结果汇总
2025-09-30 22:50:07,443 [INFO] ============================================================
2025-09-30 22:50:07,443 [INFO] server_time         : ✗ 失败
2025-09-30 22:50:07,444 [INFO] account_balance     : ✗ 失败
2025-09-30 22:50:07,444 [INFO] open_orders         : ✗ 失败
2025-09-30 22:50:07,444 [INFO] trading_status      : ✗ 失败
2025-09-30 22:50:07,444 [INFO] 
❌ 所有账户API测试失败
2025-09-30 22:50:07,445 [INFO] 可能的原因:
2025-09-30 22:50:07,445 [INFO] - API密钥配置错误
2025-09-30 22:50:07,445 [INFO] - 代理无法访问币安API
2025-09-30 22:50:07,445 [INFO] - 网络连接问题
2025-09-30 22:50:07,446 [INFO] - API权限不足

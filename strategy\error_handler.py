"""
增强的错误处理和重试机制模块
提供智能错误分类、重试策略和监控功能
"""

import time
import logging
import random
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from functools import wraps


class ErrorType(Enum):
    """错误类型分类"""
    NETWORK = "network"           # 网络错误
    RATE_LIMIT = "rate_limit"     # 限流错误
    INSUFFICIENT_BALANCE = "insufficient_balance"  # 余额不足
    PRECISION = "precision"       # 精度错误
    MARKET_CLOSED = "market_closed"  # 市场关闭
    SYMBOL_NOT_FOUND = "symbol_not_found"  # 交易对不存在
    ORDER_NOT_FOUND = "order_not_found"  # 订单不存在
    PERMISSION = "permission"     # 权限错误
    UNKNOWN = "unknown"          # 未知错误


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    retryable_errors: List[ErrorType] = None
    
    def __post_init__(self):
        if self.retryable_errors is None:
            self.retryable_errors = [
                ErrorType.NETWORK,
                ErrorType.RATE_LIMIT,
                ErrorType.INSUFFICIENT_BALANCE,
                ErrorType.PRECISION
            ]


class ErrorClassifier:
    """错误分类器"""
    
    # Binance API错误码映射
    ERROR_CODE_MAP = {
        # 网络相关
        -1001: ErrorType.NETWORK,  # 内部错误
        -1002: ErrorType.NETWORK,  # 未授权
        -1003: ErrorType.RATE_LIMIT,  # 请求过多
        -1006: ErrorType.NETWORK,  # 意外响应
        -1007: ErrorType.NETWORK,  # 超时
        
        # 余额相关
        -2010: ErrorType.INSUFFICIENT_BALANCE,  # 余额不足
        -2019: ErrorType.INSUFFICIENT_BALANCE,  # 保证金不足
        
        # 精度相关
        -1111: ErrorType.PRECISION,  # 精度超过最大值
        -1013: ErrorType.PRECISION,  # 过滤器失败
        
        # 订单相关
        -2011: ErrorType.ORDER_NOT_FOUND,  # 订单不存在
        -2013: ErrorType.ORDER_NOT_FOUND,  # 订单不存在
        
        # 交易对相关
        -1121: ErrorType.SYMBOL_NOT_FOUND,  # 无效交易对
        
        # 权限相关
        -2015: ErrorType.PERMISSION,  # 无效API密钥
        -1022: ErrorType.PERMISSION,  # 签名无效
    }
    
    @classmethod
    def classify_error(cls, error: Any) -> ErrorType:
        """分类错误类型"""
        if isinstance(error, dict) and 'code' in error:
            code = int(error.get('code', 0))
            return cls.ERROR_CODE_MAP.get(code, ErrorType.UNKNOWN)
        
        if isinstance(error, Exception):
            error_msg = str(error).lower()
            
            # 网络错误关键词
            if any(keyword in error_msg for keyword in [
                'connection', 'timeout', 'network', 'unreachable',
                'dns', 'socket', 'ssl', 'certificate'
            ]):
                return ErrorType.NETWORK
            
            # 限流错误关键词
            if any(keyword in error_msg for keyword in [
                'rate limit', 'too many requests', '429'
            ]):
                return ErrorType.RATE_LIMIT
        
        return ErrorType.UNKNOWN
    
    @classmethod
    def is_retryable(cls, error: Any, retryable_types: List[ErrorType]) -> bool:
        """判断错误是否可重试"""
        error_type = cls.classify_error(error)
        return error_type in retryable_types


class RetryStrategy:
    """重试策略"""
    
    @staticmethod
    def exponential_backoff(attempt: int, config: RetryConfig) -> float:
        """指数退避策略"""
        delay = config.base_delay * (config.backoff_factor ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            # 添加随机抖动，避免雷群效应
            jitter = delay * 0.1 * random.random()
            delay += jitter
        
        return delay
    
    @staticmethod
    def linear_backoff(attempt: int, config: RetryConfig) -> float:
        """线性退避策略"""
        delay = config.base_delay * attempt
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            jitter = delay * 0.1 * random.random()
            delay += jitter
        
        return delay


class ErrorMonitor:
    """错误监控器"""
    
    def __init__(self):
        self.error_stats = {}
        self.recent_errors = []
        self.max_recent_errors = 100
    
    def record_error(self, error_type: ErrorType, error: Any, context: str = ""):
        """记录错误"""
        timestamp = time.time()
        
        # 更新统计
        if error_type not in self.error_stats:
            self.error_stats[error_type] = {
                'count': 0,
                'first_seen': timestamp,
                'last_seen': timestamp
            }
        
        self.error_stats[error_type]['count'] += 1
        self.error_stats[error_type]['last_seen'] = timestamp
        
        # 记录最近错误
        error_record = {
            'timestamp': timestamp,
            'type': error_type,
            'error': str(error),
            'context': context
        }
        
        self.recent_errors.append(error_record)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)
    
    def get_error_rate(self, error_type: ErrorType, window_seconds: int = 300) -> float:
        """获取错误率（每分钟）"""
        current_time = time.time()
        window_start = current_time - window_seconds
        
        count = sum(1 for error in self.recent_errors 
                   if error['type'] == error_type and error['timestamp'] >= window_start)
        
        return count / (window_seconds / 60.0)  # 每分钟错误数
    
    def get_stats_summary(self) -> Dict:
        """获取统计摘要"""
        return {
            'total_error_types': len(self.error_stats),
            'error_stats': self.error_stats,
            'recent_error_count': len(self.recent_errors)
        }


class EnhancedRetryHandler:
    """增强的重试处理器"""
    
    def __init__(self, config: RetryConfig = None, monitor: ErrorMonitor = None):
        self.config = config or RetryConfig()
        self.monitor = monitor or ErrorMonitor()
        self.logger = logging.getLogger(__name__)
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数并处理重试"""
        last_error = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                result = func(*args, **kwargs)
                
                # 成功执行，检查是否为错误结果
                if isinstance(result, dict) and 'code' in result:
                    error_type = ErrorClassifier.classify_error(result)
                    
                    if ErrorClassifier.is_retryable(result, self.config.retryable_errors):
                        if attempt < self.config.max_attempts:
                            # 记录错误并重试
                            self.monitor.record_error(error_type, result, f"attempt_{attempt}")
                            delay = RetryStrategy.exponential_backoff(attempt, self.config)
                            
                            self.logger.warning(
                                f"可重试错误 (尝试 {attempt}/{self.config.max_attempts}): "
                                f"{result.get('msg', 'Unknown')} - 等待 {delay:.2f}s 后重试"
                            )
                            
                            time.sleep(delay)
                            last_error = result
                            continue
                    else:
                        # 不可重试的错误
                        self.monitor.record_error(error_type, result, "non_retryable")
                        self.logger.error(f"不可重试错误: {result.get('msg', 'Unknown')}")
                        return result
                
                # 成功结果
                if attempt > 1:
                    self.logger.info(f"重试成功 (尝试 {attempt}/{self.config.max_attempts})")
                
                return result
                
            except Exception as e:
                error_type = ErrorClassifier.classify_error(e)
                last_error = e
                
                if ErrorClassifier.is_retryable(e, self.config.retryable_errors):
                    if attempt < self.config.max_attempts:
                        # 记录错误并重试
                        self.monitor.record_error(error_type, e, f"attempt_{attempt}")
                        delay = RetryStrategy.exponential_backoff(attempt, self.config)
                        
                        self.logger.warning(
                            f"异常重试 (尝试 {attempt}/{self.config.max_attempts}): "
                            f"{str(e)} - 等待 {delay:.2f}s 后重试"
                        )
                        
                        time.sleep(delay)
                        continue
                else:
                    # 不可重试的异常
                    self.monitor.record_error(error_type, e, "non_retryable_exception")
                    self.logger.error(f"不可重试异常: {str(e)}")
                    raise e
        
        # 所有重试都失败了
        if isinstance(last_error, Exception):
            self.logger.error(f"所有重试失败，抛出最后异常: {str(last_error)}")
            raise last_error
        else:
            self.logger.error(f"所有重试失败，返回最后错误: {last_error}")
            return last_error


def with_retry(config: RetryConfig = None):
    """重试装饰器"""
    def decorator(func):
        handler = EnhancedRetryHandler(config)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return handler.execute_with_retry(func, *args, **kwargs)
        
        return wrapper
    return decorator


# 全局错误监控器实例
global_error_monitor = ErrorMonitor()


# 预定义的重试配置
TRADING_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=30.0,
    backoff_factor=2.0,
    jitter=True,
    retryable_errors=[
        ErrorType.NETWORK,
        ErrorType.RATE_LIMIT,
        ErrorType.INSUFFICIENT_BALANCE,
        ErrorType.PRECISION
    ]
)

QUERY_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=0.5,
    max_delay=10.0,
    backoff_factor=1.5,
    jitter=True,
    retryable_errors=[
        ErrorType.NETWORK,
        ErrorType.RATE_LIMIT
    ]
)
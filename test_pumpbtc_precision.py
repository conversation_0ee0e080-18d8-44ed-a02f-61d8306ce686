#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PUMPBTCUSDT 精度测试和修复脚本
专门针对 PUMPBTCUSDT 的精度错误进行调试和修复
"""

import json
import yaml
import sys
import os
from decimal import Decimal, ROUND_DOWN
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from http_client import HttpClient
except ImportError:
    print("❌ 无法导入 HttpClient，请检查文件路径")
    sys.exit(1)

def load_config():
    """加载配置文件"""
    try:
        # 尝试加载 YAML 配置
        config_yaml_path = Path("config/config.yaml")
        if config_yaml_path.exists():
            with open(config_yaml_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                print(f"✓ 成功加载配置文件: {config_yaml_path}")
                return config
        
        # 尝试加载 JSON 配置
        config_json_path = Path("config/config.json")
        if config_json_path.exists():
            with open(config_json_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"✓ 成功加载配置文件: {config_json_path}")
                return config
        
        print("❌ 未找到配置文件")
        return None
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return None

def get_exchange_info(client, symbol="PUMPBTCUSDT"):
    """获取交易对的交易规则"""
    try:
        print(f"\n🔍 获取 {symbol} 交易规则...")
        
        # 获取交易规则
        response = client.get("/fapi/v1/exchangeInfo")
        if not response:
            print("❌ 获取交易规则失败")
            return None
        
        # 查找指定交易对
        for symbol_info in response.get('symbols', []):
            if symbol_info['symbol'] == symbol:
                print(f"✓ 找到 {symbol} 交易规则")
                
                # 提取关键过滤器
                filters = {}
                for filter_info in symbol_info.get('filters', []):
                    filter_type = filter_info['filterType']
                    if filter_type == 'LOT_SIZE':
                        filters['stepSize'] = filter_info['stepSize']
                        filters['minQty'] = filter_info['minQty']
                        filters['maxQty'] = filter_info['maxQty']
                    elif filter_type == 'PRICE_FILTER':
                        filters['tickSize'] = filter_info['tickSize']
                        filters['minPrice'] = filter_info['minPrice']
                        filters['maxPrice'] = filter_info['maxPrice']
                    elif filter_type == 'MIN_NOTIONAL':
                        filters['minNotional'] = filter_info['notional']
                
                # 显示交易规则
                print(f"📊 {symbol} 交易规则:")
                print(f"   数量精度 (stepSize): {filters.get('stepSize', 'N/A')}")
                print(f"   最小数量 (minQty): {filters.get('minQty', 'N/A')}")
                print(f"   最大数量 (maxQty): {filters.get('maxQty', 'N/A')}")
                print(f"   价格精度 (tickSize): {filters.get('tickSize', 'N/A')}")
                print(f"   最小价格 (minPrice): {filters.get('minPrice', 'N/A')}")
                print(f"   最大价格 (maxPrice): {filters.get('maxPrice', 'N/A')}")
                print(f"   最小名义价值 (minNotional): {filters.get('minNotional', 'N/A')}")
                
                return {
                    'symbol': symbol,
                    'status': symbol_info.get('status'),
                    'filters': filters
                }
        
        print(f"❌ 未找到 {symbol} 交易规则")
        return None
        
    except Exception as e:
        print(f"❌ 获取交易规则异常: {e}")
        return None

def get_safe_decimal_places(step_size_str):
    """安全获取小数位数"""
    try:
        if not step_size_str or step_size_str == '0':
            return 8  # 默认最大精度
        
        step_decimal = Decimal(step_size_str)
        if step_decimal <= 0:
            return 8
        
        # 计算小数位数
        decimal_places = abs(step_decimal.as_tuple().exponent)
        return min(decimal_places, 8)  # 限制最大8位小数
        
    except:
        return 8

def format_quantity_safe(quantity, step_size):
    """安全格式化数量"""
    try:
        if not step_size or step_size == '0':
            # 如果没有stepSize，使用默认精度
            return f"{float(quantity):.8f}".rstrip('0').rstrip('.')
        
        step_decimal = Decimal(str(step_size))
        quantity_decimal = Decimal(str(quantity))
        
        # 向下取整到stepSize的倍数
        steps = int(quantity_decimal / step_decimal)
        formatted_quantity = steps * step_decimal
        
        # 获取小数位数
        decimal_places = get_safe_decimal_places(step_size)
        
        # 格式化并移除尾随零
        result = f"{formatted_quantity:.{decimal_places}f}".rstrip('0').rstrip('.')
        
        # 确保不为空
        if not result or result == '':
            result = "0"
        
        return result
        
    except Exception as e:
        print(f"❌ 数量格式化异常: {e}")
        return str(float(quantity))

def format_price_safe(price, tick_size):
    """安全格式化价格"""
    try:
        if not tick_size or tick_size == '0':
            # 如果没有tickSize，使用默认精度
            return f"{float(price):.8f}".rstrip('0').rstrip('.')
        
        tick_decimal = Decimal(str(tick_size))
        price_decimal = Decimal(str(price))
        
        # 向下取整到tickSize的倍数
        ticks = int(price_decimal / tick_decimal)
        formatted_price = ticks * tick_decimal
        
        # 获取小数位数
        decimal_places = get_safe_decimal_places(tick_size)
        
        # 格式化并移除尾随零
        result = f"{formatted_price:.{decimal_places}f}".rstrip('0').rstrip('.')
        
        # 确保不为空
        if not result or result == '':
            result = "0"
        
        return result
        
    except Exception as e:
        print(f"❌ 价格格式化异常: {e}")
        return str(float(price))

def test_pumpbtc_formatting(exchange_info):
    """测试PUMPBTCUSDT的格式化"""
    if not exchange_info:
        print("❌ 无交易规则信息，无法测试")
        return False
    
    filters = exchange_info['filters']
    step_size = filters.get('stepSize')
    tick_size = filters.get('tickSize')
    
    print(f"\n🧪 测试 PUMPBTCUSDT 格式化...")
    
    # 测试用例（基于用户日志中的实际数据）
    test_cases = [
        {"quantity": 1268.352, "price": 0.100000, "desc": "用户日志中的实际数据"},
        {"quantity": 1268, "price": 0.1, "desc": "简化的整数"},
        {"quantity": 1000, "price": 0.09999, "desc": "常见的交易数量"},
        {"quantity": 100.5, "price": 0.12345, "desc": "小数数量测试"},
    ]
    
    success_count = 0
    for i, case in enumerate(test_cases, 1):
        try:
            original_qty = case["quantity"]
            original_price = case["price"]
            
            formatted_qty = format_quantity_safe(original_qty, step_size)
            formatted_price = format_price_safe(original_price, tick_size)
            
            print(f"\n测试 {i}: {case['desc']}")
            print(f"   原始数量: {original_qty} -> 格式化: {formatted_qty}")
            print(f"   原始价格: {original_price} -> 格式化: {formatted_price}")
            
            # 验证格式化结果
            if formatted_qty and formatted_qty != "0" and formatted_price and formatted_price != "0":
                print(f"   ✓ 格式化成功")
                success_count += 1
            else:
                print(f"   ❌ 格式化失败")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print(f"\n📊 格式化测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)

def test_order_params(exchange_info):
    """测试完整的下单参数"""
    if not exchange_info:
        print("❌ 无交易规则信息，无法测试")
        return False
    
    filters = exchange_info['filters']
    
    print(f"\n🔧 测试完整下单参数...")
    
    # 模拟用户日志中的下单参数
    test_params = {
        "symbol": "PUMPBTCUSDT",
        "side": "BUY",
        "type": "MARKET",
        "quantity": 1268.352,
        "price": 0.100000  # 市价单可能不需要价格，但测试格式化
    }
    
    try:
        # 格式化参数
        formatted_params = {}
        formatted_params["symbol"] = test_params["symbol"]
        formatted_params["side"] = test_params["side"]
        formatted_params["type"] = test_params["type"]
        
        # 格式化数量
        formatted_quantity = format_quantity_safe(
            test_params["quantity"], 
            filters.get('stepSize')
        )
        formatted_params["quantity"] = formatted_quantity
        
        # 如果是限价单，格式化价格
        if test_params["type"] == "LIMIT":
            formatted_price = format_price_safe(
                test_params["price"], 
                filters.get('tickSize')
            )
            formatted_params["price"] = formatted_price
        
        print(f"✓ 格式化后的下单参数:")
        for key, value in formatted_params.items():
            print(f"   {key}: {value}")
        
        # 验证最小名义价值
        min_notional = filters.get('minNotional')
        if min_notional:
            notional_value = float(formatted_quantity) * float(test_params.get("price", 0.1))
            print(f"\n💰 名义价值检查:")
            print(f"   计算值: {notional_value}")
            print(f"   最小值: {min_notional}")
            if notional_value >= float(min_notional):
                print(f"   ✓ 满足最小名义价值要求")
            else:
                print(f"   ❌ 不满足最小名义价值要求")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 PUMPBTCUSDT 精度测试和修复")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config:
        return False
    
    # 创建HTTP客户端
    try:
        client = HttpClient(
            config['api_key'], 
            config['api_secret'], 
            config['base_url']
        )
        print("✓ HTTP客户端创建成功")
    except Exception as e:
        print(f"❌ HTTP客户端创建失败: {e}")
        return False
    
    # 获取交易规则
    exchange_info = get_exchange_info(client, "PUMPBTCUSDT")
    if not exchange_info:
        print("❌ 无法获取交易规则，测试终止")
        return False
    
    # 测试格式化
    format_success = test_pumpbtc_formatting(exchange_info)
    
    # 测试下单参数
    params_success = test_order_params(exchange_info)
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print(f"交易规则获取: {'✓ 成功' if exchange_info else '❌ 失败'}")
    print(f"格式化测试: {'✓ 成功' if format_success else '❌ 失败'}")
    print(f"参数测试: {'✓ 成功' if params_success else '❌ 失败'}")
    
    if exchange_info and format_success and params_success:
        print("\n🎉 所有测试通过！可以应用修复方案")
        
        # 输出修复建议
        print("\n💡 修复建议:")
        print("1. 确保使用正确的stepSize和tickSize进行格式化")
        print("2. 在格式化时向下取整，避免超出精度")
        print("3. 移除尾随零，但确保结果不为空")
        print("4. 限制最大小数位数为8位")
        
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
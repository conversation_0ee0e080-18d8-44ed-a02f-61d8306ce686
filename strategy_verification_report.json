{"timestamp": "2025-10-02T09:12:06.351763", "tests": {"quantity_precision": {"passed": true, "details": [{"input": 123.456789, "step": 0.01, "expected": 123.45, "result": 123.45, "success": true}, {"input": 100.999, "step": 0.1, "expected": 100.9, "result": 100.9, "success": true}, {"input": 50.001, "step": 1.0, "expected": 50.0, "result": 50.0, "success": true}, {"input": 0.123456, "step": 0.0001, "expected": 0.1234, "result": 0.1234, "success": true}]}, "price_precision": {"passed": true, "details": [{"input": 1.075678, "tick": 1e-05, "expected": 1.07567, "result": 1.07567, "success": true}, {"input": 100.999999, "tick": 0.001, "expected": 100.999, "result": 100.999, "success": true}, {"input": 50.12345, "tick": 0.01, "expected": 50.12, "result": 50.12, "success": true}]}, "position_management": {"passed": true, "scenarios": [{"scenario": "6%盈利移动止损", "success": true, "details": "Should update trailing stop"}, {"scenario": "15%盈利部分止盈", "success": true, "details": "Should take partial profit"}, {"scenario": "超时强制平仓", "success": true, "details": "Should force close after 4 hours"}]}, "duplicate_prevention": {"passed": true, "details": "Should not open new position when one exists"}, "order_cleanup": {"passed": false, "details": "Should cancel all orders when closing position"}, "volume_confirmation": {"passed": true, "details": {"current_volume": 2000.0, "average_volume": 1000.0, "threshold": 1500.0, "confirmed": true}}}, "summary": {"total_tests": 6, "passed_tests": 5, "failed_tests": 1, "success_rate": "83.3%"}}
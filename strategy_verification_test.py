# -*- coding: utf-8 -*-
"""
策略修复版本验证测试
测试修复后的交易逻辑是否正确工作
"""
import json
import time
import datetime as dt
from unittest.mock import Mock, patch
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s')

class StrategyVerificationTest:
    def __init__(self):
        self.test_results = {
            'timestamp': dt.datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
    def mock_api_responses(self):
        """模拟API响应"""
        return {
            'tickers': [
                {
                    'symbol': 'TESTUSDT',
                    'priceChangePercent': '25.5',
                    'quoteVolume': '500000'
                }
            ],
            'exchange_info': {
                'symbols': [{
                    'symbol': 'TESTUSDT',
                    'status': 'TRADING',
                    'onboardDate': int((dt.datetime.utcnow() - dt.<PERSON><PERSON><PERSON>(days=15)).timestamp() * 1000),
                    'filters': [
                        {'filterType': 'PRICE_FILTER', 'tickSize': '0.00001'},
                        {'filterType': 'LOT_SIZE', 'stepSize': '0.01'},
                        {'filterType': 'MIN_NOTIONAL', 'notional': '5.0'}
                    ]
                }]
            },
            'balance': [{'asset': 'USDT', 'balance': '1000.0'}],
            'klines': [
                [0, '1.0000', '1.0500', '0.9900', '1.0200', '1000', 0, 0, 0, 0, 0, 0],  # 历史K线
                [0, '1.0200', '1.0600', '1.0100', '1.0400', '1200', 0, 0, 0, 0, 0, 0],
                [0, '1.0400', '1.0700', '1.0300', '1.0500', '1100', 0, 0, 0, 0, 0, 0],
                # ... 更多历史数据
            ] + [[0, f'{1.05 + i*0.001}', f'{1.06 + i*0.001}', f'{1.04 + i*0.001}', f'{1.055 + i*0.001}', '1000', 0, 0, 0, 0, 0, 0] for i in range(17)] + 
            [
                [0, '1.0700', '1.0800', '1.0650', '1.0750', '2000', 0, 0, 0, 0, 0, 0]  # 突破K线
            ],
            'ticker_price': {'price': '1.0750'},
            'position_risk': [
                {
                    'symbol': 'TESTUSDT',
                    'positionAmt': '930.23',
                    'entryPrice': '1.0750',
                    'unRealizedProfit': '50.0',
                    'percentage': '5.0'
                }
            ],
            'order_success': {
                'orderId': 12345,
                'status': 'FILLED',
                'executedQty': '930.23'
            }
        }

    def test_quantity_precision(self):
        """测试数量精度处理"""
        print("\n=== 测试数量精度处理 ===")
        
        # 导入修复版策略
        import sys
        sys.path.append('e:\\allmac\\strategy')
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        strategy = LightNewCoinBreakout()
        
        # 测试数量格式化
        test_cases = [
            (123.456789, 0.01, 123.45),
            (100.999, 0.1, 100.9),
            (50.001, 1.0, 50.0),
            (0.123456, 0.0001, 0.1234)
        ]
        
        results = []
        for qty, step, expected in test_cases:
            formatted = strategy.format_quantity(qty, step)
            success = abs(formatted - expected) < 1e-8
            results.append({
                'input': qty,
                'step': step,
                'expected': expected,
                'result': formatted,
                'success': success
            })
            print(f"数量 {qty} 步长 {step} -> {formatted} (期望: {expected}) {'✅' if success else '❌'}")
        
        self.test_results['tests']['quantity_precision'] = {
            'passed': all(r['success'] for r in results),
            'details': results
        }

    def test_price_precision(self):
        """测试价格精度处理"""
        print("\n=== 测试价格精度处理 ===")
        
        import sys
        sys.path.append('e:\\allmac\\strategy')
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        strategy = LightNewCoinBreakout()
        
        # 测试价格格式化
        test_cases = [
            (1.075678, 0.00001, 1.07567),
            (100.999999, 0.001, 100.999),
            (50.12345, 0.01, 50.12)
        ]
        
        results = []
        for price, tick, expected in test_cases:
            formatted = strategy.format_price(price, tick)
            success = abs(formatted - expected) < 1e-8
            results.append({
                'input': price,
                'tick': tick,
                'expected': expected,
                'result': formatted,
                'success': success
            })
            print(f"价格 {price} 步长 {tick} -> {formatted} (期望: {expected}) {'✅' if success else '❌'}")
        
        self.test_results['tests']['price_precision'] = {
            'passed': all(r['success'] for r in results),
            'details': results
        }

    def test_position_management_logic(self):
        """测试持仓管理逻辑"""
        print("\n=== 测试持仓管理逻辑 ===")
        
        import sys
        sys.path.append('e:\\allmac\\strategy')
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        strategy = LightNewCoinBreakout()
        
        # 模拟持仓状态
        strategy.symbol = 'TESTUSDT'
        strategy.entry = 1.0000
        strategy.qty = 1000.0
        strategy.position_opened_time = dt.datetime.utcnow() - dt.timedelta(hours=2)
        strategy.max_profit = 0
        
        # 测试场景
        test_scenarios = []
        
        # 场景1: 盈利6%，应该移动止损
        with patch('maker_channel_light_fixed.get') as mock_get:
            mock_get.return_value = {'price': '1.0600'}  # 6%盈利
            
            # 模拟取消订单和下新订单
            with patch('maker_channel_light_fixed.post') as mock_post:
                mock_post.return_value = {'orderId': 67890}
                
                try:
                    strategy.manage_position()
                    test_scenarios.append({
                        'scenario': '6%盈利移动止损',
                        'success': True,
                        'details': 'Should update trailing stop'
                    })
                except Exception as e:
                    test_scenarios.append({
                        'scenario': '6%盈利移动止损',
                        'success': False,
                        'error': str(e)
                    })
        
        # 场景2: 盈利15%，应该部分止盈
        strategy.max_profit = 15
        with patch('maker_channel_light_fixed.get') as mock_get:
            mock_get.return_value = {'price': '1.1500'}  # 15%盈利
            
            with patch('maker_channel_light_fixed.post') as mock_post:
                mock_post.return_value = {'orderId': 67891}
                
                try:
                    strategy.manage_position()
                    test_scenarios.append({
                        'scenario': '15%盈利部分止盈',
                        'success': True,
                        'details': 'Should take partial profit'
                    })
                except Exception as e:
                    test_scenarios.append({
                        'scenario': '15%盈利部分止盈',
                        'success': False,
                        'error': str(e)
                    })
        
        # 场景3: 持仓超过4小时，应该强制平仓
        strategy.position_opened_time = dt.datetime.utcnow() - dt.timedelta(hours=5)
        
        with patch('maker_channel_light_fixed.get') as mock_get:
            mock_get.return_value = {'price': '1.0500'}
            
            with patch('maker_channel_light_fixed.post') as mock_post:
                mock_post.return_value = {'orderId': 67892}
                
                try:
                    strategy.manage_position()
                    test_scenarios.append({
                        'scenario': '超时强制平仓',
                        'success': strategy.entry is None,  # 应该重置持仓
                        'details': 'Should force close after 4 hours'
                    })
                except Exception as e:
                    test_scenarios.append({
                        'scenario': '超时强制平仓',
                        'success': False,
                        'error': str(e)
                    })
        
        for scenario in test_scenarios:
            print(f"{scenario['scenario']}: {'✅' if scenario['success'] else '❌'}")
            if not scenario['success'] and 'error' in scenario:
                print(f"  错误: {scenario['error']}")
        
        self.test_results['tests']['position_management'] = {
            'passed': all(s['success'] for s in test_scenarios),
            'scenarios': test_scenarios
        }

    def test_duplicate_position_prevention(self):
        """测试重复开仓防护"""
        print("\n=== 测试重复开仓防护 ===")
        
        import sys
        sys.path.append('e:\\allmac\\strategy')
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        strategy = LightNewCoinBreakout()
        strategy.symbol = 'TESTUSDT'
        
        # 模拟已有持仓
        strategy.entry = 1.0500
        strategy.qty = 1000.0
        
        # 模拟突破条件满足
        with patch('maker_channel_light_fixed.get') as mock_get:
            mock_get.side_effect = [
                # K线数据
                [[0, '1.0000', '1.0500', '0.9900', '1.0200', '1000', 0, 0, 0, 0, 0, 0]] * 21,
            ]
            
            # 检查是否会重复开仓
            original_entry = strategy.entry
            
            # 这里应该跳过开仓逻辑，因为已有持仓
            # 在实际的run方法中，会检查self.entry是否为None
            
            duplicate_prevented = strategy.entry == original_entry
            
            print(f"重复开仓防护: {'✅' if duplicate_prevented else '❌'}")
            
            self.test_results['tests']['duplicate_prevention'] = {
                'passed': duplicate_prevented,
                'details': 'Should not open new position when one exists'
            }

    def test_order_cleanup(self):
        """测试订单清理逻辑"""
        print("\n=== 测试订单清理逻辑 ===")
        
        import sys
        sys.path.append('e:\\allmac\\strategy')
        from maker_channel_light_fixed import LightNewCoinBreakout
        
        strategy = LightNewCoinBreakout()
        strategy.symbol = 'TESTUSDT'
        strategy.qty = 1000.0
        strategy.stop_order_id = 12345
        strategy.take_profit_order_id = 12346
        
        # 模拟平仓
        with patch('maker_channel_light_fixed.post') as mock_post:
            mock_post.return_value = {'orderId': 67890}
            
            with patch('maker_channel_light_fixed.get') as mock_get:
                mock_get.return_value = {'price': '1.0750'}
                
                try:
                    strategy.close_position("Test close")
                    
                    # 检查是否正确清理
                    cleanup_success = (
                        strategy.stop_order_id is None and 
                        strategy.take_profit_order_id is None
                    )
                    
                    print(f"订单清理: {'✅' if cleanup_success else '❌'}")
                    
                    self.test_results['tests']['order_cleanup'] = {
                        'passed': cleanup_success,
                        'details': 'Should cancel all orders when closing position'
                    }
                    
                except Exception as e:
                    print(f"订单清理测试失败: {e}")
                    self.test_results['tests']['order_cleanup'] = {
                        'passed': False,
                        'error': str(e)
                    }

    def test_volume_confirmation(self):
        """测试成交量确认逻辑"""
        print("\n=== 测试成交量确认逻辑 ===")
        
        # 测试成交量放大确认
        klines_data = [
            [0, '1.0000', '1.0500', '0.9900', '1.0200', '1000', 0, 0, 0, 0, 0, 0],  # 平均成交量1000
            [0, '1.0200', '1.0600', '1.0100', '1.0400', '1000', 0, 0, 0, 0, 0, 0],
            [0, '1.0400', '1.0700', '1.0300', '1.0500', '1000', 0, 0, 0, 0, 0, 0],
        ] + [[0, f'{1.05 + i*0.001}', f'{1.06 + i*0.001}', f'{1.04 + i*0.001}', f'{1.055 + i*0.001}', '1000', 0, 0, 0, 0, 0, 0] for i in range(17)] + [
            [0, '1.0700', '1.0800', '1.0650', '1.0750', '2000', 0, 0, 0, 0, 0, 0]  # 成交量放大到2000 (2倍)
        ]
        
        # 计算前10根平均成交量
        avg_volume = sum(float(k[5]) for k in klines_data[-11:-1]) / 10  # 1000
        current_volume = float(klines_data[-1][5])  # 2000
        
        volume_confirmed = current_volume > avg_volume * 1.5  # 2000 > 1500
        
        print(f"成交量确认 (当前: {current_volume}, 平均: {avg_volume}, 阈值: {avg_volume * 1.5}): {'✅' if volume_confirmed else '❌'}")
        
        self.test_results['tests']['volume_confirmation'] = {
            'passed': volume_confirmed,
            'details': {
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'threshold': avg_volume * 1.5,
                'confirmed': volume_confirmed
            }
        }

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始策略修复版本验证测试...")
        
        self.test_quantity_precision()
        self.test_price_precision()
        self.test_position_management_logic()
        self.test_duplicate_position_prevention()
        self.test_order_cleanup()
        self.test_volume_confirmation()
        
        # 生成测试摘要
        total_tests = len(self.test_results['tests'])
        passed_tests = sum(1 for test in self.test_results['tests'].values() if test.get('passed', False))
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': f"{passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%"
        }
        
        print(f"\n📊 测试摘要:")
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {self.test_results['summary']['success_rate']}")
        
        # 保存测试报告
        with open('e:\\allmac\\strategy_verification_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细测试报告已保存到: e:\\allmac\\strategy_verification_report.json")
        
        return self.test_results

if __name__ == '__main__':
    tester = StrategyVerificationTest()
    results = tester.run_all_tests()
    
    # 输出关键修复点验证结果
    print(f"\n🔧 关键修复点验证:")
    print(f"1. 数量精度处理: {'✅' if results['tests']['quantity_precision']['passed'] else '❌'}")
    print(f"2. 价格精度处理: {'✅' if results['tests']['price_precision']['passed'] else '❌'}")
    print(f"3. 持仓管理逻辑: {'✅' if results['tests']['position_management']['passed'] else '❌'}")
    print(f"4. 重复开仓防护: {'✅' if results['tests']['duplicate_prevention']['passed'] else '❌'}")
    print(f"5. 订单清理逻辑: {'✅' if results['tests']['order_cleanup']['passed'] else '❌'}")
    print(f"6. 成交量确认: {'✅' if results['tests']['volume_confirmation']['passed'] else '❌'}")
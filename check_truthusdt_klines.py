#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import datetime
from datetime import timezone, timedelta

def get_binance_klines(symbol, interval='3m', limit=100):
    """获取币安K线数据"""
    url = "https://api.binance.com/api/v3/klines"
    params = {
        'symbol': symbol,
        'interval': interval,
        'limit': limit
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取K线数据失败: {e}")
        return None

def get_symbol_info(symbol):
    """获取交易对信息"""
    url = "https://api.binance.com/api/v3/exchangeInfo"
    
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        for sym_info in data['symbols']:
            if sym_info['symbol'] == symbol:
                return sym_info
        return None
    except Exception as e:
        print(f"获取交易对信息失败: {e}")
        return None

def get_24hr_ticker(symbol):
    """获取24小时价格统计"""
    url = "https://api.binance.com/api/v3/ticker/24hr"
    params = {'symbol': symbol}
    
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"获取24小时统计失败: {e}")
        return None

def timestamp_to_datetime(timestamp):
    """将时间戳转换为可读时间"""
    return datetime.datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)

def check_truthusdt_status():
    """检查TRUTHUSDT的K线合成情况和评分准入条件"""
    
    print("=" * 60)
    print("TRUTHUSDT K线合成情况检查")
    print("=" * 60)
    
    symbol = "TRUTHUSDT"
    listing_time = datetime.datetime(2025, 10, 1, 20, 0, 0, tzinfo=timezone.utc)
    current_time = datetime.datetime.now(timezone.utc)
    
    print(f"检查币种: {symbol}")
    print(f"上线时间: {listing_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # 计算币种年龄
    age_delta = current_time - listing_time
    age_minutes = age_delta.total_seconds() / 60
    age_hours = age_minutes / 60
    age_days = age_hours / 24
    
    print(f"币种年龄: {age_days:.2f}天 ({age_hours:.2f}小时, {age_minutes:.0f}分钟)")
    print()
    
    # 1. 检查交易对信息
    print("1. 检查交易对信息...")
    symbol_info = get_symbol_info(symbol)
    
    if symbol_info:
        print(f"✓ 交易对状态: {symbol_info['status']}")
        print(f"✓ 基础资产: {symbol_info['baseAsset']}")
        print(f"✓ 计价资产: {symbol_info['quoteAsset']}")
        
        # 获取交易规则
        filters = {f['filterType']: f for f in symbol_info['filters']}
        
        if 'PRICE_FILTER' in filters:
            tick_size = float(filters['PRICE_FILTER']['tickSize'])
            print(f"✓ 价格精度 (tick_size): {tick_size}")
        
        if 'LOT_SIZE' in filters:
            step_size = float(filters['LOT_SIZE']['stepSize'])
            min_qty = float(filters['LOT_SIZE']['minQty'])
            print(f"✓ 数量精度 (step_size): {step_size}")
            print(f"✓ 最小数量: {min_qty}")
        
        if 'MIN_NOTIONAL' in filters:
            min_notional = float(filters['MIN_NOTIONAL']['minNotional'])
            print(f"✓ 最小名义价值: {min_notional} USDT")
    else:
        print("✗ 未找到交易对信息")
        return
    
    print()
    
    # 2. 检查24小时统计
    print("2. 检查24小时价格统计...")
    ticker = get_24hr_ticker(symbol)
    
    if ticker:
        print(f"✓ 当前价格: {float(ticker['lastPrice']):.6f} USDT")
        print(f"✓ 24h涨跌幅: {float(ticker['priceChangePercent']):.2f}%")
        print(f"✓ 24h成交量: {float(ticker['volume']):.2f} {symbol_info['baseAsset']}")
        print(f"✓ 24h成交额: {float(ticker['quoteVolume']):.2f} USDT")
        print(f"✓ 24h交易次数: {ticker['count']}")
    else:
        print("✗ 无法获取24小时统计")
    
    print()
    
    # 3. 检查不同时间周期的K线数据
    intervals = ['1m', '3m', '5m', '15m', '1h']
    kline_status = {}
    
    print("3. 检查K线数据可用性...")
    
    for interval in intervals:
        print(f"检查 {interval} K线...")
        klines = get_binance_klines(symbol, interval, 100)
        
        if klines and len(klines) > 0:
            kline_count = len(klines)
            first_kline_time = timestamp_to_datetime(klines[0][0])
            last_kline_time = timestamp_to_datetime(klines[-1][0])
            
            print(f"  ✓ 可用K线数量: {kline_count}")
            print(f"  ✓ 最早K线时间: {first_kline_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            print(f"  ✓ 最新K线时间: {last_kline_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            
            # 计算K线覆盖时间
            kline_span_minutes = (last_kline_time - first_kline_time).total_seconds() / 60
            print(f"  ✓ K线覆盖时间: {kline_span_minutes:.0f}分钟")
            
            kline_status[interval] = {
                'available': True,
                'count': kline_count,
                'span_minutes': kline_span_minutes,
                'first_time': first_kline_time,
                'last_time': last_kline_time
            }
        else:
            print(f"  ✗ {interval} K线数据不可用")
            kline_status[interval] = {'available': False}
        
        print()
    
    # 4. 分析3分钟K线合成情况
    print("4. 分析3分钟K线合成情况...")
    
    if kline_status['3m']['available']:
        kline_3m = kline_status['3m']
        print(f"✓ 3分钟K线已可用")
        print(f"✓ 可用数量: {kline_3m['count']} 根")
        print(f"✓ 数据覆盖: {kline_3m['span_minutes']:.0f} 分钟")
        
        # 检查是否满足评分条件（通常需要至少8根3m K线）
        min_klines_for_scoring = 8
        if kline_3m['count'] >= min_klines_for_scoring:
            print(f"✓ 满足评分条件 (≥{min_klines_for_scoring}根3m K线)")
            
            # 估算可以开始评分的时间
            if age_minutes >= 24:  # 3m * 8 = 24分钟
                print("✓ 已可以进入评分池进行评分")
            else:
                needed_minutes = 24 - age_minutes
                scoring_time = current_time + timedelta(minutes=needed_minutes)
                print(f"⏳ 预计 {needed_minutes:.0f} 分钟后可进入评分池")
                print(f"⏳ 预计评分时间: {scoring_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        else:
            needed_klines = min_klines_for_scoring - kline_3m['count']
            needed_minutes = needed_klines * 3
            scoring_time = current_time + timedelta(minutes=needed_minutes)
            print(f"⏳ 还需要 {needed_klines} 根3m K线")
            print(f"⏳ 预计 {needed_minutes} 分钟后满足评分条件")
            print(f"⏳ 预计评分时间: {scoring_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    else:
        print("✗ 3分钟K线尚未可用")
        
        # 检查1分钟K线是否可用，用于合成3分钟K线
        if kline_status['1m']['available']:
            print("✓ 1分钟K线可用，可以合成3分钟K线")
            kline_1m = kline_status['1m']
            
            # 估算3分钟K线合成时间
            # 通常需要至少3根1m K线才能合成1根3m K线
            available_3m_synthetic = kline_1m['count'] // 3
            print(f"✓ 可合成约 {available_3m_synthetic} 根3m K线")
            
            if available_3m_synthetic >= 8:
                print("✓ 合成的3m K线已满足评分条件")
                print("✓ 可以立即进入评分池")
            else:
                needed_1m = (8 - available_3m_synthetic) * 3
                needed_minutes = needed_1m
                scoring_time = current_time + timedelta(minutes=needed_minutes)
                print(f"⏳ 还需要 {needed_1m} 根1m K线来合成足够的3m K线")
                print(f"⏳ 预计 {needed_minutes} 分钟后可进入评分池")
                print(f"⏳ 预计评分时间: {scoring_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        else:
            print("✗ 1分钟K线也不可用，需要等待K线数据生成")
    
    print()
    
    # 5. 总结
    print("5. 总结...")
    print("=" * 40)
    
    if symbol_info and symbol_info['status'] == 'TRADING':
        print("✓ 交易对已正常交易")
    else:
        print("✗ 交易对状态异常")
    
    if ticker:
        print("✓ 有活跃的交易数据")
    else:
        print("✗ 缺少交易数据")
    
    # K线数据总结
    available_intervals = [k for k, v in kline_status.items() if v.get('available', False)]
    if available_intervals:
        print(f"✓ 可用K线周期: {', '.join(available_intervals)}")
    else:
        print("✗ 暂无可用K线数据")
    
    # 评分准入总结
    if kline_status.get('3m', {}).get('available', False):
        if kline_status['3m']['count'] >= 8:
            print("🎯 结论: TRUTHUSDT已可以进入评分池进行评分")
        else:
            needed = 8 - kline_status['3m']['count']
            print(f"⏳ 结论: 还需要 {needed} 根3m K线，约 {needed * 3} 分钟后可评分")
    elif kline_status.get('1m', {}).get('available', False):
        synthetic_3m = kline_status['1m']['count'] // 3
        if synthetic_3m >= 8:
            print("🎯 结论: 可通过1m K线合成，已满足评分条件")
        else:
            needed = (8 - synthetic_3m) * 3
            print(f"⏳ 结论: 通过1m合成，还需约 {needed} 分钟可评分")
    else:
        print("❌ 结论: K线数据不足，需要等待更多数据生成")
    
    print("=" * 60)

if __name__ == "__main__":
    check_truthusdt_status()
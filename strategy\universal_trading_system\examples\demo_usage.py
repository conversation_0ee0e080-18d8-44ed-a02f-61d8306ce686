"""
通用交易系统使用示例
Universal Trading System Demo Usage

本文件展示了如何使用通用交易系统处理500+币对的交易需求
包含完整的使用流程、配置示例和最佳实践
"""

import asyncio
import logging
import time
import random
from typing import List

# 导入通用交易系统
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from universal_trading_system import (
    UniversalTradingEngine,
    OrderRequest,
    SymbolTier,
    PerformanceGrade
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystemDemo:
    """交易系统演示类"""
    
    def __init__(self):
        self.engine = UniversalTradingEngine()
        
        # 演示用的币对列表（代表500+币对的样本）
        self.demo_symbols = [
            # 蓝筹币（Blue-chip）
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT',
            
            # 主流币（Mainstream）
            'ADAUSDT', 'XRPUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT',
            
            # 新兴币（Emerging）
            'MATICUSDT', 'AVAXUSDT', 'ATOMUSDT', 'NEARUSDT', 'FTMUSDT',
            
            # 小盘币（Small-cap）
            'CHZUSDT', 'ENJUSDT', 'SANDUSDT', 'MANAUSDT', 'GALAUSDT',
            
            # 新币（New）
            'OPUSDT', 'ARBUSDT', 'SUIUSDT', 'APTUSDT', 'LDOUSDT'
        ]
    
    async def demo_basic_usage(self):
        """演示基础使用方法"""
        print("\n=== 基础使用演示 ===")
        
        # 启动交易引擎
        await self.engine.start_engine()
        print("✓ 交易引擎已启动")
        
        # 创建单个订单
        order = OrderRequest(
            symbol='BTCUSDT',
            side='BUY',
            quantity=0.001,
            order_type='MARKET'
        )
        
        # 提交订单
        response = await self.engine.submit_order(order)
        print(f"✓ 订单提交结果: {response.message}")
        print(f"  - 状态: {response.status.value}")
        print(f"  - 成功: {response.success}")
        
        if response.validation_warnings:
            print(f"  - 验证警告: {'; '.join(response.validation_warnings)}")
        
        if response.risk_alerts:
            print(f"  - 风险提醒: {'; '.join(response.risk_alerts)}")
    
    async def demo_batch_trading(self):
        """演示批量交易处理"""
        print("\n=== 批量交易演示 ===")
        
        # 创建多个不同币对的订单
        orders = []
        for i, symbol in enumerate(self.demo_symbols[:10]):  # 取前10个币对
            order = OrderRequest(
                symbol=symbol,
                side='BUY' if i % 2 == 0 else 'SELL',
                quantity=random.uniform(0.001, 0.1),
                order_type='MARKET'
            )
            orders.append(order)
        
        print(f"创建了 {len(orders)} 个订单，涵盖不同分类的币对")
        
        # 批量提交订单
        results = []
        for order in orders:
            response = await self.engine.submit_order(order)
            results.append((order, response))
            
            # 显示处理结果
            tier_info = self.engine.classifier.get_symbol_tier(order.symbol).value
            print(f"  {order.symbol} ({tier_info}): {response.message}")
        
        # 统计结果
        successful = sum(1 for _, response in results if response.success)
        print(f"\n批量交易结果: {successful}/{len(results)} 成功")
    
    async def demo_symbol_classification(self):
        """演示币种分类功能"""
        print("\n=== 币种分类演示 ===")
        
        classification_stats = {}
        
        for symbol in self.demo_symbols:
            tier = self.engine.classifier.get_symbol_tier(symbol)
            if tier not in classification_stats:
                classification_stats[tier] = []
            classification_stats[tier].append(symbol)
        
        print("币种分类结果:")
        for tier, symbols in classification_stats.items():
            print(f"  {tier.value}: {', '.join(symbols)}")
            
            # 显示该分类的交易参数
            # 使用该分类中的第一个符号作为示例来获取参数
            sample_symbol = symbols[0] if symbols else "BTCUSDT"
            params = self.engine.parameter_matrix.get_parameters(sample_symbol, tier)
            print(f"    - 价格偏差容忍: {params.price_deviation_tolerance:.1%}")
            print(f"    - 最大仓位: ${params.max_position_usdt:,.0f}")
            print(f"    - 保证金缓冲: {params.margin_buffer_ratio:.1%}")
    
    async def demo_risk_management(self):
        """演示风险管理功能"""
        print("\n=== 风险管理演示 ===")
        
        # 模拟一些交易记录来触发风险评估
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        for symbol in test_symbols:
            # 模拟一些盈亏记录
            for i in range(5):
                pnl = random.uniform(-10, 20)  # 随机盈亏
                is_profitable = pnl > 0
                self.engine.risk_manager.update_trade_result(symbol, pnl, is_profitable)
            
            # 评估风险等级
            risk_level, risk_alerts = self.engine.risk_manager.assess_symbol_risk(symbol)
            can_trade, trade_reason = self.engine.risk_manager.should_allow_trade(symbol)
            
            print(f"  {symbol}:")
            print(f"    - 风险等级: {risk_level.value}")
            print(f"    - 可交易: {'是' if can_trade else '否'}")
            if not can_trade:
                print(f"    - 原因: {trade_reason}")
            if risk_alerts:
                print(f"    - 风险提醒: {'; '.join([alert.message for alert in risk_alerts])}")
        
        # 显示全局风险状态
        risk_summary = self.engine.risk_manager.get_risk_summary()
        print(f"\n全局风险分布:")
        for level, count in risk_summary['risk_distribution'].items():
            if count > 0:
                print(f"  {level}: {count} 个币种")
    
    async def demo_performance_monitoring(self):
        """演示性能监控功能"""
        print("\n=== 性能监控演示 ===")
        
        # 模拟一些交易记录
        from universal_trading_system.monitor.performance_monitor import TradeRecord
        
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'XRPUSDT']
        
        for symbol in test_symbols:
            # 为每个币种创建一些模拟交易记录
            for i in range(10):
                pnl = random.uniform(-5, 15)  # 模拟盈亏
                trade = TradeRecord(
                    symbol=symbol,
                    side='BUY' if i % 2 == 0 else 'SELL',
                    quantity=random.uniform(0.001, 0.1),
                    entry_price=random.uniform(1000, 50000),
                    pnl=pnl,
                    entry_time=time.time() - random.uniform(0, 86400),
                    exit_time=time.time(),
                    hold_duration=random.uniform(300, 3600),
                    is_closed=True
                )
                self.engine.performance_monitor.record_trade(trade)
        
        # 获取性能排名
        rankings = self.engine.performance_monitor.get_performance_ranking(5)
        print("性能排名 (Top 5):")
        for i, (symbol, perf, grade) in enumerate(rankings, 1):
            print(f"  {i}. {symbol}: {grade.value}")
            print(f"     总收益: ${perf.total_pnl:.2f}, 胜率: {perf.win_rate:.1%}")
        
        # 获取分类性能摘要
        tier_summary = self.engine.performance_monitor.get_tier_performance_summary()
        print(f"\n分类性能摘要:")
        for tier_name, stats in tier_summary.items():
            if stats['count'] > 0:
                print(f"  {tier_name}:")
                print(f"    - 币种数量: {stats['count']}")
                print(f"    - 总收益: ${stats['total_pnl']:.2f}")
                print(f"    - 平均胜率: {stats['avg_win_rate']:.1%}")
    
    async def demo_engine_status(self):
        """演示引擎状态查询"""
        print("\n=== 引擎状态演示 ===")
        
        # 获取引擎整体状态
        engine_status = self.engine.get_engine_status()
        print("引擎状态:")
        print(f"  - 运行状态: {'运行中' if engine_status['is_running'] else '已停止'}")
        print(f"  - 处理订单总数: {engine_status['total_orders_processed']}")
        print(f"  - 成功订单数: {engine_status['successful_orders']}")
        print(f"  - 失败订单数: {engine_status['failed_orders']}")
        print(f"  - 成功率: {engine_status['success_rate']:.1%}")
        print(f"  - 活跃订单数: {engine_status['active_orders_count']}")
        print(f"  - 支持币种数: {engine_status['supported_symbols_count']}")
        print(f"  - 监控币种数: {engine_status['monitored_symbols_count']}")
        
        # 查询特定币种状态
        test_symbol = 'BTCUSDT'
        symbol_status = self.engine.get_symbol_status(test_symbol)
        print(f"\n{test_symbol} 状态:")
        print(f"  - 分类: {symbol_status['tier']}")
        print(f"  - 风险等级: {symbol_status['risk_level']}")
        print(f"  - 可交易: {'是' if symbol_status['can_trade'] else '否'}")
        print(f"  - 活跃提醒: {len(symbol_status['active_alerts'])} 个")
        print(f"  - 最近1小时订单: {symbol_status['recent_orders']} 个")
    
    async def demo_advanced_features(self):
        """演示高级功能"""
        print("\n=== 高级功能演示 ===")
        
        # 1. 自定义币种参数
        print("1. 自定义币种参数:")
        custom_symbol = 'BTCUSDT'
        
        # 获取当前参数
        current_params = self.engine.parameter_matrix.get_parameters(
            custom_symbol, 
            self.engine.classifier.get_symbol_tier(custom_symbol)
        )
        print(f"  {custom_symbol} 当前最大仓位: ${current_params.max_position_usdt:,.0f}")
        
        # 设置自定义参数
        from universal_trading_system.config.parameter_matrix import TradingParameters
        custom_params = TradingParameters(
            price_deviation_tolerance=0.02,  # 2% 价格偏差容忍
            max_position_usdt=5000,          # 最大仓位 $5000
            margin_buffer_ratio=0.15         # 15% 保证金缓冲
        )
        
        self.engine.parameter_matrix.set_symbol_parameters(custom_symbol, custom_params)
        print(f"  已设置 {custom_symbol} 自定义参数: 最大仓位 ${custom_params.max_position_usdt:,.0f}")
        
        # 2. 参数优化演示
        print("\n2. 参数优化:")
        performance_data = {
            'success_rate': 0.3,      # 低成功率
            'avg_profit': -2.5,       # 负收益
            'max_drawdown': 0.15,     # 高回撤
            'volatility': 0.8,        # 高波动
            'profit_factor': 0.8,     # 低盈利因子
            'error_rate': 0.05        # 5% 错误率
        }
        
        tier = self.engine.classifier.get_symbol_tier(custom_symbol)
        optimized_params = self.engine.parameter_matrix.optimize_parameters(
            custom_symbol, tier, performance_data
        )
        print(f"  基于性能数据优化后的参数:")
        print(f"    - 价格偏差容忍: {optimized_params.price_deviation_tolerance:.1%}")
        print(f"    - 最大仓位: ${optimized_params.max_position_usdt:,.0f}")
        print(f"    - 止损比例: {optimized_params.stop_loss_ratio:.1%}")
        
        # 3. 缓存管理
        print("\n3. 缓存管理:")
        cache_stats = self.engine.validator.get_cache_stats()
        print(f"  验证器缓存统计:")
        for cache_name, stats in cache_stats.items():
            print(f"    - {cache_name}: {stats['size']} 项, 命中率 {stats['hit_rate']:.1%}")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 通用交易系统完整演示开始")
        print("=" * 50)
        
        try:
            # 基础功能演示
            await self.demo_basic_usage()
            await asyncio.sleep(1)
            
            # 批量交易演示
            await self.demo_batch_trading()
            await asyncio.sleep(1)
            
            # 币种分类演示
            await self.demo_symbol_classification()
            await asyncio.sleep(1)
            
            # 风险管理演示
            await self.demo_risk_management()
            await asyncio.sleep(1)
            
            # 性能监控演示
            await self.demo_performance_monitoring()
            await asyncio.sleep(1)
            
            # 引擎状态演示
            await self.demo_engine_status()
            await asyncio.sleep(1)
            
            # 高级功能演示
            await self.demo_advanced_features()
            
        except Exception as e:
            logger.error(f"演示过程中发生错误: {e}")
        
        finally:
            # 停止引擎
            await self.engine.stop_engine()
            print("\n✓ 交易引擎已停止")
        
        print("\n" + "=" * 50)
        print("🎉 通用交易系统演示完成！")
        print("\n核心优势总结:")
        print("  ✅ 支持500+币对，无需特定编程")
        print("  ✅ 智能分类，自动参数配置")
        print("  ✅ 多层验证，确保交易安全")
        print("  ✅ 自适应风控，实时风险管理")
        print("  ✅ 性能监控，持续优化改进")

async def main():
    """主函数"""
    demo = TradingSystemDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    # 运行完整演示
    asyncio.run(main())
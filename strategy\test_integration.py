#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 验证重构后的组件兼容性和功能
"""

import sys
import os
import time
import logging
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_http = Mock()
        self.test_config = {
            'max_rate': 1200,
            'trade_rate': 50,
            'test_mode': True
        }
    
    def test_rate_limiter_integration(self):
        """测试限流器集成"""
        try:
            from rate_limiter import TokenBucketRateLimiter, TieredRateLimiter
            
            # 测试令牌桶限流器 - 修正参数顺序
            limiter = TokenBucketRateLimiter(capacity=20, refill_rate=10, name="test")
            self.assertTrue(limiter.acquire())
            
            # 测试分层限流器 - 修正配置格式
            tiered_config = {
                'order': {'capacity': 20, 'refill_rate': 50, 'name': 'order'},
                'query': {'capacity': 30, 'refill_rate': 1200, 'name': 'query'}
            }
            tiered = TieredRateLimiter(tiered_config)
            
            self.assertTrue(tiered.acquire('order'))
            self.assertTrue(tiered.acquire('query'))
            
            print("✓ 限流器集成测试通过")
            
        except ImportError as e:
            print(f"⚠ 限流器模块导入失败: {e}")
        except Exception as e:
            print(f"✗ 限流器集成测试失败: {e}")
            raise
    
    def test_order_queue_integration(self):
        """测试订单队列集成"""
        try:
            from order_queue import EnhancedOrderQueue
            from rate_limiter import TokenBucketRateLimiter
            
            # 创建增强订单队列 - 修正参数
            rate_limiter = TokenBucketRateLimiter(capacity=20, refill_rate=10, name="test_queue")
            queue = EnhancedOrderQueue(rate_limiter=rate_limiter, max_workers=2)
            
            # 测试添加操作 - 修正参数名
            test_operation = Mock()
            test_operation.return_value = {'status': 'success'}
            
            operation_id = queue.add_operation(
                operation_func=test_operation,  # 修正参数名
                priority=1,
                callback=lambda result: print(f"操作完成: {result}")
            )
            
            self.assertIsNotNone(operation_id)
            
            # 等待操作完成
            time.sleep(0.5)
            
            # 检查状态
            status = queue.get_operation_status(operation_id)
            self.assertIsNotNone(status)
            
            # 清理
            queue.shutdown()
            
            print("✓ 订单队列集成测试通过")
            
        except ImportError as e:
            print(f"⚠ 订单队列模块导入失败: {e}")
        except Exception as e:
            print(f"✗ 订单队列集成测试失败: {e}")
            raise
    
    def test_error_handler_integration(self):
        """测试错误处理器集成"""
        try:
            from error_handler import (
                EnhancedRetryHandler, 
                TRADING_RETRY_CONFIG, 
                QUERY_RETRY_CONFIG,
                global_error_monitor,
                ErrorType
            )
            
            # 测试交易重试处理器
            trading_handler = EnhancedRetryHandler(TRADING_RETRY_CONFIG, global_error_monitor)
            
            # 模拟成功操作
            def success_operation():
                return {'status': 'success', 'data': 'test'}
            
            result = trading_handler.execute_with_retry(success_operation)
            self.assertEqual(result['status'], 'success')
            
            # 测试网络错误重试机制
            call_count = 0
            def network_error_operation():
                nonlocal call_count
                call_count += 1
                if call_count <= 2:  # 前两次调用失败
                    # 模拟网络错误
                    error = Exception("Connection timeout")
                    error.code = -1007  # 网络超时错误码
                    raise error
                return {"result": "success after retry"}
            
            result = trading_handler.execute_with_retry(network_error_operation)
            self.assertEqual(result["result"], "success after retry")
            self.assertGreaterEqual(call_count, 3)  # 至少调用3次
            
            # 测试查询重试处理器
            query_handler = EnhancedRetryHandler(QUERY_RETRY_CONFIG, global_error_monitor)
            result = query_handler.execute_with_retry(success_operation)
            self.assertEqual(result['status'], 'success')
            
            print("✓ 错误处理器集成测试通过")
            
        except ImportError as e:
            print(f"⚠ 错误处理器模块导入失败: {e}")
        except Exception as e:
            print(f"✗ 错误处理器集成测试失败: {e}")
            raise
    
    def test_maker_channel_integration(self):
        """测试MakerChannelEnhanced集成"""
        try:
            # 添加当前目录到路径
            import sys
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            
            import maker_channel_enhanced
            
            # 创建模拟的HTTP客户端和配置
            mock_http = Mock()
            mock_config = {
                'max_rate': 1200,
                'trade_rate': 100,
                'strategy_mode': 'breakout'
            }
            
            # 创建MakerChannelEnhanced实例
            channel = maker_channel_enhanced.MakerChannelEnhanced(mock_http, mock_config)
            
            # 检查初始化
            self.assertIsNotNone(channel)
            self.assertIsNotNone(channel.trading_retry_handler)
            self.assertIsNotNone(channel.query_retry_handler)
            
            # 测试符号加载（模拟）
            # 由于没有真实的API连接，这里只检查方法存在
            self.assertTrue(hasattr(channel, 'load_symbols_with_cache'))
            
            # 检查增强功能是否正确初始化
            if hasattr(channel, 'enhanced_rate_limiter'):
                self.assertIsNotNone(channel.enhanced_rate_limiter)
            
            if hasattr(channel, 'enhanced_order_queue'):
                self.assertIsNotNone(channel.enhanced_order_queue)
            
            print("✓ MakerChannelEnhanced集成测试通过")
            
        except ImportError as e:
            print(f"⚠ MakerChannelEnhanced模块导入失败: {e}")
        except Exception as e:
            print(f"✗ MakerChannelEnhanced集成测试失败: {e}")
            raise
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        try:
            # 修正导入路径
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            
            import maker_channel_enhanced
            MakerChannelEnhanced = maker_channel_enhanced.MakerChannelEnhanced
            
            # 测试原有API是否仍然可用
            mock_http = Mock()
            maker = MakerChannelEnhanced(mock_http, self.test_config)
            
            # 检查原有属性是否存在
            self.assertTrue(hasattr(maker, 'query_limit'))
            self.assertTrue(hasattr(maker, 'trade_limit'))
            self.assertTrue(hasattr(maker, 'queue'))
            
            # 检查原有方法是否存在
            self.assertTrue(hasattr(maker, 'get_klines'))
            self.assertTrue(hasattr(maker, 'get_depth01pct'))
            self.assertTrue(hasattr(maker, 'place_maker_order'))
            self.assertTrue(hasattr(maker, 'place_stop_market'))
            
            print("✓ 向后兼容性测试通过")
            
        except Exception as e:
            print(f"✗ 向后兼容性测试失败: {e}")
            raise
    
    def test_performance_impact(self):
        """测试性能影响"""
        try:
            # 修正导入路径
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            
            import maker_channel_enhanced
            MakerChannelEnhanced = maker_channel_enhanced.MakerChannelEnhanced
            import time
            
            mock_http = Mock()
            mock_http.get.return_value = []
            
            # 测试初始化时间
            start_time = time.time()
            maker = MakerChannelEnhanced(mock_http, self.test_config)
            init_time = time.time() - start_time
            
            # 初始化时间应该在合理范围内（< 1秒）
            self.assertLess(init_time, 1.0)
            
            print(f"✓ 性能测试通过 - 初始化时间: {init_time:.3f}s")
            
        except Exception as e:
            print(f"✗ 性能测试失败: {e}")
            raise

def run_integration_tests():
    """运行集成测试"""
    print("=" * 60)
    print("开始运行集成测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 所有集成测试通过！")
        print(f"运行了 {result.testsRun} 个测试")
    else:
        print("❌ 部分测试失败")
        print(f"运行了 {result.testsRun} 个测试")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
    
    print("=" * 60)
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)
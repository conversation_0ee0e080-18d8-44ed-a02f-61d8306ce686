#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精度处理函数的正确性
"""

import decimal as dec

def _round_price(price, tick_size):
    """价格精度处理"""
    if price <= 0 or tick_size <= 0:
        return price
    dec.getcontext().prec = 18
    tick_d = dec.Decimal(str(tick_size))
    price_d = dec.Decimal(str(price))
    rounded = float((price_d / tick_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
    return max(rounded, tick_size)  # 确保价格至少为一个tick

def _round_qty(q, step, min_q):
    """数量精度处理"""
    if q <= 0 or step <= 0:
        return min_q
    dec.getcontext().prec = 18
    step_d = dec.Decimal(str(step))
    out = float((dec.Decimal(str(q)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
    return max(out, min_q)

def test_precision():
    """测试精度处理"""
    
    # 从测试结果中获取的实际参数
    test_cases = [
        {
            'symbol': 'ETHUSDT',
            'tick_size': 0.10,  # PRICE_FILTER
            'step_size': 0.001,  # LOT_SIZE
            'min_qty': 0.001,
            'min_notional': 100,
            'entry_price': 4007.5,
            'first_nominal': 100  # 假设配置值
        },
        {
            'symbol': 'BTCUSDT', 
            'tick_size': 0.10,
            'step_size': 0.001,
            'min_qty': 0.001,
            'min_notional': 100,
            'entry_price': 95000.0,
            'first_nominal': 100
        }
    ]
    
    for case in test_cases:
        print(f"\n=== 测试 {case['symbol']} ===")
        print(f"入场价格: {case['entry_price']}")
        print(f"名义价值: {case['first_nominal']}")
        print(f"价格精度: {case['tick_size']}")
        print(f"数量精度: {case['step_size']}")
        print(f"最小数量: {case['min_qty']}")
        print(f"最小名义价值: {case['min_notional']}")
        
        # 计算原始数量
        raw_qty = case['first_nominal'] / case['entry_price']
        print(f"原始数量: {raw_qty}")
        
        # 精度处理
        rounded_qty = _round_qty(raw_qty, case['step_size'], case['min_qty'])
        rounded_price = _round_price(case['entry_price'], case['tick_size'])
        
        print(f"处理后数量: {rounded_qty}")
        print(f"处理后价格: {rounded_price}")
        
        # 计算实际名义价值
        actual_notional = rounded_qty * rounded_price
        print(f"实际名义价值: {actual_notional}")
        
        # 检查是否满足最小名义价值要求
        if actual_notional < case['min_notional']:
            print(f"❌ 名义价值不足! 需要至少 {case['min_notional']}, 实际 {actual_notional}")
        else:
            print(f"✅ 名义价值满足要求")
            
        # 检查精度格式
        qty_str = str(rounded_qty)
        price_str = str(rounded_price)
        print(f"数量字符串: '{qty_str}'")
        print(f"价格字符串: '{price_str}'")
        
        # 检查小数位数
        qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
        price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
        
        # 计算允许的小数位数
        step_decimals = len(str(case['step_size']).split('.')[-1]) if '.' in str(case['step_size']) else 0
        tick_decimals = len(str(case['tick_size']).split('.')[-1]) if '.' in str(case['tick_size']) else 0
        
        print(f"数量小数位: {qty_decimals}, 允许: {step_decimals}")
        print(f"价格小数位: {price_decimals}, 允许: {tick_decimals}")
        
        if qty_decimals > step_decimals:
            print(f"❌ 数量精度超限! 小数位 {qty_decimals} > {step_decimals}")
        if price_decimals > tick_decimals:
            print(f"❌ 价格精度超限! 小数位 {price_decimals} > {tick_decimals}")

if __name__ == '__main__':
    test_precision()
#!/usr/bin/env python3
"""
终极精度修复方案
彻底解决PIPPINUSDT价格格式化为0的问题
"""

from decimal import Decimal, ROUND_DOWN, getcontext
import math

def ultimate_format_order_params(price, qty, tick_size, step_size, min_qty):
    """终极版本的下单参数格式化函数"""
    
    # 设置高精度
    getcontext().prec = 50
    
    def format_to_tick_precision(value, tick_size):
        """根据tick_size格式化价格"""
        if not tick_size or tick_size <= 0:
            return str(float(value))
        
        try:
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_tick = Decimal(str(tick_size))
            
            if decimal_value <= 0:
                return str(float(value))
            
            # 计算应该保留的小数位数
            tick_str = f"{tick_size:.20f}".rstrip('0')
            if '.' in tick_str:
                decimal_places = len(tick_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到tick_size的倍数
            steps = (decimal_value / decimal_tick).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 关键修复：如果steps为0但原值大于0，设为1
            if steps == 0 and decimal_value > 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_tick
            
            # 转换为字符串，保持正确的小数位数
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0，但至少保留一位小数
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result += '0'
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"价格格式化失败: {e}")
            return str(float(value))
    
    def format_to_step_precision(value, step_size, min_qty):
        """根据step_size格式化数量"""
        if not step_size or step_size <= 0:
            return str(float(value))
        
        try:
            # 确保不小于最小数量
            actual_value = max(float(value), float(min_qty) if min_qty else 0)
            
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(actual_value))
            decimal_step = Decimal(str(step_size))
            
            # 计算应该保留的小数位数
            step_str = f"{step_size:.20f}".rstrip('0')
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到step_size的倍数
            steps = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 确保至少为1步
            if steps == 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_step
            
            # 转换为字符串
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result = result[:-1]
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"数量格式化失败: {e}")
            return str(float(value))
    
    # 格式化价格和数量
    price_str = format_to_tick_precision(price, tick_size)
    qty_str = format_to_step_precision(qty, step_size, min_qty)
    
    return price_str, qty_str

def test_ultimate_fix():
    """测试终极修复方案"""
    print("=== 测试终极修复方案 ===")
    
    test_cases = [
        # PIPPINUSDT的实际情况
        (0.4, 236.417, 0.00001, 0.001, 0.001, "PIPPINUSDT实际参数"),
        (0.00005, 1000.0, 0.00001, 0.001, 0.001, "极小价格"),
        (0.000001, 5000000.0, 0.00001, 0.001, 0.001, "超小价格"),
        (1.23456, 50.0, 0.0001, 0.01, 0.01, "正常价格"),
        (0.123456789, 100.0, 0.0001, 0.01, 0.01, "高精度价格"),
    ]
    
    for price, qty, tick_size, step_size, min_qty, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: price={price}, qty={qty}")
        print(f"规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}")
        
        price_str, qty_str = ultimate_format_order_params(price, qty, tick_size, step_size, min_qty)
        print(f"输出: price_str='{price_str}', qty_str='{qty_str}'")
        
        # 验证结果
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            notional = price_val * qty_val
            
            print(f"验证: price_val={price_val}, qty_val={qty_val}")
            print(f"名义价值: {notional:.6f} USDT")
            
            # 检查是否为0
            if price_val <= 0:
                print("❌ 价格为0或负数！")
            elif qty_val <= 0:
                print("❌ 数量为0或负数！")
            elif notional < 5.0:
                print("⚠️  名义价值小于5 USDT")
            else:
                print("✅ 所有检查通过，应该可以成功下单")
                
        except Exception as e:
            print(f"❌ 验证失败: {e}")

def generate_final_method():
    """生成最终的方法代码"""
    print("\n=== 最终方法代码 ===")
    
    code = '''
def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
    """终极版本的下单参数格式化函数 - 彻底修复价格为0的问题"""
    from decimal import Decimal, ROUND_DOWN, getcontext
    
    # 设置高精度
    getcontext().prec = 50
    
    def format_to_tick_precision(value, tick_size):
        """根据tick_size格式化价格"""
        if not tick_size or tick_size <= 0:
            return str(float(value))
        
        try:
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_tick = Decimal(str(tick_size))
            
            if decimal_value <= 0:
                return str(float(value))
            
            # 计算应该保留的小数位数
            tick_str = f"{tick_size:.20f}".rstrip('0')
            if '.' in tick_str:
                decimal_places = len(tick_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到tick_size的倍数
            steps = (decimal_value / decimal_tick).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 关键修复：如果steps为0但原值大于0，设为1
            if steps == 0 and decimal_value > 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_tick
            
            # 转换为字符串，保持正确的小数位数
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0，但至少保留一位小数
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result += '0'
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            self.log.warning(f"价格格式化失败: {e}")
            return str(float(value))
    
    def format_to_step_precision(value, step_size, min_qty):
        """根据step_size格式化数量"""
        if not step_size or step_size <= 0:
            return str(float(value))
        
        try:
            # 确保不小于最小数量
            actual_value = max(float(value), float(min_qty) if min_qty else 0)
            
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(actual_value))
            decimal_step = Decimal(str(step_size))
            
            # 计算应该保留的小数位数
            step_str = f"{step_size:.20f}".rstrip('0')
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到step_size的倍数
            steps = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 确保至少为1步
            if steps == 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_step
            
            # 转换为字符串
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result = result[:-1]
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            self.log.warning(f"数量格式化失败: {e}")
            return str(float(value))
    
    try:
        # 格式化价格和数量
        price_str = format_to_tick_precision(price, tick_size)
        qty_str = format_to_step_precision(qty, step_size, min_qty)
        
        # 最终验证
        price_val = float(price_str)
        qty_val = float(qty_str)
        
        if price_val <= 0 or qty_val <= 0:
            self.log.error(f"格式化结果异常: price={price_str}, qty={qty_str}")
            return str(float(price)), str(float(qty))
        
        return price_str, qty_str
        
    except Exception as e:
        self.log.error(f"参数格式化失败: {e}")
        return str(float(price)), str(float(qty))
'''
    
    print(code)
    return code

if __name__ == '__main__':
    test_ultimate_fix()
    generate_final_method()
    
    print("\n=== 修复说明 ===")
    print("1. 使用高精度Decimal计算，避免浮点数误差")
    print("2. 正确计算小数位数，确保格式化精度")
    print("3. 关键修复：当向下取整为0时，强制设为1个最小单位")
    print("4. 添加多层验证和错误处理")
    print("5. 立即应用此修复到maker_channel_enhanced.py！")
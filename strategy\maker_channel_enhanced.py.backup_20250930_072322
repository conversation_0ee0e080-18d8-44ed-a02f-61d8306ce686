import pandas as pd
import numpy as np
import datetime
import time
import logging
import gc
import psutil
from collections import deque, namedtuple
from .rate_limiter import ApiRateLimiter
from .order_queue import OrderOperationQueue

class Tick3m:
    """3分钟K线合成器"""
    def __init__(self, sym: str):
        self.sym = sym
        # 使用首个tick时间作为锚点，确保以该时间起每180秒切桶
        self.anchor_ts = None
        self.bucket = 0
        self.bar = {'t': 0, 'o': 0, 'h': 0, 'l': 0, 'c': 0, 'v': 0}

    def add(self, price: float, qty: float, ts: int):
        if self.anchor_ts is None:
            self.anchor_ts = ts
            self.bucket = 0
            self.bar = {'t': self.anchor_ts, 'o': price, 'h': price, 'l': price, 'c': price, 'v': qty}
            return self.bar
        # 计算相对锚点的桶索引
        bucket_idx = int((ts - self.anchor_ts) // 180000)
        if bucket_idx != self.bucket:
            # 新桶
            self.bucket = bucket_idx
            self.bar = {'t': self.anchor_ts + bucket_idx * 180000, 'o': price, 'h': price, 'l': price, 'c': price, 'v': qty}
        else:
            # 更新当前桶
            self.bar['h'] = max(self.bar['h'], price)
            self.bar['l'] = min(self.bar['l'], price)
            self.bar['c'] = price
            self.bar['v'] += qty
        return self.bar

class MakerChannelEnhanced:
    def __init__(self, http, config):
        self.http = http
        self.cfg = config
        self.log = logging.getLogger('MakerChannelEnhanced')
        self.query_limit = ApiRateLimiter(config['max_rate'], 30)
        self.trade_limit = ApiRateLimiter(config['trade_rate'], 20)
        self.queue = OrderOperationQueue(self.trade_limit)
        
        # 缓存引擎
        self.cache_mgr = {}
        self.cache_ttl = 300
        self.mem_max = 0.85
        self.cache_max = 1000
        
        # 止血贴
        self.stop_cache = {}
        self.MAX_STOP = 2
        self.FREEZE_HR = 4
        
        # 仓位字典
        self.pos = None
        self.active_limit = {}
        
        # 3mTick合成器
        self.tick3m = {}
        self.bars_3m = {}
        
        # 全市场上市时间缓存
        self.symbols_info = {}
        self.last_cold_start = 0
        self.cold_start_interval = 86400
        # 今日唯一入场券与选择
        self.selected_symbol_today = None
        self.selected_day = None
        self.entry_permit_used = False
        # 入场与风控门槛
        # 允许从配置覆盖默认值（无则使用默认）
        # 新增：策略模式与新币参数
        self.strategy_mode = str(self.cfg.get('strategy_mode', 'breakout'))
        self.new_coin_max_age_days = int(self.cfg.get('new_coin_max_age_days', 90))
        _rng = self.cfg.get('new_coin_momentum_range', [10, 50])
        if isinstance(_rng, (list, tuple)) and len(_rng) >= 2:
            self.new_coin_momentum_range = (float(_rng[0]), float(_rng[1]))
        else:
            self.new_coin_momentum_range = (10.0, 50.0)
        self.new_coin_depth_min = float(self.cfg.get('new_coin_depth_min', 200000))
        
        # 超新币模式参数
        self.ultra_new_coin_mode = bool(self.cfg.get('ultra_new_coin_mode', False))
        if self.ultra_new_coin_mode:
            # 超新币模式下使用专用参数
            _ultra_rng = self.cfg.get('ultra_new_coin_momentum_range', [0, 200])
            if isinstance(_ultra_rng, (list, tuple)) and len(_ultra_rng) >= 2:
                self.new_coin_momentum_range = (float(_ultra_rng[0]), float(_ultra_rng[1]))
            self.new_coin_depth_min = float(self.cfg.get('ultra_new_coin_depth_min', 50000))
            self.SCORE_GATE = config.get('ultra_new_coin_score_gate', 3.0)
            self.VOL_MULT = config.get('ultra_new_coin_vol_mult', 0.8)
            self.PULLBACK_FACTOR = config.get('ultra_new_coin_pullback_factor', 1.0000)
            self.top1_refresh_interval = pd.Timedelta(minutes=self.cfg.get('ultra_new_coin_top1_refresh_minutes', 3))
            self.entry_mac_params = self.cfg.get('ultra_new_coin_entry_mac_params', [15, 15, 1, 1])
        else:
            # 普通模式参数
            self.SCORE_GATE = config.get('score_gate', 6.5 if self.strategy_mode == 'new_coin_burst' else 7.0)
            self.VOL_MULT = config.get('vol_mult', 1.2)
            self.PULLBACK_FACTOR = config.get('pullback_factor', 0.9962)
        
        self.RSI_MAX = config.get('rsi_max', 85)
        self.RSI_MIN = config.get('rsi_min', 45)
        # AB快照标志位（每日一次）
        self._score_snapshot_day = None
        self._score_snapshot_done = False
        # 快照导出与回退逻辑开关（默认关闭）
        self.snapshot_export_enabled = bool(self.cfg.get('snapshot_export_enabled', False))
        # 每小时Top1刷新与入场通道配置
        self._last_top1_refresh = None
        if not self.ultra_new_coin_mode:
            # 普通模式下的刷新间隔和通道参数
            self.top1_refresh_interval = pd.Timedelta(minutes=self.cfg.get('top1_refresh_minutes', 15 if self.strategy_mode == 'new_coin_burst' else 60))
            self.entry_mac_params = self.cfg.get('entry_mac_params', [21, 21, 1, 1] if self.strategy_mode == 'new_coin_burst' else [33, 33, 1, 1])
        # 超新币模式的参数已在上面设置
        self.entry_channel_type = str(self.cfg.get('entry_channel_type', 'mac' if self.strategy_mode == 'new_coin_burst' else 'donchian')).lower()
        self.entry_donchian_period = int(self.cfg.get('entry_donchian_period', 20))

    def get_symbol_age_minutes(self, symbol: str) -> float:
        """获取币种年龄（分钟）"""
        self.load_symbols_with_cache()
        info = self.symbols_info.get(symbol)
        return info['age_days'] * 1440 if info else 999 * 1440
    
    def get_symbol_age_minutes_from_df(self, df) -> float:
        """从K线数据计算币种年龄（分钟）"""
        try:
            first_time = df.index[0]
            now = pd.Timestamp.utcnow()
            age_minutes = (now - first_time).total_seconds() / 60
            return max(1, age_minutes)  # 最小1分钟，避免除零
        except Exception:
            return 999 * 1440  # 默认999天
    
    def age_score_from_minutes(self, age_minutes: float) -> float:
        """根据分钟年龄计算年龄评分（0-10分）"""
        # 转换为天数用于判断
        age_days = age_minutes / 1440
        
        if 7 <= age_days <= 365:           # 7天-365天：最佳年龄段
            return 10.0
        elif age_days > 365:               # >365天：老币衰减
            return max(1.0, 10.0 / (1 + (age_days - 365) / 365.0))
        elif 0.25 <= age_days < 7:         # 6小时-7天：新币也给高分
            return 8.0
        else:                              # <6小时：极新币
            return 5.0

    # ---------- 公共工具 ----------
    def cache_get(self, key, loader, *args, **kwargs):
        """通用缓存获取：支持TTL与内存阈值清理"""
        try:
            now = time.time()
            rec = self.cache_mgr.get(key)
            if rec and now < rec.get('ttl', 0):
                return rec.get('val')
            # 加载新值
            val = loader(*args, **kwargs) if (args or kwargs) else loader()
            # 写入缓存
            self.cache_mgr[key] = {'val': val, 'ttl': now + self.cache_ttl}
            # 简单内存与条目上限控制：超过阈值则清理一半最早条目
            vm = psutil.virtual_memory().percent / 100.0
            if vm > self.mem_max or len(self.cache_mgr) > self.cache_max:
                # 这里使用插入顺序的前半截进行清理
                for k in list(self.cache_mgr.keys())[: max(1, len(self.cache_mgr) // 2)]:
                    self.cache_mgr.pop(k, None)
            return val
        except Exception as e:
            self.log.error(f"cache_get失败 {key}: {e}")
            # 发生错误时直接回退到loader
            try:
                return loader(*args, **kwargs) if (args or kwargs) else loader()
            except Exception:
                return None

    def load_symbols_with_cache(self):
        """加载并缓存交易所币种信息（带冷启动间隔）"""
        try:
            now_ts = time.time()
            if self.symbols_info and (now_ts - self.last_cold_start) < self.cold_start_interval:
                self.log.info(f"使用缓存的币种信息，共 {len(self.symbols_info)} 个币种")
                return
            
            self.log.info("开始加载币种信息...")
            info = self.http.get('/fapi/v1/exchangeInfo')
            symbols_info = {}
            now_ms = int(time.time() * 1000)
            
            if info and isinstance(info, dict) and 'symbols' in info:
                self.log.info(f"获取到 {len(info['symbols'])} 个原始币种信息")
                for s in info['symbols']:
                    # 只保留正在交易的USDT永续合约
                    if s.get('status') != 'TRADING':
                        continue
                    sym = s.get('symbol')
                    if not sym or not sym.endswith('USDT'):
                        continue
                    if s.get('contractType') != 'PERPETUAL':
                        continue
                    onboard = s.get('onboardDate')
                    if onboard:
                        age_days = max(0.0, (now_ms - int(onboard)) / (86400 * 1000))
                    else:
                        # onboardDate缺失的币种视为老币（年龄设为999天）
                        age_days = 999.0
                    symbols_info[sym] = {
                        'age_days': age_days,
                        'contractType': s.get('contractType'),
                        'status': s.get('status')
                    }
                self.log.info(f"过滤后得到 {len(symbols_info)} 个USDT永续合约")
            else:
                self.log.error("获取币种信息失败或格式错误")
                
            self.symbols_info = symbols_info
            self.last_cold_start = now_ts
            self.log.info("币种信息加载完成")
        except Exception as e:
            self.log.error(f"加载币种信息异常: {e}")
            raise

    def _round_price(self, price, tick_size):
        """价格精度处理"""
        if price <= 0 or tick_size <= 0:
            return price
        import decimal as dec
        dec.getcontext().prec = 18
        tick_d = dec.Decimal(str(tick_size))
        price_d = dec.Decimal(str(price))
        rounded = float((price_d / tick_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
        return max(rounded, tick_size)  # 确保价格至少为一个tick

    def _round_qty(self, q, step, min_q, price=None, min_notional=None):
        """数量精度处理，考虑最小名义价值要求"""
        if q <= 0 or step <= 0:
            return min_q
        import decimal as dec
        dec.getcontext().prec = 18
        step_d = dec.Decimal(str(step))
        
        # 先按步长向下取整
        rounded_down = float((dec.Decimal(str(q)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
        rounded_down = max(rounded_down, min_q)
        
        # 如果提供了价格和最小名义价值，检查是否满足要求
        if price and min_notional:
            notional = rounded_down * price
            if notional < min_notional:
                # 向上调整到满足最小名义价值的数量
                required_qty = min_notional / price
                rounded_up = float((dec.Decimal(str(required_qty)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_UP) * step_d)
                return max(rounded_up, min_q)
        
        return rounded_down

    def _calculate_atr(self, high, low, close, length=14):
        """计算平均真实波幅(ATR)"""
        tr = pd.DataFrame({
            'hl': high - low,
            'hc': abs(high - close.shift(1)),
            'lc': abs(low - close.shift(1))
        }).max(axis=1)
        atr = tr.rolling(window=length).mean()
        return atr

    def _compute_rsi(self, close: pd.Series, length: int = 14) -> pd.Series:
        """计算RSI"""
        delta = close.diff()
        gain = (delta.clip(lower=0)).rolling(length).mean()
        loss = (-delta.clip(upper=0)).rolling(length).mean()
        rs = gain / (loss + 1e-12)
        rsi = 100 - 100 / (1 + rs)
        return rsi

    def _candle_wick_ratio(self, h: float, l: float, o: float, c: float):
        """返回上下影线比例 (upper_ratio, lower_ratio)"""
        rng = max(h - l, 1e-12)
        upper = h - max(o, c)
        lower = min(o, c) - l
        return upper / rng, lower / rng

    def get_klines(self, symbol, interval='15m', limit=200):
        """带缓存的K线获取"""
        return self.cache_get(f"kl_{symbol}_{interval}_{limit}", self._raw_klines, symbol, interval, limit)

    def _raw_klines(self, symbol, interval, limit):
        """原始K线获取"""
        self.query_limit.acquire()
        k = self.http.get('/fapi/v1/klines', {'symbol': symbol, 'interval': interval, 'limit': limit})
        if k and isinstance(k, list):
            df = pd.DataFrame(k, columns=['t', 'o', 'h', 'l', 'c', 'v', 'ct', 'q', 'n', 'V', 'qV', 'ignore'])
            df[['o', 'h', 'l', 'c', 'v']] = df[['o', 'h', 'l', 'c', 'v']].astype(float)
            df['t'] = pd.to_datetime(df['t'], unit='ms')
            return df.set_index('t')
        return pd.DataFrame()

    def get_depth01pct(self, symbol):
        """带缓存的深度获取"""
        return self.cache_get(f"depth_{symbol}", self._raw_depth, symbol)

    def _raw_depth(self, symbol):
        """原始深度获取逻辑已按±0.2%价带名义价值实现，无需变动"""
        self.query_limit.acquire()
        book = self.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 100})
        if not book or 'bids' not in book or 'asks' not in book:
            return 0
        bids, asks = book.get('bids') or [], book.get('asks') or []
        if not bids or not asks:
            return 0
        try:
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            mid = (best_bid + best_ask) / 2
        except Exception:
            return 0
        window = 0.002  # 0.2%
        bid_depth = sum(float(v) for p, v in bids if float(p) >= mid * (1 - window))
        ask_depth = sum(float(v) for p, v in asks if float(p) <= mid * (1 + window))
        depth_notional = mid * min(bid_depth, ask_depth)
        return depth_notional

    def ensure_account_params(self, symbol: str) -> bool:
        """在开仓前检查并设置账户参数：逐仓保证金 + 杠杆不超过3倍；并记录交易规则最大杠杆"""
        try:
            # 查询当前仓位风险，获取保证金模式与杠杆
            self.query_limit.acquire()
            pos_risk = self.http.get('/fapi/v2/positionRisk', {'symbol': symbol})
            current_isolated = None
            current_leverage = None
            if isinstance(pos_risk, list) and pos_risk:
                rec = pos_risk[0]
                try:
                    if 'isolated' in rec:
                        current_isolated = bool(rec.get('isolated'))
                    elif 'marginType' in rec:
                        current_isolated = str(rec.get('marginType')).upper() == 'ISOLATED'
                except Exception:
                    current_isolated = None
                try:
                    if 'leverage' in rec:
                        current_leverage = int(float(rec.get('leverage')))
                except Exception:
                    current_leverage = None

            # 若不是逐仓，则切换为逐仓
            if not current_isolated:
                self.trade_limit.acquire()
                res_mt = self.http.post('/fapi/v1/marginType', {'symbol': symbol, 'marginType': 'ISOLATED'})
                if isinstance(res_mt, dict) and ('code' in res_mt):
                    code = int(res_mt.get('code') or 0)
                    msg = str(res_mt.get('msg') or '')
                    # -4046 表示无需更改或已为逐仓，忽略该错误
                    if code in (-4046,):
                        self.log.info(f"{symbol} 保证金模式无需更改：{msg}")
                    else:
                        self.log.error(f"{symbol} 设置逐仓失败({code}): {msg}")
                        return False
                else:
                    self.log.info(f"{symbol} 保证金模式已设置为逐仓")

            else:
                self.log.info(f"{symbol} 当前已为逐仓保证金模式")

            # 查询交易规则最大杠杆（用于记录）
            try:
                self.query_limit.acquire()
                lb = self.http.get('/fapi/v1/leverageBracket', {'symbol': symbol})
                if isinstance(lb, list) and lb:
                    brackets = lb[0].get('brackets') if isinstance(lb[0], dict) else None
                    if brackets:
                        max_rule_lev = max(int(b.get('initialLeverage') or 0) for b in brackets if isinstance(b, dict))
                        self.log.info(f"{symbol} 交易规则最大杠杆: {max_rule_lev}x")
            except Exception as e:
                self.log.warning(f"{symbol} 查询最大杠杆失败: {e}")

            # 若当前杠杆超过3倍，则设置为不超过3倍（取配置与3的较小值）
            target_lev = min(int(self.cfg.get('leverage', 3)), 3)
            if (current_leverage is None) or (current_leverage > target_lev):
                self.trade_limit.acquire()
                res_lev = self.http.post('/fapi/v1/leverage', {'symbol': symbol, 'leverage': target_lev})
                if isinstance(res_lev, dict) and ('leverage' in res_lev):
                    self.log.info(f"{symbol} 杠杆设置为 {int(res_lev.get('leverage') or target_lev)}x")
                elif isinstance(res_lev, dict) and ('code' in res_lev):
                    code = int(res_lev.get('code') or 0)
                    msg = str(res_lev.get('msg') or '')
                    self.log.error(f"{symbol} 设置杠杆失败({code}): {msg}")
                    return False
                else:
                    self.log.info(f"{symbol} 杠杆设置完成为 ≤{target_lev}x")

            else:
                self.log.info(f"{symbol} 当前杠杆 {current_leverage}x，不超过 {target_lev}x")

            # 最终条件判定
            final_ok = True
            if current_isolated is False:
                final_ok = False
            if current_leverage is not None and current_leverage > 3:
                final_ok = False
            return final_ok
        except Exception as e:
            self.log.error(f"{symbol} 账户参数检查/设置异常: {e}")
            return False

    def place_maker_order(self, symbol, side, size, price):
        """限价挂单（GTC-30min）——统一精度裁剪与失败重试"""
        def op():
            try:
                # 开仓前账户参数检查与设置
                ok = self.ensure_account_params(symbol)
                if not ok:
                    self.log.error(f"{symbol} 开仓前账户参数不满足：需逐仓且杠杆≤3x，已跳过本次下单")
                    return {'code': -4000, 'msg': 'Account params not satisfied: isolated + leverage<=3'}
                # 从缓存加载交易规则（filters）
                try:
                    self.load_symbols_with_cache()
                except Exception:
                    pass
                s_info = (self.symbols_info or {}).get(symbol, {})
                filters = s_info.get('filters') or []
                tick_size = None
                step_size = None
                min_qty = None
                min_notional = None
                for f in filters:
                    ft = f.get('filterType')
                    if ft == 'PRICE_FILTER':
                        tick_size = float(f.get('tickSize', 0) or 0)
                    elif ft == 'LOT_SIZE':
                        step_size = float(f.get('stepSize', 0) or 0)
                        min_qty = float(f.get('minQty', 0) or 0)
                    elif ft == 'MIN_NOTIONAL':
                        min_notional = float(f.get('notional') or f.get('minNotional') or 0)

                # 统一裁剪
                r_price = self._round_price(price, tick_size) if tick_size else float(price)
                r_qty = self._round_qty(size, step_size, min_qty, r_price, min_notional) if step_size and min_qty else float(size)

                # 逐步降档重试：100%→75%→50%→25%
                retry_scales = [1.0, 0.75, 0.5, 0.25]
                last_error = None
                for scale in retry_scales:
                    q_try = r_qty * scale
                    q_try = self._round_qty(q_try, step_size, min_qty, r_price, min_notional) if step_size and min_qty else q_try
                    # 用Decimal量化生成字符串，避免浮点误差
                    import decimal as dec
                    dec.getcontext().prec = 18
                    if tick_size:
                        price_dec = (dec.Decimal(str(r_price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
                        price_str = format(price_dec.normalize(), 'f')
                    else:
                        price_str = str(r_price)
                    if step_size:
                        qty_dec = (dec.Decimal(str(q_try)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
                        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
                        qty_str = format(qty_dec.normalize(), 'f')
                    else:
                        qty_str = str(q_try)
                    order = self.http.post('/fapi/v1/order', {
                        'symbol': symbol,
                        'side': side,
                        'type': 'LIMIT',
                        'quantity': qty_str,
                        'price': price_str,
                        'timeInForce': 'GTC',
                        'reduceOnly': False
                    })
                    if order and order.get('orderId'):
                        self.active_limit[symbol] = {'id': order['orderId'], 'price': r_price, 'ttl': time.time() + 1800}
                        self.log.info(f"限价挂单 {side} {q_try} @ {r_price:.6f} 30minTTL")
                        return order
                    # 捕获错误并决定是否继续降档
                    if isinstance(order, dict) and ('code' in order):
                        last_error = order
                        code = int(order.get('code') or 0)
                        msg = str(order.get('msg') or '')
                        if code in (-2019, -1111):
                            # 保证金不足或精度错误，继续尝试更小数量或已裁剪后再次尝试
                            self.log.warning(f"下单失败({code}): {msg} → 尝试降档 {int(scale*100)}% 数量")
                            continue
                        else:
                            # 其它错误不重试
                            self.log.warning(f"下单失败({code}): {msg}")
                            return order
                    else:
                        # 未返回结构化错误，直接返回用于上层记录
                        return order
                # 所有重试失败
                if last_error:
                    self.log.error(f"所有重试失败: code={last_error.get('code')} msg={last_error.get('msg')}")
                else:
                    self.log.error("所有重试失败: 未知错误")
                return last_error
            except Exception as e:
                self.log.error(f"限价挂单异常 {symbol}: {e}")
                return None
        self.queue.add_operation(op)

    def place_stop_market(self, symbol, side, size, stop_price):
        """止损市价单——统一精度裁剪与失败重试"""
        def op():
            try:
                # 开仓前账户参数检查与设置（止损同样遵循约束）
                ok = self.ensure_account_params(symbol)
                if not ok:
                    self.log.error(f"{symbol} 止损前账户参数不满足：需逐仓且杠杆≤3x，已跳过本次止损下单")
                    return {'code': -4000, 'msg': 'Account params not satisfied: isolated + leverage<=3'}
                # 加载交易规则
                try:
                    self.load_symbols_with_cache()
                except Exception:
                    pass
                s_info = (self.symbols_info or {}).get(symbol, {})
                filters = s_info.get('filters') or []
                tick_size = None
                step_size = None
                min_qty = None
                min_notional = None
                for f in filters:
                    ft = f.get('filterType')
                    if ft == 'PRICE_FILTER':
                        tick_size = float(f.get('tickSize', 0) or 0)
                    elif ft == 'LOT_SIZE':
                        step_size = float(f.get('stepSize', 0) or 0)
                        min_qty = float(f.get('minQty', 0) or 0)
                    elif ft == 'MIN_NOTIONAL':
                        min_notional = float(f.get('notional') or f.get('minNotional') or 0)

                r_stop = self._round_price(stop_price, tick_size) if tick_size else float(stop_price)
                r_qty = self._round_qty(size, step_size, min_qty, r_stop, min_notional) if step_size and min_qty else float(size)

                retry_scales = [1.0, 0.75, 0.5, 0.25]
                last_error = None
                for scale in retry_scales:
                    q_try = r_qty * scale
                    q_try = self._round_qty(q_try, step_size, min_qty, r_stop, min_notional) if step_size and min_qty else q_try
                    import decimal as dec
                    dec.getcontext().prec = 18
                    if tick_size:
                        stop_dec = (dec.Decimal(str(r_stop)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
                        stop_str = format(stop_dec.normalize(), 'f')
                    else:
                        stop_str = str(r_stop)
                    if step_size:
                        qty_dec = (dec.Decimal(str(q_try)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
                        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
                        qty_str = format(qty_dec.normalize(), 'f')
                    else:
                        qty_str = str(q_try)
                    order = self.http.post('/fapi/v1/order', {
                        'symbol': symbol,
                        'side': side,
                        'type': 'STOP_MARKET',
                        'quantity': qty_str,
                        'stopPrice': stop_str,
                        'reduceOnly': True
                    })
                    if order and order.get('orderId'):
                        self.log.info(f"止损触发 {side} qty={q_try} stop={r_stop:.6f}")
                        return order
                    if isinstance(order, dict) and ('code' in order):
                        last_error = order
                        code = int(order.get('code') or 0)
                        msg = str(order.get('msg') or '')
                        if code in (-2019, -1111):
                            self.log.warning(f"止损下单失败({code}): {msg} → 尝试降档 {int(scale*100)}% 数量")
                            continue
                        else:
                            self.log.warning(f"止损下单失败({code}): {msg}")
                            return order
                    else:
                        return order
                if last_error:
                    self.log.error(f"止损所有重试失败: code={last_error.get('code')} msg={last_error.get('msg')}")
                else:
                    self.log.error("止损所有重试失败: 未知错误")
                return last_error
            except Exception as e:
                self.log.error(f"止损市价单异常 {symbol}: {e}")
                return None
        self.queue.add_operation(op)

    # ---------- 龙头池：24h涨幅前25+成交额前25 ----------
    def get_top50_market_leaders(self):
        """获取市场龙头币种（带错误处理 + Fallback）"""
        try:
            tickers = self.http.get('/fapi/v1/ticker/24hr')
            if not tickers or not isinstance(tickers, list):
                self.log.warning("获取行情数据失败，使用fallback: symbols_info.keys()[:50]")
                try:
                    self.load_symbols_with_cache()
                except Exception as e2:
                    self.log.error(f"fallback加载币种信息失败: {e2}")
                return list(self.symbols_info.keys())[:50]

            # 原始逻辑：涨幅Top25与成交额Top25的并集，再与交易所USDT永续交集
            top_g = sorted([
                t for t in tickers
                if t.get('symbol', '').endswith('USDT') and float(t.get('priceChangePercent', 0)) > 0
            ], key=lambda x: float(x.get('priceChangePercent', 0)), reverse=True)[:25]
            top_v = sorted([
                t for t in tickers if t.get('symbol', '').endswith('USDT')
            ], key=lambda x: float(x.get('quoteVolume', 0)), reverse=True)[:25]

            candidates = {t['symbol'] for t in (top_g + top_v) if 'symbol' in t}
            symbols = [
                s for s in candidates
                if s in self.symbols_info and self.symbols_info[s].get('contractType') == 'PERPETUAL'
            ]
            if symbols:
                if self.strategy_mode == 'new_coin_burst':
                    # 新币优先过滤
                    try:
                        self.load_symbols_with_cache()
                    except Exception:
                        pass
                    min_pct, max_pct = self.new_coin_momentum_range
                    filtered = []
                    for s in symbols:
                        info = self.symbols_info.get(s, {})
                        age = info.get('age_days', 999)
                        if age > self.new_coin_max_age_days:
                            continue
                        t = next((t for t in tickers if t.get('symbol') == s), None)
                        chg24 = float(t.get('priceChangePercent', 0)) if t else 0.0
                        if not (min_pct <= chg24 <= max_pct):
                            continue
                        depth = self.get_depth01pct(s)
                        if depth < self.new_coin_depth_min:
                            continue
                        df15 = self.get_klines(s, '15m', 200)
                        if df15 is None or len(df15) < 50:
                            continue
                        ema = df15['c'].ewm(span=self.entry_mac_params[0]).mean().iloc[-1]
                        atr = self._calculate_atr(df15['h'], df15['l'], df15['c'], length=self.entry_mac_params[1]).iloc[-1]
                        upper = ema + atr * self.entry_mac_params[2]
                        lower = ema - atr * self.entry_mac_params[3]
                        close = df15['c'].iloc[-1]
                        pos = (close - lower) / (upper - lower + 1e-8)
                        if pos >= 0.6 or close >= upper:
                            filtered.append(s)
                    if filtered:
                        return filtered[:50]
                return symbols[:50]

            # Fallback 1：仅基于成交额Top排序（不要求涨幅>0），过滤为USDT永续TRADING
            try:
                self.load_symbols_with_cache()
            except Exception as e2:
                self.log.error(f"fallback加载币种信息失败: {e2}")
            vol_sorted = sorted([
                t for t in tickers
                if t.get('symbol', '').endswith('USDT')
                and t.get('symbol') in self.symbols_info
                and self.symbols_info[t['symbol']].get('contractType') == 'PERPETUAL'
            ], key=lambda x: float(x.get('quoteVolume', 0)), reverse=True)[:50]
            if vol_sorted:
                symbols_vs = [t['symbol'] for t in vol_sorted]
                if self.strategy_mode == 'new_coin_burst':
                    min_pct, max_pct = self.new_coin_momentum_range
                    filtered = []
                    for s in symbols_vs:
                        info = self.symbols_info.get(s, {})
                        age = info.get('age_days', 999)
                        if age > self.new_coin_max_age_days:
                            continue
                        t = next((t for t in tickers if t.get('symbol') == s), None)
                        chg24 = float(t.get('priceChangePercent', 0)) if t else 0.0
                        if not (min_pct <= chg24 <= max_pct):
                            continue
                        depth = self.get_depth01pct(s)
                        if depth < self.new_coin_depth_min:
                            continue
                        df15 = self.get_klines(s, '15m', 200)
                        if df15 is None or len(df15) < 50:
                            continue
                        ema = df15['c'].ewm(span=self.entry_mac_params[0]).mean().iloc[-1]
                        atr = self._calculate_atr(df15['h'], df15['l'], df15['c'], length=self.entry_mac_params[1]).iloc[-1]
                        upper = ema + atr * self.entry_mac_params[2]
                        lower = ema - atr * self.entry_mac_params[3]
                        close = df15['c'].iloc[-1]
                        pos = (close - lower) / (upper - lower + 1e-8)
                        if pos >= 0.6 or close >= upper:
                            filtered.append(s)
                    if filtered:
                        return filtered[:50]
                return symbols_vs

            # Fallback 2：退化为交易所USDT永续列表的前50
            symbols_fb = list(self.symbols_info.keys())[:50]
            if self.strategy_mode == 'new_coin_burst':
                filtered = []
                for s in symbols_fb:
                    info = self.symbols_info.get(s, {})
                    age = info.get('age_days', 999)
                    if age > self.new_coin_max_age_days:
                        continue
                    depth = self.get_depth01pct(s)
                    if depth < self.new_coin_depth_min:
                        continue
                    df15 = self.get_klines(s, '15m', 200)
                    if df15 is None or len(df15) < 50:
                        continue
                    ema = df15['c'].ewm(span=self.entry_mac_params[0]).mean().iloc[-1]
                    atr = self._calculate_atr(df15['h'], df15['l'], df15['c'], length=self.entry_mac_params[1]).iloc[-1]
                    upper = ema + atr * self.entry_mac_params[2]
                    lower = ema - atr * self.entry_mac_params[3]
                    close = df15['c'].iloc[-1]
                    pos = (close - lower) / (upper - lower + 1e-8)
                    if pos >= 0.6 or close >= upper:
                        filtered.append(s)
                return filtered[:50] if filtered else symbols_fb
            return symbols_fb
        except Exception as e:
            self.log.error(f"获取龙头币种失败: {e}")
            try:
                self.load_symbols_with_cache()
                return list(self.symbols_info.keys())[:50]
            except Exception:
                return []

    # 追加：次新币突破入池（增强版）
    def get_newcoin_breakthrough(self, max_age=120):
        try:
            self.load_symbols_with_cache()
        except Exception:
            pass
        out = []
        for s, info in (self.symbols_info or {}).items():
            try:
                if info.get('age_days', 999) > max_age:
                    continue
                df = self.cache_get(f"kl15_{s}_50", self._raw_klines, s, '15m', 50)
                if df is None or len(df) < 30:
                    continue
                
                # 计算通道上下轨（20周期）
                upper = df['h'].rolling(20).max().iloc[-1]
                lower = df['l'].rolling(20).min().iloc[-1]
                close = df['c'].iloc[-1]
                high = df['h'].iloc[-1]
                
                # 检测突破条件（放宽条件）：
                # 1. 收盘价接近或突破上轨（99.5%以上，从99.8%放宽）
                # 2. 最高价突破上轨
                # 3. 近期有明显上涨动量（25根K线涨幅>2%，从3%放宽）
                breakthrough_close = close >= upper * 0.995
                breakthrough_high = high >= upper
                momentum_check = close / df['c'].iloc[-25] > 1.02
                
                # 检测从下轨向上的突破：
                # 4. 当前价格位于通道中上部（50%以上位置，从60%放宽）
                channel_position = (close - lower) / (upper - lower + 1e-8)
                in_upper_channel = channel_position >= 0.5
                
                # 5. 成交量验证（最近5根K线平均成交量 > 前20根平均成交量的1.1倍，从1.2倍放宽）
                recent_vol = df['v'].iloc[-5:].mean()
                base_vol = df['v'].iloc[-25:-5].mean()
                volume_surge = recent_vol > base_vol * 1.1
                
                if (breakthrough_close or breakthrough_high or in_upper_channel) and momentum_check:
                    # 放宽成交量条件：如果其他条件都满足，成交量条件变为可选
                    if volume_surge or (breakthrough_close and breakthrough_high):
                        out.append(s)
                    
            except Exception:
                continue
        return out[:50]  # 扩大到50个候选

    # ---------- 增强评分（0-10分） ----------
    def compute_scores(self, df, age_days):
        close = df['c'].iloc[-1]
        # MAC通道：EMA33 ± ATR33
        ema33 = df['c'].ewm(span=33).mean().iloc[-1]
        atr33_series = self._calculate_atr(df['h'], df['l'], df['c'], length=33)
        atr33 = float(atr33_series.iloc[-1]) if hasattr(atr33_series, 'iloc') else float(atr33_series)
        upper = ema33 + atr33
        lower = ema33 - atr33
        # 通道分（相对区间位置）+ 新币补偿
        channel_raw = (close - lower) / (upper - lower + 1e-8) * 10
        channel_score = max(0, min(10, channel_raw))
        channel_score = max(0, min(10, channel_score + (2 if age_days < 30 else 0)))
        # 动量分：交易所24h涨跌幅
        try:
            sym = df.name if hasattr(df, 'name') else None
            t24 = self.http.get('/fapi/v1/ticker/24hr', {'symbol': sym}) if sym else None
            chg24 = float(t24.get('priceChangePercent', 0)) if t24 else 0.0
        except Exception:
            chg24 = 0.0
        momentum_score = 10 * max(0, min(1, (chg24 + 5) / 10))
        # 波动分：ATR相对价差
        atr14 = (df['h'] - df['l']).rolling(14).mean().iloc[-1]
        vol_score = max(0, 10 - (atr14 / close) * 250)
        # 深度分：分段线性（新币20万满分，老币100万满分）
        depth = self.get_depth01pct(df.name if hasattr(df, 'name') else 'sym')
        depth_score = min(10, depth / (2e5 if age_days < 30 else 1e6))
        # 年龄分：使用真实上线时间，避免K线时间范围导致的错误计算
        # 优先使用symbols_info中的真实年龄，fallback到K线计算
        symbol = df.name if hasattr(df, 'name') else 'unknown'
        real_age_days = self.symbols_info.get(symbol, {}).get('age_days', None)
        if real_age_days is not None:
            age_minutes = real_age_days * 1440  # 天转分钟
        else:
            age_minutes = self.get_symbol_age_minutes_from_df(df)
        age_score = self.age_score_from_minutes(age_minutes) * 0.1  # 缩放到0-1范围
        # 总分加权
        total = channel_score * 0.4 + momentum_score * 0.3 + vol_score * 0.15 + depth_score * 0.1 + age_score * 0.05
        total = max(0, min(10, total))
        M = 2 if total >= 7 else 1 if total >= 5 else 0
        from collections import namedtuple
        ScoreDetails = namedtuple('ScoreDetails', ['total', 'channel', 'momentum', 'vol', 'depth', 'age', 'M'])
        return ScoreDetails(total, channel_score, momentum_score, vol_score, depth_score, age_score, M)

    # ---------- 新币爆发评分（0-10分） ----------
    def compute_scores_new(self, df, age_days):
        close = df['c'].iloc[-1]
        # MAC通道：使用 entry_mac_params
        try:
            ema_span = int(self.entry_mac_params[0])
            atr_len = int(self.entry_mac_params[1])
            mul_u = float(self.entry_mac_params[2])
            mul_l = float(self.entry_mac_params[3])
        except Exception:
            ema_span, atr_len, mul_u, mul_l = 21, 21, 1.0, 1.0
        ema_now = df['c'].ewm(span=ema_span).mean().iloc[-1]
        atr_series = self._calculate_atr(df['h'], df['l'], df['c'], length=atr_len)
        atr_now = float(atr_series.iloc[-1]) if hasattr(atr_series, 'iloc') else float(atr_series)
        upper = ema_now + atr_now * mul_u
        lower = ema_now - atr_now * mul_l
        pos = (close - lower) / (upper - lower + 1e-8)
        channel_score = max(0, min(10, pos * 10))
        # 已突破则给微小奖励
        if close >= upper:
            channel_score = min(10, channel_score + 1.0)
        # 年龄线性补偿：0天→+2，max_age→0
        try:
            max_age = float(self.new_coin_max_age_days)
            age_norm = max(0.0, min(max_age, float(age_days)))
            age_bonus = max(0.0, 2.0 * (1.0 - age_norm / max_age))
        except Exception:
            age_norm, max_age, age_bonus = float(age_days), 90.0, 0.0
        channel_score = max(0, min(10, channel_score + age_bonus))
        # 动量分：按区间线性映射到0-10
        try:
            sym = df.name if hasattr(df, 'name') else None
            t24 = self.http.get('/fapi/v1/ticker/24hr', {'symbol': sym}) if sym else None
            chg24 = float(t24.get('priceChangePercent', 0)) if t24 else 0.0
        except Exception:
            chg24 = 0.0
        min_pct, max_pct = self.new_coin_momentum_range
        if chg24 <= min_pct:
            momentum_score = 0.0
        elif chg24 >= max_pct:
            momentum_score = 10.0
        else:
            momentum_score = 10.0 * (chg24 - min_pct) / (max_pct - min_pct + 1e-8)
        # 波动分：ATR相对价差
        atr14 = (df['h'] - df['l']).rolling(14).mean().iloc[-1]
        vol_score = max(0, 10 - (atr14 / close) * 250)
        # 深度分：以 new_coin_depth_min 归一到0-10
        depth = self.get_depth01pct(df.name if hasattr(df, 'name') else 'sym')
        depth_score = max(0, min(10, (depth / self.new_coin_depth_min) * 10))
        # 年龄分：使用真实上线时间，避免K线时间范围导致的错误计算
        # 优先使用symbols_info中的真实年龄，fallback到K线计算
        symbol = df.name if hasattr(df, 'name') else 'unknown'
        real_age_days = self.symbols_info.get(symbol, {}).get('age_days', None)
        if real_age_days is not None:
            age_minutes = real_age_days * 1440  # 天转分钟
        else:
            age_minutes = self.get_symbol_age_minutes_from_df(df)
        age_score = self.age_score_from_minutes(age_minutes)
        # 权重汇总（新币爆发专用）
        total = (
            channel_score * 0.35 +
            momentum_score * 0.4 +
            vol_score * 0.1 +
            depth_score * 0.05 +
            age_score * 0.1
        )
        total = max(0, min(10, total))
        M = 2 if total >= 7 else 1 if total >= 5 else 0
        from collections import namedtuple
        ScoreDetails = namedtuple('ScoreDetails', ['total', 'channel', 'momentum', 'vol', 'depth', 'age', 'M'])
        return ScoreDetails(total, channel_score, momentum_score, vol_score, depth_score, age_score, M)

    def score_symbol(self, df, age_days):
        """评分系统：根据策略模式切换"""
        if getattr(self, 'strategy_mode', 'breakout') == 'new_coin_burst':
            details = self.compute_scores_new(df, age_days)
            self.log.info(f"新币爆发评分 {df.name if hasattr(df,'name') else 'sym'}: 总分={details.total:.2f} (通道={details.channel:.2f}, 动量={details.momentum:.2f}, 波动={details.vol:.2f}, 深度={details.depth:.2f}, 年龄={details.age:.2f})")
        else:
            details = self.compute_scores(df, age_days)
            self.log.info(f"增强评分 {df.name if hasattr(df,'name') else 'sym'}: 总分={details.total:.2f} (通道={details.channel:.2f}, 动量={details.momentum:.2f}, 波动={details.vol:.2f}, 深度={details.depth:.2f}, 年龄={details.age:.2f})")
        from collections import namedtuple
        ScoreCard = namedtuple('ScoreCard', ['total_score', 'M'])
        return ScoreCard(details.total, details.M)

    # ---------- 3m突破+0.38%回踩限价单 ----------
    def check_entry(self, symbol: str):
        """检查入场条件（3m上轨突破后回踩确认 + 量能 + RSI + 针尖过滤）"""
        df = self.cache_get(f"kl3_{symbol}_100", self._raw_klines, symbol, '3m', 100)
        if df is None or len(df) < 30:
            self.log.info(f"{symbol} 入场条件未满足: 3m K线不足 len={0 if df is None else len(df)}")
            return None
        try:
            close = df['c'].iloc[-1]
            prev_high = df['h'].iloc[-2]
            # 通道类型切换：MAC 或 Donchian
            if self.entry_channel_type == 'mac':
                ema_now = df['c'].ewm(span=self.entry_mac_params[0]).mean()
                atr_now = self._calculate_atr(df['h'], df['l'], df['c'], length=self.entry_mac_params[1])
                upper = ema_now.iloc[-1] + atr_now.iloc[-1] * self.entry_mac_params[2]
                prev_upper = ema_now.iloc[-2] + atr_now.iloc[-2] * self.entry_mac_params[2]
            else:
                period = self.entry_donchian_period
                upper = df['h'].rolling(period).max().iloc[-1]
                prev_upper = df['h'].rolling(period).max().iloc[-2]
            # 1) 突破确认：前一根有突破迹象
            if prev_high < prev_upper:
                self.log.info(f"{symbol} 入场条件未满足: 突破未确认 prev_high={prev_high:.6f} prev_upper={prev_upper:.6f}")
                return None
            # 2) 回踩确认：当前接近上轨下方0.38%
            target_price = upper * self.PULLBACK_FACTOR
            if close < target_price:
                self.log.info(f"{symbol} 入场条件未满足: 回踩不足 close={close:.6f} target={target_price:.6f} upper={upper:.6f} factor={self.PULLBACK_FACTOR}")
                return None
            # 3) 量能验真
            vol = df['v'].iloc[-1]
            vol_ma = df['v'].rolling(20).mean().iloc[-1]
            rsi = self._compute_rsi(df['c'], 14).iloc[-1]
            if vol_ma > 0 and vol < vol_ma * self.VOL_MULT:
                self.log.info(f"{symbol} 入场条件未满足: 量能不足 vol={vol:.2f} vol_ma={vol_ma:.2f} mult={self.VOL_MULT}")
                return None
            # 4) RSI超买保护
            if rsi >= self.RSI_MAX or rsi <= self.RSI_MIN:
                self.log.info(f"{symbol} 入场条件未满足: RSI保护 rsi={rsi:.2f} 范围({self.RSI_MIN}-{self.RSI_MAX})")
                return None
            # 5) 针尖过滤：避免长上影线
            h, l, o, c = df['h'].iloc[-1], df['l'].iloc[-1], df['o'].iloc[-1], df['c'].iloc[-1]
            upper_ratio, lower_ratio = self._candle_wick_ratio(h, l, o, c)
            if upper_ratio > 0.6:  # 上影线过长
                self.log.info(f"{symbol} 入场条件未满足: 上影线过长 upper_ratio={upper_ratio:.2f}")
                return None
            return target_price
        except Exception as e:
            self.log.error(f"{symbol} 入场检查失败: {e}")
            return None

    # ---------- 统一风险管理：保本+移动+降档+Top3+止血贴 ----------
    def log_stop(self, symbol: str, typ: str):
        """记录止损事件"""
        rec = self.stop_cache.setdefault(symbol, {'count': 0, 'ts': time.time()})
        if time.time() - rec['ts'] < self.FREEZE_HR * 3600:
            rec['count'] += 1
        else:
            rec.update({'count': 1, 'ts': time.time()})
        self.log.info(f"{symbol} {typ} 累计{rec['count']}次")

    def can_open(self, symbol: str):
        """检查是否可以开仓"""
        rec = self.stop_cache.get(symbol)
        return not (rec and rec['count'] >= self.MAX_STOP and time.time() - rec['ts'] < self.FREEZE_HR * 3600)

    def monitor_position(self, symbol: str, current_p: float):
        """持仓监控"""
        if not self.pos: return
        entry, qty = self.pos['entry'], self.pos['qty']
        profit_pct = (current_p - entry) / entry

        # 1) 保本止损
        if profit_pct >= 0.05 and self.pos.get('breakeven', 0) == 0:
            self.pos['stop'] = entry * 1.004
            self.pos['breakeven'] = 1
            self.log.info(f"{symbol} 保本止损上移→{self.pos['stop']:.6f}")

        # 2) 移动止损
        if profit_pct >= 0.02:
            new_stop = current_p * 0.99
            if new_stop > self.pos.get('trail_stop', 0):
                self.pos['trail_stop'] = new_stop
        stop_price = max(self.pos.get('stop', 0), self.pos.get('trail_stop', 0))
        if current_p <= stop_price:
            self.log_stop(symbol, 'breakeven/trail')
            self.close_position(symbol, current_p)
            return

        # 3) 降档复位
        df = self.cache_get(f"kl15_{symbol}_100", self._raw_klines, symbol, '15m', 100)
        if len(df) >= 60:
            atr = (df['h'] - df['l']).rolling(14).mean().iloc[-1]
            depth = self.get_depth01pct(symbol)
            if (current_p < df['l'].rolling(20).min().iloc[-1] - atr or depth < 80000 or
                atr > df['c'].rolling(60).std().iloc[-1] * 2):
                exit_size = qty * 0.5
                self.place_maker_order(symbol, 'SELL', exit_size, current_p * 0.999)
                self.pos['qty'] -= exit_size
                self.pos['exit_pct'] = 0.5
                self.log.info(f"{symbol} 降档50%")
            if (self.pos.get('exit_pct', 0) > 0 and current_p > df['h'].rolling(20).max().iloc[-1] and
                depth > 200000 and atr < df['c'].rolling(60).std().iloc[-1] * 1.5):
                reset_size = self.pos['original_nominal'] * self.pos['exit_pct'] / current_p
                self.place_maker_order(symbol, 'BUY', reset_size, current_p * 1.001)
                self.pos['qty'] += reset_size
                self.pos['exit_pct'] = 0
                self.log.info(f"{symbol} 复位买回")

    def close_position(self, symbol: str, price: float):
        """平仓操作"""
        qty = self.pos['qty']
        self.place_maker_order(symbol, 'SELL', qty, price * 0.999)
        self.pos = None
        self.log.info(f"{symbol} 仓位已平")

    def daily_close(self):
        """每日强制平仓"""
        if self.pos and 'symbol' in self.pos:
            symbol = str(self.pos['symbol'])
            ticker_data = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
            if ticker_data and 'lastPrice' in ticker_data:
                current_price = float(ticker_data['lastPrice'])
                self.close_position(symbol, current_price)
                self.log.info("14:55 强制平仓完成")

    # ---------- 单次迭代（用于测试/仿真） ----------
    def run_once(self):
        """单次迭代执行（用于测试与仿真），不阻塞、不休眠"""
        try:
            now = pd.Timestamp.utcnow()
            if now.hour == 14 and now.minute == 55:
                self.daily_close()
                return {
                    'event': 'daily_close',
                    'selected': self.selected_symbol_today,
                    'entry_used': self.entry_permit_used,
                    'pos': self.pos
                }

            # 加载币种信息（带错误处理）
            try:
                self.load_symbols_with_cache()
            except Exception as e:
                self.log.error(f"加载币种信息失败: {e}")
                return {'event': 'load_symbols_failed'}

            # 扫描龙头池（合并次新突破）
            if self.strategy_mode == 'new_coin_burst':
                symbols = self.get_newcoin_breakthrough(self.new_coin_max_age_days)
            else:
                symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
            if not symbols:
                self.log.warning("未获取到龙头币种")
                return {'event': 'no_symbols'}

            # 今日唯一入场券：选出Top1且满足评分门槛
            today = now.date().isoformat()
            if self.selected_day != today:
                best_sym, best_score = None, -1
                for sym in symbols:
                    try:
                        df15 = self.get_klines(sym, '15m', 100)
                        if df15 is None or len(df15) < 30:
                            continue
                        # 确保评分函数能获取到正确的symbol用于深度评分
                        df15.name = sym
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        card = self.score_symbol(df15, age_days)
                        if card.total_score > best_score:
                            best_score = card.total_score
                            best_sym = sym
                    except Exception:
                        continue
                if best_sym and best_score >= self.SCORE_GATE:
                    self.selected_symbol_today = best_sym
                    self.log.info(f"今日Top1选择: {best_sym} 评分={best_score:.2f}")
                else:
                    self.selected_symbol_today = None
                    self.log.info(f"今日暂无满足评分门槛的币种 (最佳: {best_sym} 评分={best_score:.2f})")
                self.selected_day = today
                self.entry_permit_used = False
                # 若持仓与今日选择不一致，则清场
                if self.pos and self.selected_symbol_today and self.pos.get('symbol') != self.selected_symbol_today:
                    try:
                        tdata = self.http.get('/fapi/v1/ticker/24hr', {'symbol': self.pos['symbol']})
                        if tdata and 'lastPrice' in tdata:
                            self.close_position(self.pos['symbol'], float(tdata['lastPrice']))
                            self.log.info("切换Top1，强制清场旧持仓")
                    except Exception as e:
                        self.log.error(f"切换清场失败: {e}")

            # 尝试一次开仓
            for symbol in symbols:
                # 仅处理今日Top1且未使用入场券
                if self.selected_symbol_today and symbol != self.selected_symbol_today:
                    continue
                if self.entry_permit_used:
                    break
                if not self.can_open(symbol):
                    continue

                entry_price = self.check_entry(symbol)
                if entry_price:
                    info = self.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                    if not info:
                        continue
                    try:
                        # 获取过滤器信息
                        filters = info['symbols'][0]['filters']

                        # 查找特定过滤器类型
                        tick_size = None
                        step_size = None
                        min_qty = None
                        min_notional = None

                        for filter_info in filters:
                            filter_type = filter_info.get('filterType')
                            if filter_type == 'PRICE_FILTER':
                                tick_size = float(filter_info['tickSize'])
                            elif filter_type == 'LOT_SIZE':
                                step_size = float(filter_info['stepSize'])
                                min_qty = float(filter_info['minQty'])
                            elif filter_type == 'MIN_NOTIONAL':
                                # 兼容不同字段名
                                min_notional = float(filter_info.get('notional') or filter_info.get('minNotional') or 0)

                        # 检查是否获取到所有必要参数
                        if not all([tick_size, step_size, min_qty, min_notional]):
                            self.log.warning(f"未能获取完整的交易规则: {symbol}")
                            continue

                        qty = self.cfg['first_nominal'] / entry_price
                        qty = self._round_qty(qty, step_size, min_qty, entry_price, min_notional)
                        entry_price = self._round_price(entry_price, tick_size)

                        self.pos = {
                            'symbol': symbol,
                            'entry': entry_price,
                            'qty': qty,
                            'original_nominal': self.cfg['first_nominal'],
                            'open_time': time.time()
                        }
                        self.place_maker_order(symbol, 'BUY', qty, entry_price)
                        self.entry_permit_used = True
                        self.log.info(f"上车 {symbol} {qty} @ {entry_price:.6f}")
                        return {
                            'event': 'opened',
                            'selected': self.selected_symbol_today,
                            'entry_used': self.entry_permit_used,
                            'pos': self.pos
                        }
                    except Exception as e:
                        self.log.error(f"处理交易信息失败 {symbol}: {e}")
                        continue

            return {
                'event': 'idle',
                'selected': self.selected_symbol_today,
                'entry_used': self.entry_permit_used,
                'pos': self.pos
            }
        except Exception as e:
            self.log.error(f"run_once异常: {e}")
            return {'event': 'error', 'error': str(e)}

    # ---------- 主循环 ----------
    def loop(self):
        """增强版主循环（带错误处理）"""
        self.log.info("增强极简版启动")
        
        while True:
            try:
                now = pd.Timestamp.utcnow()
                if now.hour == 14 and now.minute == 55:
                    self.daily_close()
                    time.sleep(60)
                    continue
                
                # 加载币种信息（带错误处理）
                try:
                    self.load_symbols_with_cache()
                except Exception as e:
                    self.log.error(f"加载币种信息失败: {e}")
                    time.sleep(10)
                    continue
                
                # 扫描龙头池（合并次新突破）
                if self.strategy_mode == 'new_coin_burst':
                    # 与refresh_top1保持一致：合并龙头池新币和突破池
                    leaders = self.get_top50_market_leaders()
                    leader_newcoins = []
                    for sym in leaders:
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        if age_days <= self.new_coin_max_age_days:
                            leader_newcoins.append(sym)
                    breakthrough_coins = self.get_newcoin_breakthrough(self.new_coin_max_age_days)
                    symbols = list(set(leader_newcoins + breakthrough_coins))
                else:
                    symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
                if not symbols:
                    self.log.warning("未获取到龙头币种，等待重试...")
                    time.sleep(10)
                    continue

                # 今日唯一入场券：选出Top1且满足评分门槛
                today = now.date().isoformat()
                if self.selected_day != today:
                    best_sym, best_score = None, -1
                    for sym in symbols:
                        try:
                            df15 = self.get_klines(sym, '15m', 100)
                            if df15 is None or len(df15) < 30:
                                continue
                            # 确保评分函数能获取到正确的symbol用于深度评分
                            df15.name = sym
                            age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                            card = self.score_symbol(df15, age_days)
                            if card.total_score > best_score:
                                best_score = card.total_score
                                best_sym = sym
                        except Exception:
                            continue
                    if best_sym and best_score >= self.SCORE_GATE:
                        self.selected_symbol_today = best_sym
                        self.log.info(f"今日Top1选择: {best_sym} 评分={best_score:.2f}")
                    else:
                        self.selected_symbol_today = None
                        self.log.info(f"今日暂无满足评分门槛的币种 (最佳: {best_sym} 评分={best_score:.2f})")
                    self.selected_day = today
                    self.entry_permit_used = False
                    # 若持仓与今日选择不一致，则清场
                    if self.pos and self.selected_symbol_today and self.pos.get('symbol') != self.selected_symbol_today:
                        try:
                            tdata = self.http.get('/fapi/v1/ticker/24hr', {'symbol': self.pos['symbol']})
                            if tdata and 'lastPrice' in tdata:
                                self.close_position(self.pos['symbol'], float(tdata['lastPrice']))
                                self.log.info("切换Top1，强制清场旧持仓")
                        except Exception as e:
                            self.log.error(f"切换清场失败: {e}")

                for symbol in symbols:
                    # 仅处理今日Top1且未使用入场券
                    if self.selected_symbol_today and symbol != self.selected_symbol_today:
                        continue
                    if self.entry_permit_used:
                        break
                    if not self.can_open(symbol): 
                        continue
                    
                    entry_price = self.check_entry(symbol)
                    if entry_price:
                        info = self.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                        if not info: 
                            continue
                        
                        try:
                            # 获取过滤器信息
                            filters = info['symbols'][0]['filters']
                            
                            # 查找特定过滤器类型
                            tick_size = None
                            step_size = None
                            min_qty = None
                            min_notional = None
                            
                            for filter_info in filters:
                                filter_type = filter_info.get('filterType')
                                if filter_type == 'PRICE_FILTER':
                                    tick_size = float(filter_info['tickSize'])
                                elif filter_type == 'LOT_SIZE':
                                    step_size = float(filter_info['stepSize'])
                                    min_qty = float(filter_info['minQty'])
                                elif filter_type == 'MIN_NOTIONAL':
                                    min_notional = float(filter_info['notional'])
                            
                            # 检查是否获取到所有必要参数
                            if not all([tick_size, step_size, min_qty, min_notional]):
                                self.log.warning(f"未能获取完整的交易规则: {symbol}")
                                continue
                            
                            qty = self.cfg['first_nominal'] / entry_price
                            qty = self._round_qty(qty, step_size, min_qty, entry_price, min_notional)
                            entry_price = self._round_price(entry_price, tick_size)
                            
                            self.pos = {
                                'symbol': symbol, 
                                'entry': entry_price, 
                                'qty': qty, 
                                'original_nominal': self.cfg['first_nominal'], 
                                'open_time': time.time()
                            }
                            self.place_maker_order(symbol, 'BUY', qty, entry_price)
                            self.entry_permit_used = True
                            self.log.info(f"上车 {symbol} {qty} @ {entry_price:.6f}")
                        except Exception as e:
                            self.log.error(f"处理交易信息失败 {symbol}: {e}")
                            continue

                # 持仓监控
                if self.pos and 'symbol' in self.pos:
                    symbol = str(self.pos['symbol'])
                    ticker_data = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                    if ticker_data and 'lastPrice' in ticker_data:
                        try:
                            current_price = float(ticker_data['lastPrice'])
                            self.monitor_position(str(symbol), float(current_price))
                        except Exception as e:
                            self.log.error(f"持仓监控失败 {symbol}: {e}")

                # 清理过期限价单
                for sym, rec in list(self.active_limit.items()):
                    if time.time() > rec['ttl']:
                        try:
                            self.http.delete('/fapi/v1/order', {'symbol': sym, 'orderId': rec['id']})
                            del self.active_limit[sym]
                            self.log.info(f"清理过期限价单 {sym}")
                        except Exception as e:
                            self.log.error(f"清理限价单失败 {sym}: {e}")

                time.sleep(5)  # 增加循环间隔，减少API调用频率
                
            except KeyboardInterrupt:
                self.log.info("用户中断策略执行")
                break
            except Exception as e:
                self.log.error(f"主循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再重试


    # ---------- 导出评分分布快照（AB后：MAC通道） ----------
    def export_score_snapshot(self, filename: str):
        try:
            import csv, os
            os.makedirs('logs', exist_ok=True)
            symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
            if not symbols:
                # Fallback：忽略评分门槛，按全市场评分排序取前50
                try:
                    self.load_symbols_with_cache()
                    candidates = list(self.symbols_info.keys())
                    ranked = []
                    for sym in candidates:
                        try:
                            df15 = self.get_klines(sym, '15m', 100)
                            if df15 is None or len(df15) < 30:
                                continue
                            df15.name = sym
                            age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                            d = self.compute_scores(df15, age_days)
                            ranked.append((sym, d))
                        except Exception:
                            continue
                    ranked.sort(key=lambda x: getattr(x[1], 'total', 0), reverse=True)
                    symbols = [s for s, _ in ranked[:50]]
                except Exception:
                    symbols = []
            if not symbols:
                self.log.warning("快照导出：未获取到龙头币种（fallback后仍为空）")
                return False
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['symbol', 'total', 'channel', 'momentum', 'vol', 'depth', 'age'])
                for sym in symbols:
                    try:
                        df15 = self.get_klines(sym, '15m', 100)
                        if df15 is None or len(df15) < 30:
                            continue
                        df15.name = sym
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        d = self.compute_scores(df15, age_days)
                        writer.writerow([sym, f"{d.total:.2f}", f"{d.channel:.2f}", f"{d.momentum:.2f}", f"{d.vol:.2f}", f"{d.depth:.2f}", f"{d.age:.2f}"])
                    except Exception:
                        continue
            self.log.info(f"评分分布快照已导出到 {filename}")
            return True
        except Exception as e:
            self.log.error(f"快照导出失败: {e}")
            return False

    def export_score_snapshot_baseline(self, filename: str):
        """导出评分分布基线快照（AB前：Donchian通道）"""
        try:
            import csv, os
            os.makedirs('logs', exist_ok=True)
            symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
            if not symbols:
                # Fallback：忽略评分门槛，按全市场评分排序取前50
                try:
                    self.load_symbols_with_cache()
                    candidates = list(self.symbols_info.keys())
                    ranked = []
                    for sym in candidates:
                        try:
                            df15 = self.get_klines(sym, '15m', 100)
                            if df15 is None or len(df15) < 30:
                                continue
                            df15.name = sym
                            age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                            d = self.compute_scores(df15, age_days)
                            ranked.append((sym, d))
                        except Exception:
                            continue
                    ranked.sort(key=lambda x: getattr(x[1], 'total', 0), reverse=True)
                    symbols = [s for s, _ in ranked[:50]]
                except Exception:
                    symbols = []
            if not symbols:
                self.log.warning("快照导出（基线）：未获取到龙头币种（fallback后仍为空）")
                return False
            period = getattr(self, 'entry_donchian_period', 20)
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['symbol', 'total', 'channel', 'momentum', 'vol', 'depth', 'age'])
                for sym in symbols:
                    try:
                        df15 = self.get_klines(sym, '15m', 100)
                        if df15 is None or len(df15) < max(30, period):
                            continue
                        df15.name = sym
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        # 先计算除通道外的各项分数
                        d = self.compute_scores(df15, age_days)
                        close = df15['c'].iloc[-1]
                        upper_d = df15['h'].rolling(period).max().iloc[-1]
                        lower_d = df15['l'].rolling(period).min().iloc[-1]
                        channel_raw = (close - lower_d) / (upper_d - lower_d + 1e-8) * 10
                        channel_score = max(0, min(10, channel_raw))
                        # 新币补偿与 compute_scores 保持一致
                        channel_score = max(0, min(10, channel_score + (2 if age_days < 30 else 0)))
                        # 使用 Donchian 通道分替换，重算总分
                        total_baseline = channel_score * 0.4 + d.momentum * 0.3 + d.vol * 0.15 + d.depth * 0.1 + d.age * 0.05
                        total_baseline = max(0, min(10, total_baseline))
                        writer.writerow([sym, f"{total_baseline:.2f}", f"{channel_score:.2f}", f"{d.momentum:.2f}", f"{d.vol:.2f}", f"{d.depth:.2f}", f"{d.age:.2f}"])
                    except Exception:
                        continue
            self.log.info(f"评分分布基线快照已导出到 {filename}")
            return True
        except Exception as e:
            self.log.error(f"快照导出（基线）失败: {e}")
            return False

    def refresh_top1(self):
        """按当前配置与评分体系，刷新Top1选择（每小时触发）"""
        # 策略模式 == new_coin_burst 时，合并龙头池和新币突破池
        if self.strategy_mode == 'new_coin_burst':
            # 获取龙头池中的新币（年龄≤120天）
            leaders = self.get_top50_market_leaders()
            leader_newcoins = []
            for sym in leaders:
                age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                if age_days <= self.new_coin_max_age_days:
                    leader_newcoins.append(sym)
            
            # 获取新币突破池
            breakthrough_coins = self.get_newcoin_breakthrough(self.new_coin_max_age_days)
            
            # 合并去重，优先保留龙头池中的新币
            symbols = list(set(leader_newcoins + breakthrough_coins))
            self.log.info(f"[new_coin_burst] 龙头池新币:{len(leader_newcoins)} 突破池:{len(breakthrough_coins)} 合并后:{len(symbols)}")
        else:
            symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
        best_sym, best_score = None, -1
        for sym in symbols:
            try:
                df15 = self.get_klines(sym, '15m', 100)
                if df15 is None or len(df15) < 30:
                    continue
                df15.name = sym
                age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                card = self.score_symbol(df15, age_days)
                if card.total_score > best_score:
                    best_score = card.total_score
                    best_sym = sym
            except Exception:
                continue
        if best_sym and best_score >= self.SCORE_GATE:
            self.selected_symbol_today = best_sym
            self.log.info(f"[刷新Top1] {best_sym} 评分={best_score:.2f}")
        else:
            self.selected_symbol_today = None
            self.log.info(f"[刷新Top1] 暂无满足评分门槛的币种 (最佳: {best_sym} 评分={best_score:.2f})")
        # 每次刷新允许重新使用入场许可（符合‘每小时刷新Top1’需求）
        self.entry_permit_used = False

    def log_stop(self, symbol: str, typ: str):
        """记录止损事件"""
        rec = self.stop_cache.setdefault(symbol, {'count': 0, 'ts': time.time()})
        if time.time() - rec['ts'] < self.FREEZE_HR * 3600:
            rec['count'] += 1
        else:
            rec.update({'count': 1, 'ts': time.time()})
        self.log.info(f"{symbol} {typ} 累计{rec['count']}次")

    def can_open(self, symbol: str):
        """检查是否可以开仓"""
        rec = self.stop_cache.get(symbol)
        return not (rec and rec['count'] >= self.MAX_STOP and time.time() - rec['ts'] < self.FREEZE_HR * 3600)

    def monitor_position(self, symbol: str, current_p: float):
        """持仓监控"""
        if not self.pos: return
        entry, qty = self.pos['entry'], self.pos['qty']
        profit_pct = (current_p - entry) / entry

        # 1) 保本止损
        if profit_pct >= 0.05 and self.pos.get('breakeven', 0) == 0:
            self.pos['stop'] = entry * 1.004
            self.pos['breakeven'] = 1
            self.log.info(f"{symbol} 保本止损上移→{self.pos['stop']:.6f}")

        # 2) 移动止损
        if profit_pct >= 0.02:
            new_stop = current_p * 0.99
            if new_stop > self.pos.get('trail_stop', 0):
                self.pos['trail_stop'] = new_stop
        stop_price = max(self.pos.get('stop', 0), self.pos.get('trail_stop', 0))
        if current_p <= stop_price:
            self.log_stop(symbol, 'breakeven/trail')
            self.close_position(symbol, current_p)
            return

        # 3) 降档复位
        df = self.cache_get(f"kl15_{symbol}_100", self._raw_klines, symbol, '15m', 100)
        if len(df) >= 60:
            atr = (df['h'] - df['l']).rolling(14).mean().iloc[-1]
            depth = self.get_depth01pct(symbol)
            if (current_p < df['l'].rolling(20).min().iloc[-1] - atr or depth < 80000 or
                atr > df['c'].rolling(60).std().iloc[-1] * 2):
                exit_size = qty * 0.5
                self.place_maker_order(symbol, 'SELL', exit_size, current_p * 0.999)
                self.pos['qty'] -= exit_size
                self.pos['exit_pct'] = 0.5
                self.log.info(f"{symbol} 降档50%")
            if (self.pos.get('exit_pct', 0) > 0 and current_p > df['h'].rolling(20).max().iloc[-1] and
                depth > 200000 and atr < df['c'].rolling(60).std().iloc[-1] * 1.5):
                reset_size = self.pos['original_nominal'] * self.pos['exit_pct'] / current_p
                self.place_maker_order(symbol, 'BUY', reset_size, current_p * 1.001)
                self.pos['qty'] += reset_size
                self.pos['exit_pct'] = 0
                self.log.info(f"{symbol} 复位买回")

    def close_position(self, symbol: str, price: float):
        """平仓操作"""
        qty = self.pos['qty']
        self.place_maker_order(symbol, 'SELL', qty, price * 0.999)
        self.pos = None
        self.log.info(f"{symbol} 仓位已平")

    def daily_close(self):
        """每日强制平仓"""
        if self.pos and 'symbol' in self.pos:
            symbol = str(self.pos['symbol'])
            ticker_data = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
            if ticker_data and 'lastPrice' in ticker_data:
                current_price = float(ticker_data['lastPrice'])
                self.close_position(symbol, current_price)
                self.log.info("14:55 强制平仓完成")

    # ---------- 单次迭代（用于测试/仿真） ----------
    def run_once(self):
        """单次迭代执行（用于测试与仿真），不阻塞、不休眠"""
        try:
            now = pd.Timestamp.utcnow()
            if now.hour == 14 and now.minute == 55:
                self.daily_close()
                return {
                    'event': 'daily_close',
                    'selected': self.selected_symbol_today,
                    'entry_used': self.entry_permit_used,
                    'pos': self.pos
                }

            # 加载币种信息（带错误处理）
            try:
                self.load_symbols_with_cache()
            except Exception as e:
                self.log.error(f"加载币种信息失败: {e}")
                return {'event': 'load_symbols_failed'}

            # 扫描龙头池（合并次新突破）
            if self.strategy_mode == 'new_coin_burst':
                symbols = self.get_newcoin_breakthrough(self.new_coin_max_age_days)
            else:
                symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
            if not symbols:
                self.log.warning("未获取到龙头币种")
                return {'event': 'no_symbols'}

            # 今日唯一入场券：选出Top1且满足评分门槛
            today = now.date().isoformat()
            if self.selected_day != today:
                best_sym, best_score = None, -1
                for sym in symbols:
                    try:
                        df15 = self.get_klines(sym, '15m', 100)
                        if df15 is None or len(df15) < 30:
                            continue
                        # 确保评分函数能获取到正确的symbol用于深度评分
                        df15.name = sym
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        card = self.score_symbol(df15, age_days)
                        if card.total_score > best_score:
                            best_score = card.total_score
                            best_sym = sym
                    except Exception:
                        continue
                if best_sym and best_score >= self.SCORE_GATE:
                    self.selected_symbol_today = best_sym
                    self.log.info(f"今日Top1选择: {best_sym} 评分={best_score:.2f}")
                else:
                    self.selected_symbol_today = None
                    self.log.info(f"今日暂无满足评分门槛的币种 (最佳: {best_sym} 评分={best_score:.2f})")
                self.selected_day = today
                self.entry_permit_used = False
                # 若持仓与今日选择不一致，则清场
                if self.pos and self.selected_symbol_today and self.pos.get('symbol') != self.selected_symbol_today:
                    try:
                        tdata = self.http.get('/fapi/v1/ticker/24hr', {'symbol': self.pos['symbol']})
                        if tdata and 'lastPrice' in tdata:
                            self.close_position(self.pos['symbol'], float(tdata['lastPrice']))
                            self.log.info("切换Top1，强制清场旧持仓")
                    except Exception as e:
                        self.log.error(f"切换清场失败: {e}")

            # 尝试一次开仓
            for symbol in symbols:
                # 仅处理今日Top1且未使用入场券
                if self.selected_symbol_today and symbol != self.selected_symbol_today:
                    continue
                if self.entry_permit_used:
                    break
                if not self.can_open(symbol):
                    continue

                entry_price = self.check_entry(symbol)
                if entry_price:
                    info = self.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                    if not info:
                        continue
                    try:
                        # 获取过滤器信息
                        filters = info['symbols'][0]['filters']

                        # 查找特定过滤器类型
                        tick_size = None
                        step_size = None
                        min_qty = None
                        min_notional = None

                        for filter_info in filters:
                            filter_type = filter_info.get('filterType')
                            if filter_type == 'PRICE_FILTER':
                                tick_size = float(filter_info['tickSize'])
                            elif filter_type == 'LOT_SIZE':
                                step_size = float(filter_info['stepSize'])
                                min_qty = float(filter_info['minQty'])
                            elif filter_type == 'MIN_NOTIONAL':
                                # 兼容不同字段名
                                min_notional = float(filter_info.get('notional') or filter_info.get('minNotional') or 0)

                        # 检查是否获取到所有必要参数
                        if not all([tick_size, step_size, min_qty, min_notional]):
                            self.log.warning(f"未能获取完整的交易规则: {symbol}")
                            continue

                        qty = self.cfg['first_nominal'] / entry_price
                        qty = self._round_qty(qty, step_size, min_qty, entry_price, min_notional)
                        entry_price = self._round_price(entry_price, tick_size)

                        self.pos = {
                            'symbol': symbol,
                            'entry': entry_price,
                            'qty': qty,
                            'original_nominal': self.cfg['first_nominal'],
                            'open_time': time.time()
                        }
                        self.place_maker_order(symbol, 'BUY', qty, entry_price)
                        self.entry_permit_used = True
                        self.log.info(f"上车 {symbol} {qty} @ {entry_price:.6f}")
                        return {
                            'event': 'opened',
                            'selected': self.selected_symbol_today,
                            'entry_used': self.entry_permit_used,
                            'pos': self.pos
                        }
                    except Exception as e:
                        self.log.error(f"处理交易信息失败 {symbol}: {e}")
                        continue

            return {
                'event': 'idle',
                'selected': self.selected_symbol_today,
                'entry_used': self.entry_permit_used,
                'pos': self.pos
            }
        except Exception as e:
            self.log.error(f"run_once异常: {e}")
            return {'event': 'error', 'error': str(e)}

    # ---------- 主循环 ----------
    def loop(self):
        """增强版主循环（带错误处理）"""
        self.log.info("增强极简版启动")
        
        while True:
            try:
                now = pd.Timestamp.utcnow()
                # 每日一次：北京时间>=09:30 导出AB后快照
                bj_now = now + pd.Timedelta(hours=8)
                day_key = bj_now.date().isoformat()
                if (self._score_snapshot_day != day_key) and (bj_now.hour >= 9 and bj_now.minute >= 30):
                    if not self.snapshot_export_enabled:
                        self.log.info("快照导出已跳过（配置 snapshot_export_enabled=False）")
                    else:
                        # 先导出基线（AB前：Donchian）
                        self.export_score_snapshot_baseline('logs/score_snapshot_before_ab.csv')
                        # 再导出 AB 后（MAC 通道）
                        if self.export_score_snapshot('logs/score_snapshot_after_ab.csv'):
                            self._score_snapshot_day = day_key
                            self._score_snapshot_done = True
                # 每小时刷新Top1
                if self._last_top1_refresh is None or (now - self._last_top1_refresh) >= self.top1_refresh_interval:
                    self._last_top1_refresh = now
                    self.refresh_top1()
                if now.hour == 14 and now.minute == 55:
                    self.daily_close()
                    time.sleep(60)
                    continue
                
                # 加载币种信息（带错误处理）
                try:
                    self.load_symbols_with_cache()
                except Exception as e:
                    self.log.error(f"加载币种信息失败: {e}")
                    time.sleep(10)
                    continue
                
                # 扫描龙头池（合并次新突破）
                if self.strategy_mode == 'new_coin_burst':
                    # 与refresh_top1保持一致：合并龙头池新币和突破池
                    leaders = self.get_top50_market_leaders()
                    leader_newcoins = []
                    for sym in leaders:
                        age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                        if age_days <= self.new_coin_max_age_days:
                            leader_newcoins.append(sym)
                    breakthrough_coins = self.get_newcoin_breakthrough(self.new_coin_max_age_days)
                    symbols = list(set(leader_newcoins + breakthrough_coins))
                else:
                    symbols = list(set(self.get_top50_market_leaders() + self.get_newcoin_breakthrough()))
                if not symbols:
                    self.log.warning("未获取到龙头币种，等待重试...")
                    time.sleep(10)
                    continue

                # 今日唯一入场券：选出Top1且满足评分门槛
                today = now.date().isoformat()
                if self.selected_day != today:
                    best_sym, best_score = None, -1
                    for sym in symbols:
                        try:
                            df15 = self.get_klines(sym, '15m', 100)
                            if df15 is None or len(df15) < 30:
                                continue
                            # 确保评分函数能获取到正确的symbol用于深度评分
                            df15.name = sym
                            age_days = self.symbols_info.get(sym, {}).get('age_days', 999)
                            card = self.score_symbol(df15, age_days)
                            if card.total_score > best_score:
                                best_score = card.total_score
                                best_sym = sym
                        except Exception:
                            continue
                    if best_sym and best_score >= self.SCORE_GATE:
                        self.selected_symbol_today = best_sym
                        self.log.info(f"今日Top1选择: {best_sym} 评分={best_score:.2f}")
                    else:
                        self.selected_symbol_today = None
                        self.log.info(f"今日暂无满足评分门槛的币种 (最佳: {best_sym} 评分={best_score:.2f})")
                    self.selected_day = today
                    self.entry_permit_used = False
                    # 若持仓与今日选择不一致，则清场
                    if self.pos and self.selected_symbol_today and self.pos.get('symbol') != self.selected_symbol_today:
                        try:
                            tdata = self.http.get('/fapi/v1/ticker/24hr', {'symbol': self.pos['symbol']})
                            if tdata and 'lastPrice' in tdata:
                                self.close_position(self.pos['symbol'], float(tdata['lastPrice']))
                                self.log.info("切换Top1，强制清场旧持仓")
                        except Exception as e:
                            self.log.error(f"切换清场失败: {e}")

                for symbol in symbols:
                    # 仅处理今日Top1且未使用入场券
                    if self.selected_symbol_today and symbol != self.selected_symbol_today:
                        continue
                    if self.entry_permit_used:
                        break
                    if not self.can_open(symbol): 
                        continue
                    
                    entry_price = self.check_entry(symbol)
                    if entry_price:
                        info = self.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
                        if not info: 
                            continue
                        
                        try:
                            # 获取过滤器信息
                            filters = info['symbols'][0]['filters']
                            
                            # 查找特定过滤器类型
                            tick_size = None
                            step_size = None
                            min_qty = None
                            min_notional = None
                            
                            for filter_info in filters:
                                filter_type = filter_info.get('filterType')
                                if filter_type == 'PRICE_FILTER':
                                    tick_size = float(filter_info['tickSize'])
                                elif filter_type == 'LOT_SIZE':
                                    step_size = float(filter_info['stepSize'])
                                    min_qty = float(filter_info['minQty'])
                                elif filter_type == 'MIN_NOTIONAL':
                                    min_notional = float(filter_info['notional'])
                            
                            # 检查是否获取到所有必要参数
                            if not all([tick_size, step_size, min_qty, min_notional]):
                                self.log.warning(f"未能获取完整的交易规则: {symbol}")
                                continue
                            
                            qty = self.cfg['first_nominal'] / entry_price
                            qty = self._round_qty(qty, step_size, min_qty, entry_price, min_notional)
                            entry_price = self._round_price(entry_price, tick_size)
                            
                            self.pos = {
                                'symbol': symbol, 
                                'entry': entry_price, 
                                'qty': qty, 
                                'original_nominal': self.cfg['first_nominal'], 
                                'open_time': time.time()
                            }
                            self.place_maker_order(symbol, 'BUY', qty, entry_price)
                            self.entry_permit_used = True
                            self.log.info(f"上车 {symbol} {qty} @ {entry_price:.6f}")
                        except Exception as e:
                            self.log.error(f"处理交易信息失败 {symbol}: {e}")
                            continue

                # 持仓监控
                if self.pos and 'symbol' in self.pos:
                    symbol = str(self.pos['symbol'])
                    ticker_data = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                    if ticker_data and 'lastPrice' in ticker_data:
                        try:
                            current_price = float(ticker_data['lastPrice'])
                            self.monitor_position(str(symbol), float(current_price))
                        except Exception as e:
                            self.log.error(f"持仓监控失败 {symbol}: {e}")

                # 清理过期限价单
                for sym, rec in list(self.active_limit.items()):
                    if time.time() > rec['ttl']:
                        try:
                            self.http.delete('/fapi/v1/order', {'symbol': sym, 'orderId': rec['id']})
                            del self.active_limit[sym]
                            self.log.info(f"清理过期限价单 {sym}")
                        except Exception as e:
                            self.log.error(f"清理限价单失败 {sym}: {e}")

                time.sleep(5)  # 增加循环间隔，减少API调用频率
                
            except KeyboardInterrupt:
                self.log.info("用户中断策略执行")
                break
            except Exception as e:
                self.log.error(f"主循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再重试


    def log_stop(self, symbol: str, typ: str):
        """记录止损事件"""
        rec = self.stop_cache.setdefault(symbol, {'count': 0, 'ts': time.time()})
        if time.time() - rec['ts'] < self.FREEZE_HR * 3600:
            rec['count'] += 1
        else:
            rec.update({'count': 1, 'ts': time.time()})
        self.log.info(f"{symbol} {typ} 累计{rec['count']}次")

    def can_open(self, symbol: str):
        """检查是否可以开仓"""
        rec = self.stop_cache.get(symbol)
        return not (rec and rec['count'] >= self.MAX_STOP and time.time() - rec['ts'] < self.FREEZE_HR * 3600)

    def monitor_position(self, symbol: str, current_p: float):
        """持仓监控"""
        if not self.pos: return
        entry, qty = self.pos['entry'], self.pos['qty']
        profit_pct = (current_p - entry) / entry

        # 1) 保本止损
        if profit_pct >= 0.05 and self.pos.get('breakeven', 0) == 0:
            self.pos['stop'] = entry * 1.004
            self.pos['breakeven'] = 1
            self.log.info(f"{symbol} 保本止损上移→{self.pos['stop']:.6f}")

        # 2) 移动止损
        if profit_pct >= 0.02:
            new_stop = current_p * 0.99
            if new_stop > self.pos.get('trail_stop', 0):
                self.pos['trail_stop'] = new_stop
        stop_price = max(self.pos.get('stop', 0), self.pos.get('trail_stop', 0))
        if current_p <= stop_price:
            self.log_stop(symbol, 'breakeven/trail')
            self.close_position(symbol, current_p)
            return

        # 3) 降档复位
        df = self.cache_get(f"kl15_{symbol}_100", self._raw_klines, symbol, '15m', 100)
        if len(df) >= 60:
            atr = (df['h'] - df['l']).rolling(14).mean().iloc[-1]
            depth = self.get_depth01pct(symbol)
            if (current_p < df['l'].rolling(20).min().iloc[-1] - atr or depth < 80000 or
                atr > df['c'].rolling(60).std().iloc[-1] * 2):
                exit_size = qty * 0.5
                self.place_maker_order(symbol, 'SELL', exit_size, current_p * 0.999)
                self.pos['qty'] -= exit_size
                self.pos['exit_pct'] = 0.5
                self.log.info(f"{symbol} 降档50%")
            if (self.pos.get('exit_pct', 0) > 0 and current_p > df['h'].rolling(20).max().iloc[-1] and
                depth > 200000 and atr < df['c'].rolling(60).std().iloc[-1] * 1.5):
                reset_size = self.pos['original_nominal'] * self.pos['exit_pct'] / current_p
                self.place_maker_order(symbol, 'BUY', reset_size, current_p * 1.001)
                self.pos['qty'] += reset_size
                self.pos['exit_pct'] = 0
                self.log.info(f"{symbol} 复位买回")

    def close_position(self, symbol: str, price: float):
        """平仓操作"""
        qty = self.pos['qty']
        self.place_maker_order(symbol, 'SELL', qty, price * 0.999)
        self.pos = None
        self.log.info(f"{symbol} 仓位已平")

    def daily_close(self):
        """每日强制平仓"""
        if self.pos and 'symbol' in self.pos:
            symbol = str(self.pos['symbol'])
            ticker_data = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
            if ticker_data and 'lastPrice' in ticker_data:
                current_price = float(ticker_data['lastPrice'])
                self.close_position(symbol, current_price)
                self.log.info("14:55 强制平仓完成")

    # ---------- 单次迭代（用于测试/仿真） ------
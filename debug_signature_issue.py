#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORDERUSDT 签名验证失败问题调试脚本
分析签名生成过程中可能的问题
"""

import yaml
import time
import hmac
import hashlib
import urllib.parse
import json
from http_client import HttpClient

def debug_signature_generation():
    """调试签名生成过程"""
    print("=== 签名生成调试 ===")
    
    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 模拟下单参数
    params = {
        'symbol': 'ORDERUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'quantity': '236.417',
        'price': '0.4',
        'timeInForce': 'GTC',
        'reduceOnly': False,
        'timestamp': int(time.time() * 1000),
        'recvWindow': 60000
    }
    
    print(f"原始参数: {json.dumps(params, indent=2)}")
    
    # 生成查询字符串
    query_string = urllib.parse.urlencode(params)
    print(f"查询字符串: {query_string}")
    
    # 生成签名
    api_secret = cfg['api_secret']
    signature = hmac.new(
        api_secret.encode('utf-8'),
        query_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    print(f"生成的签名: {signature}")
    
    # 检查参数中是否有特殊字符
    print("\n=== 参数检查 ===")
    for key, value in params.items():
        print(f"{key}: {value} (类型: {type(value)})")
        if isinstance(value, str) and any(c in value for c in ['%', '+', '&', '=']):
            print(f"  ⚠️  包含特殊字符: {value}")
    
    # 检查编码问题
    print("\n=== 编码检查 ===")
    print(f"API Secret 长度: {len(api_secret)}")
    print(f"查询字符串长度: {len(query_string)}")
    print(f"查询字符串字节长度: {len(query_string.encode('utf-8'))}")
    
    return params, signature

def test_with_http_client():
    """使用HTTP客户端测试签名"""
    print("\n=== HTTP客户端签名测试 ===")
    
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    http = HttpClient(cfg['api_key'], cfg['api_secret'], cfg.get('base_url', 'https://fapi.binance.com'))
    
    # 测试参数
    test_params = {
        'symbol': 'ORDERUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'quantity': '236.417',
        'price': '0.4',
        'timeInForce': 'GTC',
        'reduceOnly': False
    }
    
    print(f"测试参数: {json.dumps(test_params, indent=2)}")
    
    # 尝试下单（测试接口）
    result = http.post('/fapi/v1/order/test', test_params)
    print(f"测试下单结果: {result}")
    
    return result

def analyze_precision_in_signature():
    """分析精度在签名中的影响"""
    print("\n=== 精度对签名的影响分析 ===")
    
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    api_secret = cfg['api_secret']
    base_params = {
        'symbol': 'ORDERUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'reduceOnly': False,
        'timestamp': int(time.time() * 1000),
        'recvWindow': 60000
    }
    
    # 测试不同精度的数量和价格
    test_cases = [
        {'quantity': '236.417', 'price': '0.4'},
        {'quantity': '236.4', 'price': '0.4'},
        {'quantity': '236', 'price': '0.4'},
        {'quantity': '236.417', 'price': '0.40'},
        {'quantity': '236.417', 'price': '0.400'},
        {'quantity': '236.417', 'price': '0.4000'},
    ]
    
    for i, case in enumerate(test_cases):
        params = {**base_params, **case}
        query_string = urllib.parse.urlencode(params)
        signature = hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        print(f"测试 {i+1}: quantity={case['quantity']}, price={case['price']}")
        print(f"  查询字符串: {query_string}")
        print(f"  签名: {signature[:16]}...")
        print()

def check_parameter_order():
    """检查参数顺序对签名的影响"""
    print("\n=== 参数顺序对签名的影响 ===")
    
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    api_secret = cfg['api_secret']
    timestamp = int(time.time() * 1000)
    
    # 不同顺序的参数
    params1 = {
        'symbol': 'ORDERUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'quantity': '236.417',
        'price': '0.4',
        'timeInForce': 'GTC',
        'reduceOnly': False,
        'timestamp': timestamp,
        'recvWindow': 60000
    }
    
    params2 = {
        'timestamp': timestamp,
        'symbol': 'ORDERUSDT',
        'quantity': '236.417',
        'price': '0.4',
        'side': 'BUY',
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'reduceOnly': False,
        'recvWindow': 60000
    }
    
    for i, params in enumerate([params1, params2], 1):
        query_string = urllib.parse.urlencode(params)
        signature = hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        print(f"参数顺序 {i}:")
        print(f"  查询字符串: {query_string}")
        print(f"  签名: {signature[:16]}...")
        print()

if __name__ == '__main__':
    try:
        # 1. 调试签名生成
        debug_signature_generation()
        
        # 2. 测试HTTP客户端
        test_with_http_client()
        
        # 3. 分析精度影响
        analyze_precision_in_signature()
        
        # 4. 检查参数顺序
        check_parameter_order()
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
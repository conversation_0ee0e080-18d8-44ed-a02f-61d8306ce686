#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查ICNTUSDT的交易规则和精度问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient
import yaml
import decimal as dec

def load_config():
    """加载配置"""
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_decimal_places(value_str):
    """获取小数位数"""
    if '.' not in str(value_str):
        return 0
    return len(str(value_str).split('.')[1])

def format_order_params(price, qty, tick_size, step_size, min_qty):
    """安全的订单参数格式化"""
    try:
        dec.getcontext().prec = 18
        
        # 价格格式化
        if tick_size and tick_size > 0:
            tick_places = get_decimal_places(tick_size)
            price_dec = dec.Decimal(str(price))
            tick_dec = dec.Decimal(str(tick_size))
            price_quantized = (price_dec / tick_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_dec
            price_str = format(price_quantized, f'.{tick_places}f').rstrip('0').rstrip('.')
        else:
            price_str = str(price)
        
        # 数量格式化
        if step_size and step_size > 0:
            step_places = get_decimal_places(step_size)
            qty_dec = dec.Decimal(str(qty))
            step_dec = dec.Decimal(str(step_size))
            qty_quantized = (qty_dec / step_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_dec
            
            # 确保不小于最小数量
            if min_qty and qty_quantized < dec.Decimal(str(min_qty)):
                qty_quantized = dec.Decimal(str(min_qty))
            
            qty_str = format(qty_quantized, f'.{step_places}f').rstrip('0').rstrip('.')
        else:
            qty_str = str(qty)
        
        # 处理空字符串
        if not price_str or price_str == '':
            price_str = '0'
        if not qty_str or qty_str == '':
            qty_str = '0'
        
        # 确保至少有一位小数（如果原始值有小数）
        if '.' in str(price) and '.' not in price_str:
            price_str += '.0'
        if '.' in str(qty) and '.' not in qty_str:
            qty_str += '.0'
        
        return price_str, qty_str
    except Exception as e:
        print(f"格式化参数时出错: {e}")
        return str(price), str(qty)

def main():
    """主函数"""
    print("=== ICNTUSDT 精度问题调试 ===")
    
    # 加载配置
    config = load_config()
    
    # 创建HTTP客户端
    http = HttpClient(
        api_key=config['api_key'],
        api_secret=config['api_secret'],
        base_url=config.get('base_url', 'https://fapi.binance.com')
    )
    
    symbol = 'ICNTUSDT'
    
    # 1. 获取交易规则
    print(f"\n1. 获取 {symbol} 交易规则...")
    exchange_info = http.get('/fapi/v1/exchangeInfo')
    if not exchange_info or 'symbols' not in exchange_info:
        print("❌ 获取交易规则失败")
        return
    
    symbol_info = None
    for s in exchange_info['symbols']:
        if s['symbol'] == symbol:
            symbol_info = s
            break
    
    if not symbol_info:
        print(f"❌ 未找到 {symbol} 的交易规则")
        return
    
    print(f"✅ 找到 {symbol} 交易规则")
    print(f"   状态: {symbol_info.get('status')}")
    
    # 解析过滤器
    filters = symbol_info.get('filters', [])
    tick_size = None
    step_size = None
    min_qty = None
    min_notional = None
    
    for f in filters:
        ft = f.get('filterType')
        if ft == 'PRICE_FILTER':
            tick_size = float(f.get('tickSize', 0) or 0)
            print(f"   价格过滤器: tickSize={tick_size}")
        elif ft == 'LOT_SIZE':
            step_size = float(f.get('stepSize', 0) or 0)
            min_qty = float(f.get('minQty', 0) or 0)
            print(f"   数量过滤器: stepSize={step_size}, minQty={min_qty}")
        elif ft == 'MIN_NOTIONAL':
            min_notional = float(f.get('notional') or f.get('minNotional') or 0)
            print(f"   最小名义价值: {min_notional}")
    
    # 2. 获取当前价格
    print(f"\n2. 获取 {symbol} 当前价格...")
    ticker = http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
    if not ticker or 'lastPrice' not in ticker:
        print("❌ 获取价格失败")
        return
    
    current_price = float(ticker['lastPrice'])
    print(f"✅ 当前价格: {current_price}")
    
    # 3. 测试不同的下单参数
    print(f"\n3. 测试下单参数格式化...")
    
    # 模拟策略中的计算
    first_nominal = 100  # 假设100 USDT
    qty = first_nominal / current_price
    entry_price = current_price * 0.9962  # 0.38%回踩
    
    print(f"   原始数量: {qty}")
    print(f"   原始价格: {entry_price}")
    
    # 使用旧的格式化方法（可能有问题的）
    print(f"\n   旧格式化方法:")
    try:
        dec.getcontext().prec = 18
        if tick_size:
            price_dec = dec.Decimal(str(entry_price))
            tick_dec = dec.Decimal(str(tick_size))
            price_rounded = (price_dec / tick_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_dec
            old_price_str = format(price_rounded.normalize(), 'f')
        else:
            old_price_str = str(entry_price)
        
        if step_size:
            qty_dec = dec.Decimal(str(qty))
            step_dec = dec.Decimal(str(step_size))
            qty_rounded = (qty_dec / step_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_dec
            qty_rounded = max(qty_rounded, dec.Decimal(str(min_qty or 0)))
            old_qty_str = format(qty_rounded.normalize(), 'f')
        else:
            old_qty_str = str(qty)
        
        print(f"     价格: {old_price_str}")
        print(f"     数量: {old_qty_str}")
    except Exception as e:
        print(f"     ❌ 旧方法出错: {e}")
    
    # 使用新的格式化方法
    print(f"\n   新格式化方法:")
    try:
        new_price_str, new_qty_str = format_order_params(entry_price, qty, tick_size, step_size, min_qty)
        print(f"     价格: {new_price_str}")
        print(f"     数量: {new_qty_str}")
    except Exception as e:
        print(f"     ❌ 新方法出错: {e}")
    
    # 4. 测试降档重试
    print(f"\n4. 测试降档重试...")
    retry_scales = [1.0, 0.75, 0.5, 0.25]
    
    for scale in retry_scales:
        print(f"\n   降档 {int(scale*100)}%:")
        q_try = qty * scale
        
        # 重新计算数量（确保符合步长）
        if step_size and min_qty:
            dec.getcontext().prec = 18
            qty_dec = dec.Decimal(str(q_try))
            step_dec = dec.Decimal(str(step_size))
            qty_rounded = (qty_dec / step_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_dec
            qty_rounded = max(qty_rounded, dec.Decimal(str(min_qty)))
            q_try = float(qty_rounded)
        
        try:
            price_str, qty_str = format_order_params(entry_price, q_try, tick_size, step_size, min_qty)
            print(f"     数量: {q_try} -> {qty_str}")
            print(f"     价格: {entry_price} -> {price_str}")
            
            # 检查参数长度和格式
            print(f"     价格长度: {len(price_str)}, 数量长度: {len(qty_str)}")
            
            # 检查是否包含科学计数法
            if 'e' in price_str.lower() or 'e' in qty_str.lower():
                print(f"     ⚠️  包含科学计数法!")
            
        except Exception as e:
            print(f"     ❌ 格式化出错: {e}")

if __name__ == '__main__':
    main()
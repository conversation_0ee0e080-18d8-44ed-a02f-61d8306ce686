#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Step Size 精度计算测试
验证 step_size 到显示精度的转换逻辑
"""

def test_step_size_precision():
    """测试不同 step_size 值的精度计算"""
    
    test_cases = [
        # (step_size, expected_precision, description)
        (1e-05, 5, "BTCUSDT/ETHUSDT 数量精度"),
        (0.01, 2, "BTCUSDT/ETHUSDT 价格精度"),
        (1e-06, 6, "高精度小数"),
        (1e-09, 9, "极高精度小数"),
        (0.1, 1, "一位小数"),
        (1.0, 0, "整数"),
        (1000.0, 0, "大整数"),
    ]
    
    print("Step Size 精度计算测试")
    print("=" * 50)
    
    for step_size, expected_precision, description in test_cases:
        # 模拟 safe_format_to_precision 中的精度计算逻辑
        step_str = f"{float(step_size):.10f}".rstrip('0').rstrip('.')
        if '.' in step_str:
            decimal_part = step_str.split('.')[1]
            calculated_precision = len(decimal_part)
        else:
            calculated_precision = 0
        
        # 应用最大精度限制（根据智能精度控制）
        step_float = float(step_size)
        if step_float >= 1.0:
            max_decimal_places = 0
        elif step_float >= 0.001:
            max_decimal_places = 3
        else:
            max_decimal_places = 8
        
        display_precision = min(calculated_precision, max_decimal_places)
        
        status = "✓" if display_precision == expected_precision else "✗"
        print(f"{status} {description}")
        print(f"  step_size: {step_size}")
        print(f"  step_str: '{step_str}'")
        print(f"  计算精度: {calculated_precision}")
        print(f"  最大精度: {max_decimal_places}")
        print(f"  显示精度: {display_precision} (期望: {expected_precision})")
        print()

if __name__ == "__main__":
    test_step_size_precision()
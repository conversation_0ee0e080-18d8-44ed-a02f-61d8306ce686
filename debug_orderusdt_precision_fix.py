#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORDERUSDT精度问题分析和修复脚本
基于日志分析和代码审查，找出精度错误的根本原因
"""

import decimal as dec
from decimal import Decimal

def analyze_precision_issue():
    """分析ORDERUSDT精度问题"""
    print("=== ORDERUSDT精度问题分析 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际下单参数:")
    print(f"  数量: {actual_qty}")
    print(f"  价格: {actual_price}")
    print(f"  名义价值: {actual_qty * actual_price:.6f} USDT")
    
    # 模拟不同的交易规则
    test_cases = [
        {
            "name": "高精度新币(8位小数)",
            "tick_size": 0.00000001,  # 8位小数
            "step_size": 0.001,       # 3位小数
            "min_qty": 0.001,
            "min_notional": 5.0
        },
        {
            "name": "中精度新币(6位小数)", 
            "tick_size": 0.000001,    # 6位小数
            "step_size": 0.01,        # 2位小数
            "min_qty": 0.01,
            "min_notional": 5.0
        },
        {
            "name": "低精度新币(4位小数)",
            "tick_size": 0.0001,      # 4位小数
            "step_size": 0.1,         # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        analyze_case(actual_qty, actual_price, case)

def analyze_case(qty, price, rules):
    """分析特定交易规则下的精度处理"""
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules['min_notional']
    
    print(f"交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 使用策略中的精度处理函数
    rounded_price = _round_price(price, tick_size)
    rounded_qty = _round_qty(qty, step_size, min_qty, rounded_price, min_notional)
    
    print(f"精度处理后:")
    print(f"  价格: {rounded_price}")
    print(f"  数量: {rounded_qty}")
    print(f"  名义价值: {rounded_qty * rounded_price:.6f} USDT")
    
    # 模拟下单参数格式化（这是关键问题所在）
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (Decimal(str(rounded_price)) / Decimal(str(tick_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(tick_size))
        price_str = format(price_dec.normalize(), 'f')
    else:
        price_str = str(rounded_price)
    
    # 数量格式化
    if step_size:
        qty_dec = (Decimal(str(rounded_qty)) / Decimal(str(step_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(step_size))
        qty_dec = max(qty_dec, Decimal(str(min_qty or 0)))
        qty_str = format(qty_dec.normalize(), 'f')
    else:
        qty_str = str(rounded_qty)
    
    print(f"格式化字符串:")
    print(f"  价格字符串: '{price_str}'")
    print(f"  数量字符串: '{qty_str}'")
    
    # 检查字符串格式是否符合币安要求
    price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
    qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
    
    # 计算tick_size和step_size的小数位数
    # 对于科学计数法，需要特殊处理
    def get_decimal_places(value):
        if value == 0:
            return 0
        # 转换为字符串并处理科学计数法
        str_val = f"{value:.20f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[-1])
        return 0
    
    tick_decimals = get_decimal_places(tick_size)
    step_decimals = get_decimal_places(step_size)
    
    print(f"小数位数检查:")
    print(f"  价格小数位: {price_decimals} (要求≤{tick_decimals})")
    print(f"  数量小数位: {qty_decimals} (要求≤{step_decimals})")
    
    # 检查是否可能导致精度错误
    precision_issues = []
    if price_decimals > tick_decimals:
        precision_issues.append(f"价格精度过高: {price_decimals} > {tick_decimals}")
    if qty_decimals > step_decimals:
        precision_issues.append(f"数量精度过高: {qty_decimals} > {step_decimals}")
    
    if precision_issues:
        print(f"⚠️  潜在精度问题: {', '.join(precision_issues)}")
    else:
        print("✅ 精度检查通过")

def _round_price(price, tick_size):
    """价格精度处理（复制自策略代码）"""
    if price <= 0 or tick_size <= 0:
        return price
    dec.getcontext().prec = 18
    tick_d = Decimal(str(tick_size))
    price_d = Decimal(str(price))
    rounded = float((price_d / tick_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
    return max(rounded, tick_size)

def _round_qty(q, step, min_q, price=None, min_notional=None):
    """数量精度处理（复制自策略代码）"""
    if q <= 0 or step <= 0:
        return min_q
    dec.getcontext().prec = 18
    step_d = Decimal(str(step))
    
    # 先按步长向下取整
    rounded_down = float((Decimal(str(q)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
    rounded_down = max(rounded_down, min_q)
    
    # 如果提供了价格和最小名义价值，检查是否满足要求
    if price and min_notional:
        notional = rounded_down * price
        if notional < min_notional:
            # 向上调整到满足最小名义价值的数量
            required_qty = min_notional / price
            rounded_up = float((Decimal(str(required_qty)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_UP) * step_d)
            return max(rounded_up, min_q)
    
    return rounded_down

def test_format_fix():
    """测试修复后的格式化方法"""
    print("\n=== 测试修复方案 ===")
    
    # 模拟高精度新币场景
    price = 0.400000
    qty = 236.417
    tick_size = 0.00000001  # 8位小数
    step_size = 0.001       # 3位小数
    
    print(f"原始参数: 价格={price}, 数量={qty}")
    print(f"交易规则: tick_size={tick_size}, step_size={step_size}")
    
    # 当前的格式化方法（可能有问题）
    dec.getcontext().prec = 18
    price_dec = (Decimal(str(price)) / Decimal(str(tick_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(tick_size))
    price_str_old = format(price_dec.normalize(), 'f')
    
    qty_dec = (Decimal(str(qty)) / Decimal(str(step_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(step_size))
    qty_str_old = format(qty_dec.normalize(), 'f')
    
    print(f"当前格式化结果:")
    print(f"  价格: '{price_str_old}' ({len(price_str_old.split('.')[-1])}位小数)")
    print(f"  数量: '{qty_str_old}' ({len(qty_str_old.split('.')[-1])}位小数)")
    
    # 修复后的格式化方法
    # 根据tick_size和step_size确定小数位数
    def get_decimal_places(value):
        if value == 0:
            return 0
        # 转换为字符串并处理科学计数法
        str_val = f"{value:.20f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[-1])
        return 0
    
    tick_decimals = get_decimal_places(tick_size)
    step_decimals = get_decimal_places(step_size)
    
    price_str_new = f"{price_dec:.{tick_decimals}f}".rstrip('0').rstrip('.')
    qty_str_new = f"{qty_dec:.{step_decimals}f}".rstrip('0').rstrip('.')
    
    print(f"修复后格式化结果:")
    print(f"  价格: '{price_str_new}' ({len(price_str_new.split('.')[-1]) if '.' in price_str_new else 0}位小数)")
    print(f"  数量: '{qty_str_new}' ({len(qty_str_new.split('.')[-1]) if '.' in qty_str_new else 0}位小数)")

if __name__ == "__main__":
    analyze_precision_issue()
    test_format_fix()
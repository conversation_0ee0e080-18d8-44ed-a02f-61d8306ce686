#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控增强模块
实时监控交易规则获取、开仓状态、错误模式等
"""

import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import threading
from dataclasses import dataclass, asdict


@dataclass
class TradingAlert:
    """交易告警数据结构"""
    timestamp: str
    level: str  # INFO, WARNING, ERROR, CRITICAL
    category: str  # TRADING_RULES, ORDER_EXECUTION, SYSTEM_ERROR
    symbol: str
    message: str
    details: Dict[str, Any]
    resolved: bool = False


class SystemMonitoringEnhanced:
    """增强的系统监控器"""
    
    def __init__(self, log_dir: str = "logs", alert_threshold: int = 3):
        self.log_dir = log_dir
        self.alert_threshold = alert_threshold
        self.logger = logging.getLogger(__name__)
        
        # 监控数据存储
        self.error_patterns = defaultdict(list)  # 错误模式统计
        self.trading_rules_status = {}  # 交易规则获取状态
        self.order_execution_stats = defaultdict(dict)  # 订单执行统计
        self.alerts = deque(maxlen=1000)  # 告警队列
        
        # 监控阈值配置
        self.thresholds = {
            'precision_error_rate': 0.3,  # 精度错误率阈值30%
            'signature_error_rate': 0.2,   # 签名错误率阈值20%
            'trading_rules_failure_rate': 0.1,  # 交易规则获取失败率阈值10%
            'consecutive_failures': 5,      # 连续失败次数阈值
            'order_success_rate': 0.8,     # 订单成功率阈值80%
        }
        
        # 启动监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._continuous_monitoring, daemon=True)
        self.monitor_thread.start()
    
    def record_trading_rules_event(self, symbol: str, success: bool, method: str, details: Dict = None):
        """记录交易规则获取事件"""
        
        timestamp = datetime.now().isoformat()
        
        if symbol not in self.trading_rules_status:
            self.trading_rules_status[symbol] = {
                'total_attempts': 0,
                'successful_attempts': 0,
                'failed_attempts': 0,
                'last_success_time': None,
                'last_failure_time': None,
                'methods_used': defaultdict(int),
                'recent_events': deque(maxlen=50)
            }
        
        status = self.trading_rules_status[symbol]
        status['total_attempts'] += 1
        status['methods_used'][method] += 1
        
        event = {
            'timestamp': timestamp,
            'success': success,
            'method': method,
            'details': details or {}
        }
        
        if success:
            status['successful_attempts'] += 1
            status['last_success_time'] = timestamp
            self.logger.info(f"✅ {symbol} 交易规则获取成功 ({method})")
        else:
            status['failed_attempts'] += 1
            status['last_failure_time'] = timestamp
            self.logger.warning(f"❌ {symbol} 交易规则获取失败 ({method})")
            
            # 检查是否需要告警
            failure_rate = status['failed_attempts'] / status['total_attempts']
            if failure_rate > self.thresholds['trading_rules_failure_rate']:
                self._create_alert(
                    level='WARNING',
                    category='TRADING_RULES',
                    symbol=symbol,
                    message=f'交易规则获取失败率过高: {failure_rate:.2%}',
                    details={'failure_rate': failure_rate, 'method': method}
                )
        
        status['recent_events'].append(event)
    
    def record_order_execution_event(self, symbol: str, success: bool, error_code: int = None, 
                                   attempt: int = 1, details: Dict = None):
        """记录订单执行事件"""
        
        timestamp = datetime.now().isoformat()
        
        if symbol not in self.order_execution_stats:
            self.order_execution_stats[symbol] = {
                'total_orders': 0,
                'successful_orders': 0,
                'failed_orders': 0,
                'error_codes': defaultdict(int),
                'consecutive_failures': 0,
                'last_success_time': None,
                'last_failure_time': None,
                'recent_events': deque(maxlen=100)
            }
        
        stats = self.order_execution_stats[symbol]
        stats['total_orders'] += 1
        
        event = {
            'timestamp': timestamp,
            'success': success,
            'error_code': error_code,
            'attempt': attempt,
            'details': details or {}
        }
        
        if success:
            stats['successful_orders'] += 1
            stats['consecutive_failures'] = 0
            stats['last_success_time'] = timestamp
            self.logger.info(f"✅ {symbol} 订单执行成功")
        else:
            stats['failed_orders'] += 1
            stats['consecutive_failures'] += 1
            stats['last_failure_time'] = timestamp
            
            if error_code:
                stats['error_codes'][error_code] += 1
                self._analyze_error_pattern(symbol, error_code, attempt)
            
            self.logger.warning(f"❌ {symbol} 订单执行失败 (错误码: {error_code}, 尝试: {attempt})")
            
            # 检查连续失败告警
            if stats['consecutive_failures'] >= self.thresholds['consecutive_failures']:
                self._create_alert(
                    level='ERROR',
                    category='ORDER_EXECUTION',
                    symbol=symbol,
                    message=f'连续订单失败 {stats["consecutive_failures"]} 次',
                    details={'consecutive_failures': stats['consecutive_failures'], 'error_code': error_code}
                )
        
        stats['recent_events'].append(event)
    
    def _analyze_error_pattern(self, symbol: str, error_code: int, attempt: int):
        """分析错误模式"""
        
        pattern_key = f"{symbol}_{error_code}"
        self.error_patterns[pattern_key].append({
            'timestamp': datetime.now().isoformat(),
            'attempt': attempt
        })
        
        # 保留最近100个错误记录
        if len(self.error_patterns[pattern_key]) > 100:
            self.error_patterns[pattern_key] = self.error_patterns[pattern_key][-100:]
        
        # 分析特定错误模式
        if error_code == -1111:  # 精度错误
            recent_precision_errors = len([
                e for e in self.error_patterns[pattern_key]
                if datetime.fromisoformat(e['timestamp']) > datetime.now() - timedelta(hours=1)
            ])
            
            if recent_precision_errors >= 5:
                self._create_alert(
                    level='CRITICAL',
                    category='TRADING_RULES',
                    symbol=symbol,
                    message=f'1小时内精度错误 {recent_precision_errors} 次，可能存在交易规则获取问题',
                    details={'error_code': error_code, 'recent_count': recent_precision_errors}
                )
        
        elif error_code == -1022:  # 签名错误
            recent_signature_errors = len([
                e for e in self.error_patterns[pattern_key]
                if datetime.fromisoformat(e['timestamp']) > datetime.now() - timedelta(minutes=30)
            ])
            
            if recent_signature_errors >= 3:
                self._create_alert(
                    level='ERROR',
                    category='SYSTEM_ERROR',
                    symbol=symbol,
                    message=f'30分钟内签名错误 {recent_signature_errors} 次，可能存在时间同步问题',
                    details={'error_code': error_code, 'recent_count': recent_signature_errors}
                )
    
    def _create_alert(self, level: str, category: str, symbol: str, message: str, details: Dict):
        """创建告警"""
        
        alert = TradingAlert(
            timestamp=datetime.now().isoformat(),
            level=level,
            category=category,
            symbol=symbol,
            message=message,
            details=details
        )
        
        self.alerts.append(alert)
        
        # 记录到日志
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(f"🚨 [{level}] {category} - {symbol}: {message}")
        
        # 保存到文件
        self._save_alert_to_file(alert)
    
    def _save_alert_to_file(self, alert: TradingAlert):
        """保存告警到文件"""
        
        alert_file = os.path.join(self.log_dir, f"alerts_{datetime.now().strftime('%Y%m%d')}.json")
        
        try:
            # 读取现有告警
            alerts_data = []
            if os.path.exists(alert_file):
                with open(alert_file, 'r', encoding='utf-8') as f:
                    alerts_data = json.load(f)
            
            # 添加新告警
            alerts_data.append(asdict(alert))
            
            # 保存回文件
            with open(alert_file, 'w', encoding='utf-8') as f:
                json.dump(alerts_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存告警失败: {e}")
    
    def analyze_error_patterns(self) -> List[Dict[str, Any]]:
        """分析错误模式"""
        patterns = []
        
        try:
            # 分析订单执行错误
            order_errors = {}
            for symbol, stats in self.order_execution_stats.items():
                for error_code, count in stats['error_codes'].items():
                    if count >= 3:  # 至少出现3次才认为是模式
                        pattern = {
                            'type': 'order_execution_error',
                            'symbol': symbol,
                            'error_code': error_code,
                            'count': count,
                            'severity': 'HIGH' if count >= 10 else 'MEDIUM'
                        }
                        
                        # 添加错误描述
                        if error_code == -1111:
                            pattern['description'] = '精度错误 - 价格或数量精度不符合要求'
                            pattern['recommendation'] = '检查交易规则获取和精度处理逻辑'
                        elif error_code == -1022:
                            pattern['description'] = '签名错误 - 请求签名验证失败'
                            pattern['recommendation'] = '检查时间同步和签名算法'
                        elif error_code == -2010:
                            pattern['description'] = '余额不足'
                            pattern['recommendation'] = '检查账户余额和风险管理'
                        else:
                            pattern['description'] = f'未知错误码: {error_code}'
                            pattern['recommendation'] = '查阅API文档或联系技术支持'
                        
                        patterns.append(pattern)
            
            # 分析交易规则获取失败模式
            for symbol, status in self.trading_rules_status.items():
                if status['failed_attempts'] >= 2:  # 至少失败2次
                    pattern = {
                        'type': 'trading_rules_failure',
                        'symbol': symbol,
                        'count': status['failed_attempts'],
                        'severity': 'HIGH' if status['failed_attempts'] >= 5 else 'MEDIUM',
                        'description': f'{symbol} 交易规则获取持续失败',
                        'recommendation': '检查网络连接、API权限或符号有效性'
                    }
                    patterns.append(pattern)
        
        except Exception as e:
            self.logger.error(f"错误模式分析失败: {e}")
        
        return patterns

    def get_system_health_report(self) -> Dict[str, Any]:
        """获取系统健康报告"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'HEALTHY',
            'trading_rules_summary': {},
            'order_execution_summary': {},
            'recent_alerts': [],
            'recommendations': []
        }
        
        # 交易规则获取汇总
        total_symbols = len(self.trading_rules_status)
        if total_symbols > 0:
            total_attempts = sum(s['total_attempts'] for s in self.trading_rules_status.values())
            total_failures = sum(s['failed_attempts'] for s in self.trading_rules_status.values())
            success_rate = (total_attempts - total_failures) / total_attempts if total_attempts > 0 else 1.0
            failure_rate = total_failures / total_attempts if total_attempts > 0 else 0
            
            report['trading_rules_summary'] = {
                'total_symbols': total_symbols,
                'total_attempts': total_attempts,
                'success_rate': success_rate,
                'failure_rate': failure_rate,
                'status': 'CRITICAL' if failure_rate > 0.2 else 'WARNING' if failure_rate > 0.1 else 'HEALTHY'
            }
            
            # 设置成功率字段
            report['trading_rules_success_rate'] = success_rate
            
            if failure_rate > self.thresholds['trading_rules_failure_rate']:
                report['overall_status'] = 'WARNING'
                report['recommendations'].append('交易规则获取失败率过高，建议检查网络连接和API配置')
        else:
            report['trading_rules_success_rate'] = 1.0
        
        # 订单执行汇总
        total_order_symbols = len(self.order_execution_stats)
        if total_order_symbols > 0:
            total_orders = sum(s['total_orders'] for s in self.order_execution_stats.values())
            total_order_failures = sum(s['failed_orders'] for s in self.order_execution_stats.values())
            order_success_rate = (total_orders - total_order_failures) / total_orders if total_orders > 0 else 1.0
            order_failure_rate = total_order_failures / total_orders if total_orders > 0 else 0
            
            report['order_execution_summary'] = {
                'total_symbols': total_order_symbols,
                'total_orders': total_orders,
                'success_rate': order_success_rate,
                'failure_rate': order_failure_rate,
                'status': 'CRITICAL' if order_failure_rate > 0.5 else 'WARNING' if order_failure_rate > 0.2 else 'HEALTHY'
            }
            
            # 设置成功率字段
            report['order_execution_success_rate'] = order_success_rate
            
            if order_failure_rate > (1 - self.thresholds['order_success_rate']):
                report['overall_status'] = 'CRITICAL'
                report['recommendations'].append('订单执行失败率过高，建议检查交易规则和账户状态')
        else:
            report['order_execution_success_rate'] = 1.0
        
        # 分析错误模式
        error_patterns = self.analyze_error_patterns()
        high_severity_patterns = [p for p in error_patterns if p.get('severity') == 'HIGH']
        
        report['error_patterns_count'] = len(error_patterns)
        report['high_severity_patterns_count'] = len(high_severity_patterns)
        report['error_patterns'] = error_patterns[:5]  # 只返回前5个模式
        
        # 最近告警
        recent_alerts = [asdict(alert) for alert in list(self.alerts)[-10:]]
        report['recent_alerts'] = recent_alerts
        
        # 设置整体状态
        critical_alerts = [a for a in recent_alerts if a['level'] == 'CRITICAL']
        if critical_alerts or len(high_severity_patterns) > 2:
            report['overall_status'] = 'CRITICAL'
        elif report['overall_status'] == 'HEALTHY' and (len(recent_alerts) > 5 or len(high_severity_patterns) > 0):
            report['overall_status'] = 'WARNING'
        
        if len(high_severity_patterns) > 0:
            report['recommendations'].append('处理高严重性错误模式')
        
        return report
    
    def _continuous_monitoring(self):
        """持续监控线程"""
        
        while self.monitoring_active:
            try:
                # 每5分钟生成健康报告
                report = self.get_system_health_report()
                
                if report['overall_status'] != 'HEALTHY':
                    self.logger.warning(f"系统状态: {report['overall_status']}")
                    
                    # 保存健康报告
                    report_file = os.path.join(
                        self.log_dir, 
                        f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    )
                    
                    with open(report_file, 'w', encoding='utf-8') as f:
                        json.dump(report, f, ensure_ascii=False, indent=2)
                
                time.sleep(300)  # 5分钟检查一次
                
            except Exception as e:
                self.logger.error(f"监控线程错误: {e}")
                time.sleep(60)  # 出错后1分钟重试
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)


def test_monitoring():
    """测试监控功能"""
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
    
    # 创建监控器
    monitor = SystemMonitoringEnhanced()
    
    # 模拟一些事件
    monitor.record_trading_rules_event('PLAYUSDT', True, 'cache')
    monitor.record_trading_rules_event('PLAYUSDT', False, 'api', {'error': 'timeout'})
    monitor.record_order_execution_event('PLAYUSDT', False, -1111, 1)
    monitor.record_order_execution_event('PLAYUSDT', False, -1022, 2)
    
    # 获取健康报告
    report = monitor.get_system_health_report()
    print(json.dumps(report, ensure_ascii=False, indent=2))
    
    # 停止监控
    monitor.stop_monitoring()


if __name__ == '__main__':
    test_monitoring()
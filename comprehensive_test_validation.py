#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试验证修复效果
验证交易规则获取、精度处理、监控告警等修复方案的有效性
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from emergency_fix_trading_rules import EmergencyTradingRulesFix
from system_monitoring_enhanced import SystemMonitoringEnhanced

class ComprehensiveTestValidation:
    """全面测试验证类"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.test_results = {}
        self.start_time = datetime.now()
        
        # 测试符号列表
        self.test_symbols = [
            'BTCUSDT',    # 主流币种
            'ETHUSDT',    # 主流币种
            'PLAYUSDT',   # 问题币种
            'PROVEUSDT',  # 问题币种
            'PIPPINUSDT', # 问题币种
            'ADAUSDT',    # 其他币种
            'DOTUSDT',    # 其他币种
        ]
        
        self.logger.info("🚀 开始全面测试验证修复效果")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('TestValidation')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 文件处理器
            log_file = f"logs/test_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 格式化
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)
            
            logger.addHandler(console_handler)
            logger.addHandler(file_handler)
        
        return logger
    
    def test_trading_rules_acquisition(self) -> Dict[str, Any]:
        """测试交易规则获取功能"""
        self.logger.info("📋 测试1: 交易规则获取功能")
        
        results = {
            'test_name': '交易规则获取',
            'start_time': datetime.now().isoformat(),
            'symbols_tested': [],
            'success_count': 0,
            'failure_count': 0,
            'details': {}
        }
        
        try:
            # 初始化修复器（模拟HTTP客户端）
            class MockHttpClient:
                def get(self, endpoint, params=None):
                    # 模拟API响应
                    if 'exchangeInfo' in endpoint:
                        return {
                            'symbols': [{
                                'symbol': params.get('symbol', 'BTCUSDT'),
                                'status': 'TRADING',
                                'filters': [
                                    {'filterType': 'PRICE_FILTER', 'tickSize': '0.01'},
                                    {'filterType': 'LOT_SIZE', 'stepSize': '0.001'},
                                    {'filterType': 'MIN_NOTIONAL', 'minNotional': '5.0'}
                                ]
                            }]
                        }
                    return {}
            
            fixer = EmergencyTradingRulesFix(MockHttpClient())
            
            for symbol in self.test_symbols:
                self.logger.info(f"  测试符号: {symbol}")
                
                try:
                    # 测试获取交易规则
                    rules = fixer.get_trading_rules_enhanced(symbol)
                    
                    if rules and all(key in rules for key in ['tick_size', 'step_size', 'min_qty']):
                        results['success_count'] += 1
                        results['details'][symbol] = {
                            'status': 'SUCCESS',
                            'rules': rules,
                            'validation': self._validate_trading_rules(rules)
                        }
                        self.logger.info(f"    ✅ {symbol} 规则获取成功: {rules}")
                    else:
                        results['failure_count'] += 1
                        results['details'][symbol] = {
                            'status': 'FAILED',
                            'rules': rules,
                            'error': '规则不完整'
                        }
                        self.logger.error(f"    ❌ {symbol} 规则获取失败")
                
                except Exception as e:
                    results['failure_count'] += 1
                    results['details'][symbol] = {
                        'status': 'ERROR',
                        'error': str(e)
                    }
                    self.logger.error(f"    ❌ {symbol} 测试异常: {e}")
                
                results['symbols_tested'].append(symbol)
        
        except Exception as e:
            self.logger.error(f"交易规则获取测试失败: {e}")
            results['test_error'] = str(e)
        
        results['end_time'] = datetime.now().isoformat()
        results['success_rate'] = results['success_count'] / len(self.test_symbols) * 100
        
        self.logger.info(f"📊 交易规则获取测试完成 - 成功率: {results['success_rate']:.1f}%")
        return results
    
    def _validate_trading_rules(self, rules: Dict[str, Any]) -> Dict[str, bool]:
        """验证交易规则的有效性"""
        validation = {}
        
        # 检查tick_size
        tick_size = rules.get('tick_size')
        validation['tick_size_valid'] = isinstance(tick_size, (int, float)) and tick_size > 0
        
        # 检查step_size
        step_size = rules.get('step_size')
        validation['step_size_valid'] = isinstance(step_size, (int, float)) and step_size > 0
        
        # 检查min_qty
        min_qty = rules.get('min_qty')
        validation['min_qty_valid'] = isinstance(min_qty, (int, float)) and min_qty > 0
        
        # 检查min_notional
        min_notional = rules.get('min_notional', 5.0)
        validation['min_notional_valid'] = isinstance(min_notional, (int, float)) and min_notional > 0
        
        validation['all_valid'] = all(validation.values())
        
        return validation
    
    def test_precision_handling(self) -> Dict[str, Any]:
        """测试精度处理功能"""
        self.logger.info("🔢 测试2: 精度处理功能")
        
        results = {
            'test_name': '精度处理',
            'start_time': datetime.now().isoformat(),
            'test_cases': [],
            'success_count': 0,
            'failure_count': 0
        }
        
        # 测试用例
        test_cases = [
            # (价格, tick_size, 期望结果) - 向下舍入
            (50123.456789, 0.01, 50123.45),  # 50123.456789 -> 50123.45
            (50123.454, 0.01, 50123.45),     # 50123.454 -> 50123.45
            (0.000012345, 0.000001, 0.000012),  # 0.000012345 -> 0.000012
            (1.23456789, 0.0001, 1.2345),    # 1.23456789 -> 1.2345
            
            # (数量, step_size, 期望结果) - 向下舍入
            (10.123456, 0.001, 10.123),      # 10.123456 -> 10.123
            (0.000123456, 0.000001, 0.000123),  # 0.000123456 -> 0.000123
            (100.999, 1.0, 100.0),           # 100.999 -> 100.0
        ]
        
        try:
            class MockHttpClient:
                def get(self, endpoint, params=None):
                    return {}
            
            fixer = EmergencyTradingRulesFix(MockHttpClient())
            
            for i, (value, precision, expected) in enumerate(test_cases):
                test_case = {
                    'case_id': i + 1,
                    'input_value': value,
                    'precision': precision,
                    'expected': expected
                }
                
                try:
                    # 初始化result为原始值
                    result = value
                    
                    # 测试价格精度处理
                    if i < 4:  # 前4个是价格测试
                        self.logger.info(f"    测试价格精度: {value} with precision {precision}")
                        result = fixer._round_price(value, precision)
                        test_case['type'] = 'price'
                        self.logger.info(f"    价格精度结果: {result}")
                    else:  # 后面是数量测试
                        self.logger.info(f"    测试数量精度: {value} with precision {precision}")
                        result = fixer._round_qty(value, precision)
                        test_case['type'] = 'quantity'
                        self.logger.info(f"    数量精度结果: {result}")
                    
                    test_case['actual'] = result
                    test_case['success'] = abs(result - expected) < 1e-10
                    
                    if test_case['success']:
                        results['success_count'] += 1
                        self.logger.info(f"    ✅ 测试用例 {i+1}: {value} -> {result} (期望: {expected})")
                    else:
                        results['failure_count'] += 1
                        self.logger.error(f"    ❌ 测试用例 {i+1}: {value} -> {result} (期望: {expected}), 差值: {abs(result - expected)}")
                
                except Exception as e:
                    test_case['error'] = str(e)
                    test_case['actual'] = value  # 异常时使用原始值
                    test_case['success'] = False
                    results['failure_count'] += 1
                    self.logger.error(f"    ❌ 测试用例 {i+1} 异常: {e}")
                    self.logger.error(f"    异常详情: {traceback.format_exc()}")
                
                results['test_cases'].append(test_case)
        
        except Exception as e:
            self.logger.error(f"精度处理测试失败: {e}")
            results['test_error'] = str(e)
        
        results['end_time'] = datetime.now().isoformat()
        results['success_rate'] = results['success_count'] / len(test_cases) * 100 if test_cases else 0
        
        self.logger.info(f"📊 精度处理测试完成 - 成功率: {results['success_rate']:.1f}%")
        return results
    
    def test_monitoring_system(self) -> Dict[str, Any]:
        """测试监控系统功能"""
        self.logger.info("📊 测试3: 监控系统功能")
        
        results = {
            'test_name': '监控系统',
            'start_time': datetime.now().isoformat(),
            'components_tested': [],
            'success_count': 0,
            'failure_count': 0,
            'details': {}
        }
        
        try:
            monitor = SystemMonitoringEnhanced(log_dir="logs")
            
            # 测试1: 交易规则事件记录
            self.logger.info("  测试监控组件1: 交易规则事件记录")
            try:
                monitor.record_trading_rules_event('TESTUSDT', True, 'cache')
                monitor.record_trading_rules_event('TESTUSDT', False, 'api', {'error': 'timeout'})
                
                results['success_count'] += 1
                results['details']['trading_rules_events'] = 'SUCCESS'
                self.logger.info("    ✅ 交易规则事件记录正常")
            except Exception as e:
                results['failure_count'] += 1
                results['details']['trading_rules_events'] = f'FAILED: {e}'
                self.logger.error(f"    ❌ 交易规则事件记录失败: {e}")
            
            results['components_tested'].append('trading_rules_events')
            
            # 测试2: 订单执行事件记录
            self.logger.info("  测试监控组件2: 订单执行事件记录")
            try:
                monitor.record_order_execution_event('TESTUSDT', True, attempt=1)
                monitor.record_order_execution_event('TESTUSDT', False, -1111, 2, {'error': 'precision'})
                
                results['success_count'] += 1
                results['details']['order_execution_events'] = 'SUCCESS'
                self.logger.info("    ✅ 订单执行事件记录正常")
            except Exception as e:
                results['failure_count'] += 1
                results['details']['order_execution_events'] = f'FAILED: {e}'
                self.logger.error(f"    ❌ 订单执行事件记录失败: {e}")
            
            results['components_tested'].append('order_execution_events')
            
            # 测试3: 错误模式分析
            self.logger.info("  测试监控组件3: 错误模式分析")
            try:
                # 添加一些测试数据
                for i in range(5):
                    monitor.record_order_execution_event('TESTUSDT', False, -1111, 1, {'error': 'precision'})
                
                patterns = monitor.analyze_error_patterns()
                
                if patterns and len(patterns) > 0:
                    results['success_count'] += 1
                    results['details']['error_pattern_analysis'] = 'SUCCESS'
                    results['details']['patterns_found'] = len(patterns)
                    self.logger.info(f"    ✅ 错误模式分析正常，发现 {len(patterns)} 个模式")
                else:
                    results['failure_count'] += 1
                    results['details']['error_pattern_analysis'] = 'FAILED: No patterns found'
                    self.logger.error("    ❌ 错误模式分析失败：未发现模式")
            except Exception as e:
                results['failure_count'] += 1
                results['details']['error_pattern_analysis'] = f'FAILED: {e}'
                self.logger.error(f"    ❌ 错误模式分析失败: {e}")
            
            results['components_tested'].append('error_pattern_analysis')
            
            # 测试4: 健康报告生成
            self.logger.info("  测试监控组件4: 健康报告生成")
            try:
                health_report = monitor.get_system_health_report()
                
                required_keys = ['overall_status', 'trading_rules_success_rate', 'order_execution_success_rate']
                if all(key in health_report for key in required_keys):
                    results['success_count'] += 1
                    results['details']['health_report'] = 'SUCCESS'
                    results['details']['health_status'] = health_report['overall_status']
                    self.logger.info(f"    ✅ 健康报告生成正常，状态: {health_report['overall_status']}")
                else:
                    results['failure_count'] += 1
                    results['details']['health_report'] = 'FAILED: Missing required keys'
                    self.logger.error("    ❌ 健康报告生成失败：缺少必要字段")
            except Exception as e:
                results['failure_count'] += 1
                results['details']['health_report'] = f'FAILED: {e}'
                self.logger.error(f"    ❌ 健康报告生成失败: {e}")
            
            results['components_tested'].append('health_report')
            
            # 停止监控
            monitor.stop_monitoring()
        
        except Exception as e:
            self.logger.error(f"监控系统测试失败: {e}")
            results['test_error'] = str(e)
        
        results['end_time'] = datetime.now().isoformat()
        total_components = len(results['components_tested'])
        results['success_rate'] = results['success_count'] / total_components * 100 if total_components > 0 else 0
        
        self.logger.info(f"📊 监控系统测试完成 - 成功率: {results['success_rate']:.1f}%")
        return results
    
    def test_retry_mechanism(self) -> Dict[str, Any]:
        """测试智能重试机制"""
        self.logger.info("🔄 测试4: 智能重试机制")
        
        results = {
            'test_name': '智能重试机制',
            'start_time': datetime.now().isoformat(),
            'test_scenarios': [],
            'success_count': 0,
            'failure_count': 0
        }
        
        # 测试场景
        test_scenarios = [
            (-1111, 1, 'refresh_rules'),  # 精度错误 -> 刷新规则
            (-1022, 1, 'refresh_timestamp'),  # 签名错误 -> 刷新时间戳
            (-2010, 1, 'normal_retry'),  # 余额不足 -> 正常重试
            (-1111, 3, 'skip'),  # 精度错误重试3次 -> 跳过
            (-9999, 1, 'normal_retry'),  # 未知错误 -> 正常重试
        ]
        
        try:
            class MockHttpClient:
                def get(self, endpoint, params=None):
                    return {}
            
            fixer = EmergencyTradingRulesFix(MockHttpClient())
            
            for error_code, attempt, expected_action in test_scenarios:
                scenario = {
                    'error_code': error_code,
                    'attempt': attempt,
                    'expected_action': expected_action
                }
                
                try:
                    actual_action = fixer.smart_retry_mechanism('TESTUSDT', error_code, attempt)
                    scenario['actual_action'] = actual_action
                    scenario['success'] = actual_action == expected_action
                    
                    if scenario['success']:
                        results['success_count'] += 1
                        self.logger.info(f"    ✅ 错误码 {error_code}, 尝试 {attempt}: {actual_action}")
                    else:
                        results['failure_count'] += 1
                        self.logger.error(f"    ❌ 错误码 {error_code}, 尝试 {attempt}: 期望 {expected_action}, 实际 {actual_action}")
                
                except Exception as e:
                    scenario['error'] = str(e)
                    scenario['success'] = False
                    results['failure_count'] += 1
                    self.logger.error(f"    ❌ 重试机制测试异常: {e}")
                
                results['test_scenarios'].append(scenario)
        
        except Exception as e:
            self.logger.error(f"重试机制测试失败: {e}")
            results['test_error'] = str(e)
        
        results['end_time'] = datetime.now().isoformat()
        total_scenarios = len(test_scenarios)
        results['success_rate'] = results['success_count'] / total_scenarios * 100 if total_scenarios > 0 else 0
        
        self.logger.info(f"📊 智能重试机制测试完成 - 成功率: {results['success_rate']:.1f}%")
        return results
    
    def test_integration_compatibility(self) -> Dict[str, Any]:
        """测试集成兼容性"""
        self.logger.info("🔗 测试5: 集成兼容性")
        
        results = {
            'test_name': '集成兼容性',
            'start_time': datetime.now().isoformat(),
            'compatibility_checks': [],
            'success_count': 0,
            'failure_count': 0,
            'details': {}
        }
        
        compatibility_checks = [
            'emergency_fix_import',
            'system_monitoring_import',
            'config_file_access',
            'log_directory_access',
            'strategy_file_syntax'
        ]
        
        for check in compatibility_checks:
            check_result = {'check_name': check}
            
            try:
                if check == 'emergency_fix_import':
                    from emergency_fix_trading_rules import EmergencyTradingRulesFix
                    check_result['status'] = 'SUCCESS'
                    self.logger.info("    ✅ 紧急修复模块导入成功")
                
                elif check == 'system_monitoring_import':
                    from system_monitoring_enhanced import SystemMonitoringEnhanced
                    check_result['status'] = 'SUCCESS'
                    self.logger.info("    ✅ 系统监控模块导入成功")
                
                elif check == 'config_file_access':
                    config_path = 'config/config.json'
                    if os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            json.load(f)
                        check_result['status'] = 'SUCCESS'
                        self.logger.info("    ✅ 配置文件访问正常")
                    else:
                        check_result['status'] = 'WARNING'
                        check_result['message'] = '配置文件不存在'
                        self.logger.warning("    ⚠️ 配置文件不存在")
                
                elif check == 'log_directory_access':
                    log_dir = 'logs'
                    os.makedirs(log_dir, exist_ok=True)
                    test_file = os.path.join(log_dir, 'test_write.tmp')
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    check_result['status'] = 'SUCCESS'
                    self.logger.info("    ✅ 日志目录访问正常")
                
                elif check == 'strategy_file_syntax':
                    strategy_file = 'strategy/maker_channel_enhanced.py'
                    if os.path.exists(strategy_file):
                        import py_compile
                        py_compile.compile(strategy_file, doraise=True)
                        check_result['status'] = 'SUCCESS'
                        self.logger.info("    ✅ 策略文件语法检查通过")
                    else:
                        check_result['status'] = 'WARNING'
                        check_result['message'] = '策略文件不存在'
                        self.logger.warning("    ⚠️ 策略文件不存在")
                
                if check_result['status'] == 'SUCCESS':
                    results['success_count'] += 1
                else:
                    results['failure_count'] += 1
            
            except Exception as e:
                check_result['status'] = 'FAILED'
                check_result['error'] = str(e)
                results['failure_count'] += 1
                self.logger.error(f"    ❌ {check} 检查失败: {e}")
            
            results['compatibility_checks'].append(check_result)
        
        results['end_time'] = datetime.now().isoformat()
        total_checks = len(compatibility_checks)
        results['success_rate'] = results['success_count'] / total_checks * 100 if total_checks > 0 else 0
        
        self.logger.info(f"📊 集成兼容性测试完成 - 成功率: {results['success_rate']:.1f}%")
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.logger.info("🎯 开始运行全面测试验证")
        
        all_results = {
            'test_suite': '全面修复效果验证',
            'start_time': self.start_time.isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # 运行各项测试
        test_methods = [
            ('trading_rules_acquisition', self.test_trading_rules_acquisition),
            ('precision_handling', self.test_precision_handling),
            ('monitoring_system', self.test_monitoring_system),
            ('retry_mechanism', self.test_retry_mechanism),
            ('integration_compatibility', self.test_integration_compatibility),
        ]
        
        total_success = 0
        total_tests = 0
        
        for test_name, test_method in test_methods:
            self.logger.info(f"\n{'='*60}")
            try:
                result = test_method()
                all_results['tests'][test_name] = result
                
                # 统计
                if 'success_rate' in result:
                    total_success += result.get('success_count', 0)
                    total_tests += result.get('success_count', 0) + result.get('failure_count', 0)
            
            except Exception as e:
                self.logger.error(f"测试 {test_name} 执行失败: {e}")
                all_results['tests'][test_name] = {
                    'test_name': test_name,
                    'status': 'ERROR',
                    'error': str(e),
                    'traceback': traceback.format_exc()
                }
        
        # 生成总结
        all_results['end_time'] = datetime.now().isoformat()
        all_results['duration'] = str(datetime.now() - self.start_time)
        
        overall_success_rate = (total_success / total_tests * 100) if total_tests > 0 else 0
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'total_success': total_success,
            'total_failures': total_tests - total_success,
            'overall_success_rate': overall_success_rate,
            'status': 'PASSED' if overall_success_rate >= 80 else 'FAILED'
        }
        
        # 保存结果
        self._save_test_results(all_results)
        
        # 输出总结
        self._print_test_summary(all_results)
        
        return all_results
    
    def _save_test_results(self, results: Dict[str, Any]):
        """保存测试结果"""
        try:
            os.makedirs('logs', exist_ok=True)
            result_file = f"logs/test_validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📄 测试结果已保存到: {result_file}")
        
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")
    
    def _print_test_summary(self, results: Dict[str, Any]):
        """打印测试总结"""
        self.logger.info(f"\n{'='*80}")
        self.logger.info("🎯 全面测试验证总结报告")
        self.logger.info(f"{'='*80}")
        
        summary = results['summary']
        self.logger.info(f"📊 总体统计:")
        self.logger.info(f"   总测试数: {summary['total_tests']}")
        self.logger.info(f"   成功数: {summary['total_success']}")
        self.logger.info(f"   失败数: {summary['total_failures']}")
        self.logger.info(f"   成功率: {summary['overall_success_rate']:.1f}%")
        self.logger.info(f"   总体状态: {summary['status']}")
        
        self.logger.info(f"\n📋 各项测试详情:")
        for test_name, test_result in results['tests'].items():
            if 'success_rate' in test_result:
                status_icon = "✅" if test_result['success_rate'] >= 80 else "❌"
                self.logger.info(f"   {status_icon} {test_result['test_name']}: {test_result['success_rate']:.1f}%")
            else:
                self.logger.info(f"   ❌ {test_name}: 测试失败")
        
        # 建议
        self.logger.info(f"\n💡 建议:")
        if summary['overall_success_rate'] >= 90:
            self.logger.info("   🎉 修复方案效果优秀，可以部署到生产环境")
        elif summary['overall_success_rate'] >= 80:
            self.logger.info("   ✅ 修复方案效果良好，建议在测试环境进一步验证")
        elif summary['overall_success_rate'] >= 60:
            self.logger.info("   ⚠️ 修复方案需要进一步优化，请检查失败的测试项")
        else:
            self.logger.info("   🚨 修复方案存在严重问题，需要重新设计")
        
        self.logger.info(f"{'='*80}")

def main():
    """主函数"""
    try:
        validator = ComprehensiveTestValidation()
        results = validator.run_all_tests()
        
        # 返回退出码
        success_rate = results['summary']['overall_success_rate']
        exit_code = 0 if success_rate >= 80 else 1
        
        print(f"\n🏁 测试完成，退出码: {exit_code}")
        return exit_code
    
    except Exception as e:
        print(f"❌ 测试验证失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
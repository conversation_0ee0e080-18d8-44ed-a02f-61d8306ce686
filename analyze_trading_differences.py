#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析两种交易执行方式的差异
1. auto_trading_test.py (成功)
2. MakerChannelEnhanced 策略 (失败)
"""

import sys
import os
import json
import yaml
import time
from decimal import Decimal

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient
from strategy.maker_channel_enhanced import MakerChannelEnhanced

class TradingDifferenceAnalyzer:
    def __init__(self):
        self.symbol = "PIPPINUSDT"  # 使用出现问题的交易对
        self.test_nominal = 10.0
        
        # 初始化配置
        self.setup_config()
        
        # 初始化HTTP客户端
        self.http = HttpClient(
            self.config['api_key'],
            self.config['api_secret'],
            base_url=self.config.get('base_url', 'https://fapi.binance.com'),
            verify_ssl=self.config.get('verify_ssl', True)
        )
        
        # 初始化策略
        self.strategy = MakerChannelEnhanced(self.http, self.config)
        
        print("=" * 80)
        print("交易执行方式差异分析")
        print("=" * 80)
        print(f"测试标的: {self.symbol}")
        print(f"测试名义价值: {self.test_nominal} USDT")
        print()
    
    def setup_config(self):
        """设置配置"""
        try:
            with open('config/config.yaml', 'r', encoding='utf-8') as f:
                base_cfg = yaml.safe_load(f)
            
            try:
                with open('config/config.json', 'r', encoding='utf-8') as f:
                    json_cfg = json.load(f)
            except:
                json_cfg = {}
            
            self.config = {**base_cfg, **json_cfg}
        except Exception as e:
            print(f"配置加载失败: {e}")
            raise
    
    def get_trading_rules(self):
        """获取交易规则"""
        print("1. 获取交易规则")
        print("-" * 40)
        
        try:
            # 获取交易规则
            exchange_info = self.http.get('/fapi/v1/exchangeInfo')
            
            symbol_info = None
            for s in exchange_info.get('symbols', []):
                if s['symbol'] == self.symbol:
                    symbol_info = s
                    break
            
            if not symbol_info:
                raise Exception(f"未找到 {self.symbol} 交易对")
            
            # 解析过滤器
            filters = {}
            for f in symbol_info['filters']:
                filters[f['filterType']] = f
            
            # 提取关键参数
            self.tick_size = float(filters['PRICE_FILTER']['tickSize'])
            self.step_size = float(filters['LOT_SIZE']['stepSize'])
            self.min_qty = float(filters['LOT_SIZE']['minQty'])
            self.min_notional = float(filters['MIN_NOTIONAL']['notional'])
            
            print(f"✅ 交易规则获取成功:")
            print(f"  tickSize: {self.tick_size}")
            print(f"  stepSize: {self.step_size}")
            print(f"  minQty: {self.min_qty}")
            print(f"  minNotional: {self.min_notional}")
            
            # 获取当前价格
            ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': self.symbol})
            self.current_price = float(ticker['price'])
            print(f"  当前价格: {self.current_price}")
            print()
            
            return True
            
        except Exception as e:
            print(f"❌ 获取交易规则失败: {e}")
            return False
    
    def test_auto_trading_approach(self):
        """测试 auto_trading_test.py 的方法"""
        print("2. 测试 auto_trading_test.py 方法")
        print("-" * 40)
        
        try:
            # 步骤1: 计算原始数量
            raw_qty = self.test_nominal / self.current_price
            print(f"原始数量计算: {self.test_nominal} / {self.current_price} = {raw_qty}")
            
            # 步骤2: 使用策略的精度处理方法
            entry_qty = self.strategy._round_qty(
                raw_qty, 
                self.step_size, 
                self.min_qty, 
                self.current_price, 
                self.min_notional
            )
            
            # 步骤3: 调整价格精度
            entry_price = self.strategy._round_price(self.current_price, self.tick_size)
            
            print(f"精度调整后:")
            print(f"  数量: {raw_qty} -> {entry_qty}")
            print(f"  价格: {self.current_price} -> {entry_price}")
            
            # 步骤4: 格式化订单参数
            price_str, qty_str = self.strategy._format_order_params(
                entry_price,
                entry_qty,
                self.tick_size,
                self.step_size,
                self.min_qty
            )
            
            print(f"格式化结果:")
            print(f"  price_str: '{price_str}' (长度: {len(price_str)})")
            print(f"  qty_str: '{qty_str}' (长度: {len(qty_str)})")
            
            # 验证精度
            price_decimals = len(price_str.split('.')[1]) if '.' in price_str else 0
            qty_decimals = len(qty_str.split('.')[1]) if '.' in qty_str else 0
            
            print(f"精度验证:")
            print(f"  价格小数位: {price_decimals}")
            print(f"  数量小数位: {qty_decimals}")
            
            # 检查科学计数法
            has_scientific = 'e' in price_str.lower() or 'e' in qty_str.lower()
            print(f"  科学计数法: {'是' if has_scientific else '否'}")
            
            # 构建订单参数
            order_params = {
                'symbol': self.symbol,
                'side': 'BUY',
                'type': 'MARKET',
                'quantity': qty_str,
            }
            
            print(f"订单参数: {order_params}")
            
            # 计算实际名义价值
            actual_notional = entry_price * entry_qty
            print(f"实际名义价值: {actual_notional:.2f} USDT")
            
            success = (price_decimals <= 8 and qty_decimals <= 8 and 
                      not has_scientific and actual_notional >= self.min_notional)
            
            print(f"✅ auto_trading_test.py 方法: {'成功' if success else '失败'}")
            print()
            
            return {
                'success': success,
                'raw_qty': raw_qty,
                'entry_qty': entry_qty,
                'entry_price': entry_price,
                'price_str': price_str,
                'qty_str': qty_str,
                'price_decimals': price_decimals,
                'qty_decimals': qty_decimals,
                'has_scientific': has_scientific,
                'actual_notional': actual_notional,
                'order_params': order_params
            }
            
        except Exception as e:
            print(f"❌ auto_trading_test.py 方法失败: {e}")
            print()
            return {'success': False, 'error': str(e)}
    
    def test_strategy_approach(self):
        """测试 MakerChannelEnhanced 策略的方法"""
        print("3. 测试 MakerChannelEnhanced 策略方法")
        print("-" * 40)
        
        try:
            # 模拟策略中的开仓流程
            # 步骤1: 计算数量（策略中的逻辑）
            qty = self.test_nominal / self.current_price
            qty = self.strategy._round_qty(qty, self.step_size, self.min_qty, self.current_price, self.min_notional)
            entry_price = self.strategy._round_price(self.current_price, self.tick_size)
            
            print(f"策略计算结果:")
            print(f"  数量: {qty}")
            print(f"  价格: {entry_price}")
            
            # 步骤2: 模拟 place_maker_order 中的重试逻辑
            retry_scales = [1.0, 0.75, 0.5, 0.25]
            
            for i, scale in enumerate(retry_scales):
                print(f"\n降档 {int(scale*100)}%:")
                
                q_try = qty * scale
                q_try = self.strategy._round_qty(q_try, self.step_size, self.min_qty, entry_price, self.min_notional)
                
                print(f"  降档后数量: {q_try}")
                
                # 使用策略的格式化方法
                price_str, qty_str = self.strategy._format_order_params(
                    entry_price, q_try, self.tick_size, self.step_size, self.min_qty
                )
                
                print(f"  格式化结果:")
                print(f"    价格: '{price_str}' (长度: {len(price_str)})")
                print(f"    数量: '{qty_str}' (长度: {len(qty_str)})")
                
                # 验证精度
                price_decimals = len(price_str.split('.')[1]) if '.' in price_str else 0
                qty_decimals = len(qty_str.split('.')[1]) if '.' in qty_str else 0
                has_scientific = 'e' in price_str.lower() or 'e' in qty_str.lower()
                
                print(f"    价格小数位: {price_decimals}")
                print(f"    数量小数位: {qty_decimals}")
                print(f"    科学计数法: {'是' if has_scientific else '否'}")
                
                # 构建订单参数（策略中的格式）
                order_params = {
                    'symbol': self.symbol,
                    'side': 'BUY',
                    'type': 'LIMIT',
                    'quantity': qty_str,
                    'price': price_str,
                    'timeInForce': 'GTC',
                    'reduceOnly': False
                }
                
                print(f"    订单参数: {order_params}")
                
                # 检查是否会导致精度错误
                precision_error = price_decimals > 8 or qty_decimals > 8 or has_scientific
                
                if precision_error:
                    print(f"    ❌ 可能导致精度错误")
                else:
                    print(f"    ✅ 精度检查通过")
                
                # 模拟订单提交（不实际提交）
                print(f"    模拟提交结果: {'精度错误' if precision_error else '成功'}")
            
            print(f"\n✅ MakerChannelEnhanced 策略方法测试完成")
            print()
            
            return {'success': True}
            
        except Exception as e:
            print(f"❌ MakerChannelEnhanced 策略方法失败: {e}")
            print()
            return {'success': False, 'error': str(e)}
    
    def compare_format_methods(self):
        """对比格式化方法的差异"""
        print("4. 对比格式化方法差异")
        print("-" * 40)
        
        # 测试用例
        test_cases = [
            {
                'name': 'PIPPINUSDT 典型案例',
                'price': 0.00000123,
                'qty': 8130081.3,
                'tick_size': 1e-8,
                'step_size': 1.0,
                'min_qty': 1.0
            },
            {
                'name': 'XANUSDT 典型案例',
                'price': 0.11993,
                'qty': 83.0,
                'tick_size': 1e-5,
                'step_size': 1.0,
                'min_qty': 1.0
            },
            {
                'name': '极小价格案例',
                'price': 1e-8,
                'qty': 100000000.0,
                'tick_size': 1e-8,
                'step_size': 1.0,
                'min_qty': 1.0
            }
        ]
        
        for case in test_cases:
            print(f"\n测试案例: {case['name']}")
            print(f"输入: price={case['price']}, qty={case['qty']}")
            print(f"规则: tick_size={case['tick_size']}, step_size={case['step_size']}")
            
            try:
                # 使用策略的格式化方法
                price_str, qty_str = self.strategy._format_order_params(
                    case['price'], case['qty'], case['tick_size'], case['step_size'], case['min_qty']
                )
                
                print(f"输出: price_str='{price_str}', qty_str='{qty_str}'")
                
                # 验证精度
                price_decimals = len(price_str.split('.')[1]) if '.' in price_str else 0
                qty_decimals = len(qty_str.split('.')[1]) if '.' in qty_str else 0
                has_scientific = 'e' in price_str.lower() or 'e' in qty_str.lower()
                
                print(f"精度: price_decimals={price_decimals}, qty_decimals={qty_decimals}")
                print(f"科学计数法: {has_scientific}")
                
                if price_decimals > 8 or qty_decimals > 8 or has_scientific:
                    print("❌ 可能导致精度错误")
                else:
                    print("✅ 精度检查通过")
                    
            except Exception as e:
                print(f"❌ 格式化失败: {e}")
        
        print()
    
    def analyze_differences(self):
        """分析差异的根本原因"""
        print("5. 差异根因分析")
        print("-" * 40)
        
        print("关键差异点:")
        print("1. 订单类型:")
        print("   - auto_trading_test.py: MARKET 订单")
        print("   - MakerChannelEnhanced: LIMIT 订单")
        print()
        
        print("2. 重试机制:")
        print("   - auto_trading_test.py: 无重试，一次性计算")
        print("   - MakerChannelEnhanced: 多次降档重试 (100%→75%→50%→25%)")
        print()
        
        print("3. 数量计算:")
        print("   - auto_trading_test.py: 基于固定名义价值计算")
        print("   - MakerChannelEnhanced: 基于配置的 first_nominal 计算")
        print()
        
        print("4. 格式化方法:")
        print("   - 两者使用相同的 _format_order_params 方法")
        print("   - 但调用时机和参数可能不同")
        print()
        
        print("5. 可能的问题原因:")
        print("   a) 降档重试过程中数量变得过小或过大")
        print("   b) LIMIT 订单对精度要求更严格")
        print("   c) 不同的计算路径导致浮点数精度累积误差")
        print("   d) 策略中的其他逻辑影响了参数计算")
        print()
    
    def run_analysis(self):
        """运行完整分析"""
        if not self.get_trading_rules():
            return
        
        auto_result = self.test_auto_trading_approach()
        strategy_result = self.test_strategy_approach()
        
        self.compare_format_methods()
        self.analyze_differences()
        
        print("6. 总结和建议")
        print("-" * 40)
        
        if auto_result.get('success') and strategy_result.get('success'):
            print("✅ 两种方法都通过了基本测试")
            print("问题可能出现在:")
            print("- 实际运行时的数据差异")
            print("- 网络延迟导致的价格变化")
            print("- 策略中的其他逻辑干扰")
        else:
            print("❌ 发现明显差异:")
            if not auto_result.get('success'):
                print(f"- auto_trading_test.py 失败: {auto_result.get('error')}")
            if not strategy_result.get('success'):
                print(f"- MakerChannelEnhanced 失败: {strategy_result.get('error')}")
        
        print("\n建议的修复方案:")
        print("1. 在策略中添加更详细的日志记录")
        print("2. 在格式化前后都进行精度验证")
        print("3. 考虑统一使用 MARKET 订单进行测试")
        print("4. 添加格式化结果的二次验证")
        print("5. 在重试循环中添加精度检查")

def main():
    analyzer = TradingDifferenceAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()

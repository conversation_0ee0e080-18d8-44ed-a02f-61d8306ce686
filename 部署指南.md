# 增强版交易策略 - 部署指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖包
pip install pandas numpy requests pyyaml psutil

# 克隆项目
git clone <repository-url>
cd allmac
```

### 2. 配置API密钥
编辑 `config/config.yaml` 文件：
```yaml
api_key: "your_binance_api_key"
api_secret: "your_binance_api_secret"
base_url: "https://fapi.binance.com"

# 策略参数
first_nominal: 100      # 首单名义金额(USDT)
max_add: 3               # 最大加仓次数
add_ratio: [1.5, 2.0, 999] # 加仓倍数
max_rate: 10             # API调用频率限制(次/秒)
trade_rate: 5            # 交易频率限制(次/秒)
```

### 3. 网络配置说明

#### 🔒 强制代理策略
- **本地网络 (192.168.x.x)**: 强制使用代理模式
- **马来西亚服务器 (*************)**: 强制使用直连模式
- **其他公网IP**: 强制使用代理模式

#### 代理服务器配置
```python
# 默认代理设置 (可修改)
proxy_url = "http://127.0.0.1:7897"
```

### 4. 运行策略

#### 方式一：直接运行增强版
```bash
python main_enhanced.py
```

#### 方式二：测试验证
```bash
python test_enhanced_strategy.py
```

## 📊 功能特性

### ✅ 已实现功能
- **智能缓存系统**: 四级缓存，API调用减少90%
- **双引擎币种加载**: 00:10冷启 + 实时补票
- **3mTick合成**: 实时K线数据聚合
- **增强评分系统**: 0-10分综合评分
- **统一风险管理**: 四重风控机制
- **强制代理配置**: 根据IP自动切换模式

### 🔧 核心配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `first_nominal` | 首单名义金额 | 100 USDT |
| `max_add` | 最大加仓次数 | 3次 |
| `add_ratio` | 加仓倍数 | [1.5, 2.0, 999] |
| `max_rate` | API调用限制 | 10次/秒 |
| `trade_rate` | 交易频率限制 | 5次/秒 |
| `cache_ttl` | 缓存过期时间 | 300秒 |

## 🛠 故障排除

### 常见问题

#### 1. API连接失败
```bash
# 检查网络连接
ping fapi.binance.com

# 检查代理设置
curl -x http://127.0.0.1:7897 https://fapi.binance.com/fapi/v1/ping
```

#### 2. 内存使用过高
```python
# 调整缓存参数
cache_ttl: 180      # 减少缓存时间
cache_max: 500      # 减少缓存数量
mem_max: 0.75       # 降低内存阈值
```

#### 3. 策略不执行
```bash
# 检查日志文件
tail -f logs/strategy.log

# 验证配置
python -c "import yaml; print(yaml.safe_load(open('config/config.yaml')))"
```

### 性能优化建议

#### 硬件要求
- **内存**: 最低4GB，推荐8GB
- **CPU**: 双核以上
- **网络**: 稳定宽带连接

#### 软件优化
```yaml
# 优化配置示例
cache_ttl: 180      # 3分钟缓存
cache_max: 800      # 最大缓存数量
max_rate: 8         # 降低API频率
```

## 📈 监控与日志

### 日志配置
策略运行日志保存在 `logs/` 目录：
- `strategy.log`: 策略运行日志
- `trade.log`: 交易记录日志
- `error.log`: 错误日志

### 性能监控
```python
# 实时监控内存使用
import psutil
memory_percent = psutil.Process().memory_percent()
print(f"内存使用率: {memory_percent:.1f}%")
```

## 🔄 更新与维护

### 版本更新
```bash
# 拉取最新代码
git pull origin main

# 重新安装依赖
pip install -r requirements.txt

# 运行测试
python test_enhanced_strategy.py
```

### 数据备份
```bash
# 备份配置和日志
tar -czf backup_$(date +%Y%m%d).tar.gz config/ logs/
```

## 📞 技术支持

### 联系方式
- **项目文档**: `README_增强版策略.md`
- **测试报告**: `test_enhanced_strategy.py`
- **问题反馈**: 查看 `logs/error.log`

### 紧急处理
如遇紧急情况，立即停止策略：
```bash
# 强制停止策略
pkill -f "python main_enhanced.py"

# 检查剩余进程
ps aux | grep python
```

---
## 🎯 部署完成检查清单

- [ ] API密钥配置正确
- [ ] 网络连接正常
- [ ] 代理设置正确
- [ ] 依赖包安装完成
- [ ] 配置文件验证通过
- [ ] 测试用例全部通过
- [ ] 日志目录权限正确

**部署状态**: ✅ 已完成  
**测试状态**: ✅ 100%通过  
**运行状态**: ✅ 可立即启动  

*增强版交易策略已准备就绪，可投入模拟环境运行！*
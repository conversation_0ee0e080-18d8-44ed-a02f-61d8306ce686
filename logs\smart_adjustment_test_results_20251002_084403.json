{"test_time": "2025-10-02T08:44:03.637245", "total_tests": 5, "passed_tests": 5, "success_rate": 100.0, "test_results": [{"case_name": "正常情况 - 余额充足", "symbol": "BTCUSDT", "expected": "no_adjustment", "result": {"success": true, "adjusted": false, "original_notional": 50.0, "balance": 100.0}, "passed": true}, {"case_name": "智能调整 - 余额不足但>=6U", "symbol": "ETHUSDT", "expected": "adjusted", "result": {"success": true, "adjusted": true, "original_notional": 30.0, "final_notional": 18.0, "original_qty": 0.01, "adjusted_qty": 0.006, "balance": 20.0}, "passed": true}, {"case_name": "余额不足 - 小于6U", "symbol": "ADAUSDT", "expected": "insufficient_balance", "result": {"success": false, "error": "insufficient_balance", "balance": 5.0}, "passed": true}, {"case_name": "边界情况 - 余额刚好6U", "symbol": "DOTUSDT", "expected": "adjusted", "result": {"success": true, "adjusted": true, "original_notional": 10.0, "final_notional": 5.0, "original_qty": 1.0, "adjusted_qty": 0.5, "balance": 6.0}, "passed": true}, {"case_name": "调整后仍不足5U", "symbol": "LINKUSDT", "expected": "insufficient_balance", "result": {"success": false, "error": "insufficient_balance", "balance": 5.8}, "passed": true}]}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户余额测试脚本
使用现有的HTTP客户端测试账户API访问能力
"""

import json
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('account_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class AccountTester:
    def __init__(self):
        self.config = self.load_config()
        self.http_client = None
        
    def load_config(self):
        """加载配置文件"""
        try:
            # 首先尝试加载YAML配置文件
            import yaml
            with open('config/config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config
        except Exception as e:
            logger.warning(f"加载YAML配置文件失败: {e}")
            
        try:
            # 如果YAML失败，尝试JSON配置文件
            with open('config/config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config
        except Exception as e:
            logger.error(f"加载JSON配置文件失败: {e}")
            return None
    
    def initialize_http_client(self):
        """初始化HTTP客户端"""
        if not self.config:
            logger.error("配置文件未加载，无法初始化HTTP客户端")
            return False
            
        try:
            self.http_client = HttpClient(
                api_key=self.config.get('api_key', ''),
                api_secret=self.config.get('api_secret', ''),
                base_url=self.config.get('base_url', 'https://fapi.binance.com'),
                verify_ssl=False
            )
            logger.info("HTTP客户端初始化成功")
            return True
        except Exception as e:
            logger.error(f"HTTP客户端初始化失败: {e}")
            return False
    
    def test_server_time(self):
        """测试服务器时间同步"""
        logger.info("=" * 60)
        logger.info("1. 测试服务器时间同步")
        logger.info("=" * 60)
        
        try:
            # 使用HTTP客户端的同步时间方法
            success = self.http_client._sync_server_time()
            if success:
                logger.info("✓ 服务器时间同步成功")
                logger.info(f"  时间偏移: {self.http_client.time_offset}ms")
                return True
            else:
                logger.error("✗ 服务器时间同步失败")
                return False
        except Exception as e:
            logger.error(f"✗ 服务器时间同步异常: {e}")
            return False
    
    def test_account_balance(self):
        """测试账户余额获取"""
        logger.info("=" * 60)
        logger.info("2. 测试账户余额获取")
        logger.info("=" * 60)
        
        try:
            # 构造账户信息请求
            endpoint = "/fapi/v2/account"
            params = {}
            
            response = self.http_client.get(endpoint, params)
            
            if response and 'totalWalletBalance' in response:
                total_balance = float(response['totalWalletBalance'])
                available_balance = float(response['availableBalance'])
                
                logger.info("✓ 账户余额获取成功")
                logger.info(f"  总余额: {total_balance:.4f} USDT")
                logger.info(f"  可用余额: {available_balance:.4f} USDT")
                
                # 显示持仓信息
                positions = response.get('positions', [])
                active_positions = [pos for pos in positions if float(pos['positionAmt']) != 0]
                
                if active_positions:
                    logger.info(f"  活跃持仓: {len(active_positions)} 个")
                    for pos in active_positions[:5]:  # 只显示前5个
                        symbol = pos['symbol']
                        size = float(pos['positionAmt'])
                        pnl = float(pos['unrealizedPnl'])
                        logger.info(f"    {symbol}: {size:+.4f}, PnL: {pnl:+.4f} USDT")
                else:
                    logger.info("  无活跃持仓")
                
                return True
            else:
                logger.error("✗ 账户余额获取失败")
                logger.error(f"  响应内容: {response}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 账户余额获取异常: {e}")
            return False
    
    def test_open_orders(self):
        """测试获取当前委托"""
        logger.info("=" * 60)
        logger.info("3. 测试获取当前委托")
        logger.info("=" * 60)
        
        try:
            endpoint = "/fapi/v1/openOrders"
            params = {}
            
            response = self.http_client.get(endpoint, params)
            
            if isinstance(response, list):
                logger.info(f"✓ 当前委托获取成功，共 {len(response)} 个订单")
                
                if response:
                    logger.info("  活跃订单:")
                    for order in response[:5]:  # 只显示前5个
                        symbol = order['symbol']
                        side = order['side']
                        order_type = order['type']
                        price = order['price']
                        qty = order['origQty']
                        logger.info(f"    {symbol} {side} {order_type}: {qty} @ {price}")
                else:
                    logger.info("  无活跃订单")
                
                return True
            else:
                logger.error("✗ 当前委托获取失败")
                logger.error(f"  响应内容: {response}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 当前委托获取异常: {e}")
            return False
    
    def test_trading_status(self):
        """测试交易状态"""
        logger.info("=" * 60)
        logger.info("4. 测试交易状态")
        logger.info("=" * 60)
        
        try:
            endpoint = "/fapi/v1/apiTradingStatus"
            params = {}
            
            response = self.http_client.get(endpoint, params)
            
            if response and 'status' in response:
                status = response['status']
                logger.info(f"✓ 交易状态获取成功")
                logger.info(f"  API状态: {status}")
                
                # 检查是否有交易限制
                if 'indicators' in response:
                    indicators = response['indicators']
                    for key, value in indicators.items():
                        logger.info(f"  {key}: {value}")
                
                return True
            else:
                logger.error("✗ 交易状态获取失败")
                logger.error(f"  响应内容: {response}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 交易状态获取异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始账户API测试...")
        
        if not self.config:
            logger.error("配置文件加载失败，无法进行测试")
            return
        
        api_key = self.config.get('api_key', '')
        if not api_key:
            logger.error("未找到API密钥配置，无法进行账户API测试")
            return
        
        logger.info(f"API Key: {api_key[:8]}...{api_key[-8:] if len(api_key) > 16 else api_key}")
        
        # 初始化HTTP客户端
        if not self.initialize_http_client():
            return
        
        # 执行测试
        results = {}
        results['server_time'] = self.test_server_time()
        results['account_balance'] = self.test_account_balance()
        results['open_orders'] = self.test_open_orders()
        results['trading_status'] = self.test_trading_status()
        
        # 输出测试结果
        logger.info("=" * 60)
        logger.info("账户API测试结果汇总")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{test_name:20}: {status}")
        
        # 分析结果
        success_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        
        if success_count == total_count:
            logger.info("\n✅ 所有账户API测试通过，代理可以正常访问账户信息")
        elif success_count > 0:
            logger.info(f"\n⚠ 部分账户API测试通过 ({success_count}/{total_count})")
            logger.info("建议检查失败的API调用")
        else:
            logger.info("\n❌ 所有账户API测试失败")
            logger.info("可能的原因:")
            logger.info("- API密钥配置错误")
            logger.info("- 代理无法访问币安API")
            logger.info("- 网络连接问题")
            logger.info("- API权限不足")

if __name__ == "__main__":
    tester = AccountTester()
    tester.run_all_tests()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试_format_order_params函数的输出
"""

import decimal as dec

def get_decimal_places(value):
    """计算允许的最大小数位数"""
    if value == 0:
        return 0
    str_val = f"{value:.20f}".rstrip('0')
    if '.' in str_val:
        return len(str_val.split('.')[-1])
    return 0

def _format_order_params(price, qty, tick_size, step_size, min_qty):
    """安全的下单参数格式化，避免精度错误（复制自策略代码）"""
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        tick_decimals = get_decimal_places(tick_size)
        # 限制小数位数，避免精度错误
        price_str = f"{float(price_dec):.{tick_decimals}f}".rstrip('0').rstrip('.')
        if not price_str or price_str == '':
            price_str = f"{float(price_dec):.{max(1, tick_decimals)}f}"
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
        step_decimals = get_decimal_places(step_size)
        # 限制小数位数，避免精度错误
        qty_str = f"{float(qty_dec):.{step_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str or qty_str == '':
            qty_str = f"{float(qty_dec):.{max(1, step_decimals)}f}"
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def test_format_function():
    """测试格式化函数"""
    print("=== 测试_format_order_params函数 ===")
    
    # ICNTUSDT交易规则
    tick_size = 0.0001
    step_size = 1.0
    min_qty = 1.0
    
    # 测试用例
    test_cases = [
        (0.2412, 41.0),
        (0.24127964, 412.8819157720892),
        (0.2412, 30.75),  # 降档75%
        (0.2412, 20.5),   # 降档50%
        (0.2412, 10.25),  # 降档25%
    ]
    
    for i, (price, qty) in enumerate(test_cases):
        print(f"\n--- 测试用例 {i+1} ---")
        print(f"输入: price={price}, qty={qty}")
        
        price_str, qty_str = _format_order_params(price, qty, tick_size, step_size, min_qty)
        
        print(f"输出: price_str='{price_str}', qty_str='{qty_str}'")
        print(f"类型: price_str={type(price_str)}, qty_str={type(qty_str)}")
        print(f"长度: price_str={len(price_str)}, qty_str={len(qty_str)}")
        
        # 检查是否包含科学计数法
        if 'e' in price_str.lower():
            print(f"⚠️  价格包含科学计数法: {price_str}")
        if 'e' in qty_str.lower():
            print(f"⚠️  数量包含科学计数法: {qty_str}")
        
        # 检查是否为空
        if not price_str or price_str == '':
            print(f"❌ 价格为空")
        if not qty_str or qty_str == '':
            print(f"❌ 数量为空")
        
        # 检查是否能转换回数值
        try:
            float(price_str)
            float(qty_str)
            print(f"✅ 参数可正常转换为数值")
        except ValueError as e:
            print(f"❌ 参数转换失败: {e}")

if __name__ == '__main__':
    test_format_function()
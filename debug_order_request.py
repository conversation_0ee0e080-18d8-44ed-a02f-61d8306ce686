#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际发送给API的下单请求
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient
import yaml
import json

def load_config():
    """加载配置"""
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

class DebugHttpClient(HttpClient):
    """调试版HTTP客户端，记录所有请求"""
    
    def post(self, endpoint: str, data: dict) -> dict:
        """重写post方法，记录请求详情"""
        print(f"\n=== 发送POST请求 ===")
        print(f"端点: {endpoint}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查参数格式
        if 'quantity' in data:
            qty = data['quantity']
            print(f"数量参数详情:")
            print(f"  值: {qty}")
            print(f"  类型: {type(qty)}")
            print(f"  长度: {len(str(qty))}")
            print(f"  包含科学计数法: {'e' in str(qty).lower()}")
        
        if 'price' in data:
            price = data['price']
            print(f"价格参数详情:")
            print(f"  值: {price}")
            print(f"  类型: {type(price)}")
            print(f"  长度: {len(str(price))}")
            print(f"  包含科学计数法: {'e' in str(price).lower()}")
        
        if 'stopPrice' in data:
            stop_price = data['stopPrice']
            print(f"止损价格参数详情:")
            print(f"  值: {stop_price}")
            print(f"  类型: {type(stop_price)}")
            print(f"  长度: {len(str(stop_price))}")
            print(f"  包含科学计数法: {'e' in str(stop_price).lower()}")
        
        # 调用原始方法
        result = super().post(endpoint, data)
        
        print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False) if result else 'None'}")
        print("=" * 50)
        
        return result

def main():
    """主函数"""
    print("=== 调试实际下单请求 ===")
    
    # 加载配置
    config = load_config()
    
    # 创建调试HTTP客户端
    http = DebugHttpClient(
        api_key=config['api_key'],
        api_secret=config['api_secret'],
        base_url=config.get('base_url', 'https://fapi.binance.com')
    )
    
    symbol = 'ICNTUSDT'
    
    # 获取交易规则
    print(f"获取 {symbol} 交易规则...")
    exchange_info = http.get('/fapi/v1/exchangeInfo')
    if not exchange_info or 'symbols' not in exchange_info:
        print("❌ 获取交易规则失败")
        return
    
    symbol_info = None
    for s in exchange_info['symbols']:
        if s['symbol'] == symbol:
            symbol_info = s
            break
    
    if not symbol_info:
        print(f"❌ 未找到 {symbol} 的交易规则")
        return
    
    # 解析过滤器
    filters = symbol_info.get('filters', [])
    tick_size = None
    step_size = None
    min_qty = None
    
    for f in filters:
        ft = f.get('filterType')
        if ft == 'PRICE_FILTER':
            tick_size = float(f.get('tickSize', 0) or 0)
        elif ft == 'LOT_SIZE':
            step_size = float(f.get('stepSize', 0) or 0)
            min_qty = float(f.get('minQty', 0) or 0)
    
    print(f"交易规则: tickSize={tick_size}, stepSize={step_size}, minQty={min_qty}")
    
    # 测试下单（使用策略中的实际参数）
    print(f"\n测试下单...")
    
    # 模拟策略中可能出现的参数
    test_cases = [
        # 正常参数
        {'quantity': '41.0', 'price': '0.2412'},
        # 可能有问题的参数
        {'quantity': '41', 'price': '0.2412'},
        {'quantity': '41.0000', 'price': '0.2412000'},
        {'quantity': '4.1e1', 'price': '2.412e-1'},  # 科学计数法
        {'quantity': 41.0, 'price': 0.2412},  # 数值类型
    ]
    
    for i, params in enumerate(test_cases):
        print(f"\n--- 测试用例 {i+1} ---")
        order_data = {
            'symbol': symbol,
            'side': 'BUY',
            'type': 'LIMIT',
            'quantity': params['quantity'],
            'price': params['price'],
            'timeInForce': 'GTC',
            'reduceOnly': False
        }
        
        # 发送请求（会被调试客户端拦截）
        result = http.post('/fapi/v1/order', order_data)
        
        # 分析结果
        if result and isinstance(result, dict) and result.get('code') == -1111:
            print(f"❌ 精度错误: {result.get('msg')}")
        elif result and result.get('orderId'):
            print(f"✅ 下单成功: {result.get('orderId')}")
        else:
            print(f"⚠️  其他结果: {result}")

if __name__ == '__main__':
    main()
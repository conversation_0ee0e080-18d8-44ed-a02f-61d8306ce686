#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PIPPINUSDT的精度问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient
import yaml
import decimal as dec
import requests

def load_config():
    """加载配置"""
    try:
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except:
        return None

def get_pippinusdt_rules():
    """获取PIPPINUSDT的交易规则"""
    print("=== 获取PIPPINUSDT交易规则 ===")
    
    try:
        # 直接获取交易规则
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        params = {"symbol": "PIPPINUSDT"}
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if 'symbols' not in data or len(data['symbols']) == 0:
            print("❌ 未找到PIPPINUSDT交易对")
            return None
        
        symbol_info = data['symbols'][0]
        print(f"交易对: {symbol_info['symbol']}")
        print(f"状态: {symbol_info['status']}")
        print(f"基础资产: {symbol_info['baseAsset']}")
        print(f"报价资产: {symbol_info['quoteAsset']}")
        print(f"基础资产精度: {symbol_info['baseAssetPrecision']}")
        print(f"报价资产精度: {symbol_info['quotePrecision']}")
        print(f"数量精度: {symbol_info['quantityPrecision']}")
        print(f"价格精度: {symbol_info['pricePrecision']}")
        
        # 解析过滤器
        filters = {}
        for filter_info in symbol_info['filters']:
            filter_type = filter_info.get('filterType')
            
            if filter_type == 'PRICE_FILTER':
                filters['price'] = {
                    'tick_size': float(filter_info['tickSize']),
                    'min_price': float(filter_info['minPrice']),
                    'max_price': float(filter_info['maxPrice'])
                }
            elif filter_type == 'LOT_SIZE':
                filters['lot'] = {
                    'step_size': float(filter_info['stepSize']),
                    'min_qty': float(filter_info['minQty']),
                    'max_qty': float(filter_info['maxQty'])
                }
            elif filter_type == 'MIN_NOTIONAL':
                filters['min_notional'] = float(filter_info['notional'])
        
        print("\n=== 交易规则 ===")
        if 'price' in filters:
            print(f"价格过滤器:")
            print(f"  tick_size: {filters['price']['tick_size']}")
            print(f"  min_price: {filters['price']['min_price']}")
            print(f"  max_price: {filters['price']['max_price']}")
        
        if 'lot' in filters:
            print(f"数量过滤器:")
            print(f"  step_size: {filters['lot']['step_size']}")
            print(f"  min_qty: {filters['lot']['min_qty']}")
            print(f"  max_qty: {filters['lot']['max_qty']}")
        
        if 'min_notional' in filters:
            print(f"最小名义价值: {filters['min_notional']}")
        
        return filters
        
    except Exception as e:
        print(f"❌ 获取交易规则失败: {e}")
        return None

def analyze_precision_issue(qty, price, rules):
    """分析精度问题"""
    print(f"\n=== 分析精度问题 ===")
    print(f"原始参数: qty={qty}, price={price}")
    
    if not rules:
        print("❌ 没有交易规则")
        return
    
    # 提取规则
    tick_size = rules.get('price', {}).get('tick_size', 0.00001)
    step_size = rules.get('lot', {}).get('step_size', 0.001)
    min_qty = rules.get('lot', {}).get('min_qty', 0.001)
    min_notional = rules.get('min_notional', 5.0)
    
    print(f"交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 模拟精度处理
    rounded_price = round(price / tick_size) * tick_size
    rounded_qty = round(qty / step_size) * step_size
    rounded_qty = max(rounded_qty, min_qty)
    
    # 确保名义价值
    notional = rounded_price * rounded_qty
    if notional < min_notional:
        needed_qty = (min_notional / rounded_price)
        rounded_qty = round(needed_qty / step_size) * step_size
        rounded_qty = max(rounded_qty, min_qty)
    
    print(f"\n精度处理后:")
    print(f"  rounded_price: {rounded_price}")
    print(f"  rounded_qty: {rounded_qty}")
    print(f"  notional: {rounded_price * rounded_qty}")
    
    # 格式化为字符串（关键步骤）
    dec.getcontext().prec = 18
    
    # 价格格式化
    price_dec = (dec.Decimal(str(rounded_price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
    price_str = format(price_dec.normalize(), 'f')
    
    # 数量格式化
    qty_dec = (dec.Decimal(str(rounded_qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
    qty_dec = max(qty_dec, dec.Decimal(str(min_qty)))
    qty_str = format(qty_dec.normalize(), 'f')
    
    print(f"\n格式化结果:")
    print(f"  price_str: '{price_str}'")
    print(f"  qty_str: '{qty_str}'")
    
    # 检查精度
    def count_decimals(s):
        if '.' not in s:
            return 0
        return len(s.split('.')[1])
    
    price_decimals = count_decimals(price_str)
    qty_decimals = count_decimals(qty_str)
    tick_decimals = count_decimals(str(tick_size))
    step_decimals = count_decimals(str(step_size))
    
    print(f"\n精度检查:")
    print(f"  价格小数位: {price_decimals} (允许: {tick_decimals}) {'✅' if price_decimals <= tick_decimals else '❌'}")
    print(f"  数量小数位: {qty_decimals} (允许: {step_decimals}) {'✅' if qty_decimals <= step_decimals else '❌'}")
    
    # 检查科学计数法
    if 'e' in price_str.lower() or 'e' in qty_str.lower():
        print("❌ 检测到科学计数法，这可能导致精度错误")
    
    return price_str, qty_str

def main():
    """主函数"""
    print("=== PIPPINUSDT 精度问题调试 ===")
    
    # 获取交易规则
    rules = get_pippinusdt_rules()
    if not rules:
        return
    
    # 从日志中提取的问题参数
    problem_qty = 100.0  # 假设的下单数量
    problem_price = 0.1    # 假设的下单价格
    
    # 分析精度问题
    analyze_precision_issue(problem_qty, problem_price, rules)
    
    # 测试降档重试
    print(f"\n=== 测试降档重试 ===")
    for scale in [1.0, 0.75, 0.5, 0.25]:
        print(f"\n--- 降档 {scale*100:.0f}% ---")
        test_qty = problem_qty * scale
        analyze_precision_issue(test_qty, problem_price, rules)

if __name__ == "__main__":
    main()
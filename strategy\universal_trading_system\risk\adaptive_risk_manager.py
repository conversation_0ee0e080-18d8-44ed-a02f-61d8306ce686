"""
自适应风控系统
Adaptive Risk Management System for Universal Trading

功能：
1. 根据币种特性动态调整风控参数
2. 实时监控风险指标和异常情况
3. 自动触发风控措施和仓位管理
4. 提供风险评估和预警机制
"""

import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics

from ..classifier.symbol_classifier import SymbolTier, SymbolClassifier
from ..config.parameter_matrix import ParameterMatrix, TradingParameters

class RiskLevel(Enum):
    """风险等级"""
    LOW = "低风险"
    MEDIUM = "中风险"
    HIGH = "高风险"
    CRITICAL = "极高风险"

class RiskAction(Enum):
    """风控动作"""
    CONTINUE = "继续交易"
    REDUCE_POSITION = "减少仓位"
    STOP_TRADING = "停止交易"
    EMERGENCY_EXIT = "紧急平仓"

@dataclass
class RiskMetrics:
    """风险指标"""
    symbol: str
    tier: SymbolTier
    
    # 交易表现指标
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    consecutive_losses: int = 0
    max_consecutive_losses: int = 0
    
    # 收益指标
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    max_profit: float = 0.0
    max_loss: float = 0.0
    
    # 风险指标
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    
    # 仓位指标
    current_position: float = 0.0
    max_position: float = 0.0
    position_value: float = 0.0
    
    # 时间指标
    last_trade_time: float = 0.0
    last_update_time: float = field(default_factory=time.time)
    
    # 异常指标
    api_error_count: int = 0
    network_error_count: int = 0
    validation_error_count: int = 0

@dataclass
class RiskAlert:
    """风险预警"""
    symbol: str
    level: RiskLevel
    action: RiskAction
    message: str
    details: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)

class AdaptiveRiskManager:
    """自适应风控管理器"""
    
    def __init__(self, classifier: SymbolClassifier = None, 
                 parameter_matrix: ParameterMatrix = None):
        self.classifier = classifier or SymbolClassifier()
        self.parameter_matrix = parameter_matrix or ParameterMatrix()
        self.logger = logging.getLogger(__name__)
        
        # 风险指标存储
        self.risk_metrics: Dict[str, RiskMetrics] = {}
        
        # 风险预警历史
        self.risk_alerts: List[RiskAlert] = []
        self.max_alert_history = 1000
        
        # 全局风控状态
        self.global_risk_state = {
            'total_exposure': 0.0,
            'max_exposure': 10000.0,  # 最大总敞口
            'active_positions': 0,
            'max_positions': 20,      # 最大同时持仓数
            'daily_loss': 0.0,
            'max_daily_loss': 500.0,  # 最大日亏损
            'emergency_stop': False
        }
        
        # 风控规则配置
        self.risk_rules = {
            RiskLevel.LOW: {
                'max_drawdown': 0.02,      # 2%
                'max_consecutive_losses': 3,
                'max_position_ratio': 0.1,  # 10%
                'volatility_threshold': 0.05 # 5%
            },
            RiskLevel.MEDIUM: {
                'max_drawdown': 0.05,      # 5%
                'max_consecutive_losses': 5,
                'max_position_ratio': 0.08, # 8%
                'volatility_threshold': 0.1  # 10%
            },
            RiskLevel.HIGH: {
                'max_drawdown': 0.08,      # 8%
                'max_consecutive_losses': 3,
                'max_position_ratio': 0.05, # 5%
                'volatility_threshold': 0.15 # 15%
            },
            RiskLevel.CRITICAL: {
                'max_drawdown': 0.1,       # 10%
                'max_consecutive_losses': 2,
                'max_position_ratio': 0.02, # 2%
                'volatility_threshold': 0.2  # 20%
            }
        }
    
    def assess_symbol_risk(self, symbol: str) -> Tuple[RiskLevel, List[RiskAlert]]:
        """评估单个币种的风险等级"""
        alerts = []
        
        # 获取或创建风险指标
        if symbol not in self.risk_metrics:
            tier = self.classifier.get_symbol_tier(symbol)
            self.risk_metrics[symbol] = RiskMetrics(symbol=symbol, tier=tier)
        
        metrics = self.risk_metrics[symbol]
        
        # 计算风险分数
        risk_score = self._calculate_risk_score(metrics)
        
        # 确定风险等级
        if risk_score >= 80:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 60:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 40:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW
        
        # 生成具体的风险预警
        alerts.extend(self._generate_risk_alerts(symbol, metrics, risk_level))
        
        self.logger.info(f"风险评估: {symbol} -> {risk_level.value} (分数: {risk_score})")
        
        return risk_level, alerts
    
    def _calculate_risk_score(self, metrics: RiskMetrics) -> float:
        """计算风险分数 (0-100)"""
        score = 0.0
        
        # 连续亏损风险 (0-25分)
        if metrics.total_trades > 0:
            consecutive_loss_ratio = metrics.consecutive_losses / max(metrics.total_trades, 1)
            score += min(consecutive_loss_ratio * 100, 25)
        
        # 回撤风险 (0-25分)
        if metrics.max_drawdown > 0:
            drawdown_score = (metrics.current_drawdown / metrics.max_drawdown) * 25
            score += min(drawdown_score, 25)
        
        # 波动率风险 (0-20分)
        volatility_score = min(metrics.volatility * 100, 20)
        score += volatility_score
        
        # 仓位风险 (0-15分)
        parameters = self.parameter_matrix.get_parameters(metrics.symbol, metrics.tier)
        if parameters.max_position_usdt > 0:
            position_ratio = metrics.position_value / parameters.max_position_usdt
            position_score = min(position_ratio * 15, 15)
            score += position_score
        
        # 错误率风险 (0-15分)
        total_errors = (metrics.api_error_count + 
                       metrics.network_error_count + 
                       metrics.validation_error_count)
        if metrics.total_trades > 0:
            error_ratio = total_errors / max(metrics.total_trades, 1)
            error_score = min(error_ratio * 100, 15)
            score += error_score
        
        return min(score, 100.0)
    
    def _generate_risk_alerts(self, symbol: str, metrics: RiskMetrics, 
                            risk_level: RiskLevel) -> List[RiskAlert]:
        """生成风险预警"""
        alerts = []
        rules = self.risk_rules[risk_level]
        
        # 检查连续亏损
        if metrics.consecutive_losses >= rules['max_consecutive_losses']:
            action = RiskAction.STOP_TRADING if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL] else RiskAction.REDUCE_POSITION
            alerts.append(RiskAlert(
                symbol=symbol,
                level=risk_level,
                action=action,
                message=f"连续亏损过多: {metrics.consecutive_losses}次",
                details={
                    'consecutive_losses': metrics.consecutive_losses,
                    'max_allowed': rules['max_consecutive_losses']
                }
            ))
        
        # 检查回撤
        if metrics.current_drawdown > rules['max_drawdown']:
            action = RiskAction.EMERGENCY_EXIT if risk_level == RiskLevel.CRITICAL else RiskAction.REDUCE_POSITION
            alerts.append(RiskAlert(
                symbol=symbol,
                level=risk_level,
                action=action,
                message=f"回撤过大: {metrics.current_drawdown:.2%}",
                details={
                    'current_drawdown': metrics.current_drawdown,
                    'max_allowed': rules['max_drawdown']
                }
            ))
        
        # 检查波动率
        if metrics.volatility > rules['volatility_threshold']:
            alerts.append(RiskAlert(
                symbol=symbol,
                level=risk_level,
                action=RiskAction.REDUCE_POSITION,
                message=f"波动率过高: {metrics.volatility:.2%}",
                details={
                    'current_volatility': metrics.volatility,
                    'max_allowed': rules['volatility_threshold']
                }
            ))
        
        # 检查仓位比例
        parameters = self.parameter_matrix.get_parameters(symbol, metrics.tier)
        if parameters.max_position_usdt > 0:
            position_ratio = metrics.position_value / parameters.max_position_usdt
            if position_ratio > rules['max_position_ratio']:
                alerts.append(RiskAlert(
                    symbol=symbol,
                    level=risk_level,
                    action=RiskAction.REDUCE_POSITION,
                    message=f"仓位比例过高: {position_ratio:.2%}",
                    details={
                        'position_ratio': position_ratio,
                        'max_allowed': rules['max_position_ratio']
                    }
                ))
        
        return alerts
    
    def update_trade_result(self, symbol: str, pnl: float, is_win: bool):
        """更新交易结果"""
        if symbol not in self.risk_metrics:
            tier = self.classifier.get_symbol_tier(symbol)
            self.risk_metrics[symbol] = RiskMetrics(symbol=symbol, tier=tier)
        
        metrics = self.risk_metrics[symbol]
        
        # 更新交易统计
        metrics.total_trades += 1
        metrics.total_pnl += pnl
        metrics.realized_pnl += pnl
        metrics.last_trade_time = time.time()
        
        if is_win:
            metrics.winning_trades += 1
            metrics.consecutive_losses = 0
            metrics.max_profit = max(metrics.max_profit, pnl)
        else:
            metrics.losing_trades += 1
            metrics.consecutive_losses += 1
            metrics.max_consecutive_losses = max(metrics.max_consecutive_losses, metrics.consecutive_losses)
            metrics.max_loss = min(metrics.max_loss, pnl)
        
        # 更新回撤
        if metrics.total_pnl > 0:
            peak_pnl = max(metrics.total_pnl, metrics.max_profit)
            current_drawdown = (peak_pnl - metrics.total_pnl) / peak_pnl
            metrics.current_drawdown = current_drawdown
            metrics.max_drawdown = max(metrics.max_drawdown, current_drawdown)
        
        # 更新全局状态
        self.global_risk_state['daily_loss'] += pnl if pnl < 0 else 0
        
        metrics.last_update_time = time.time()
        
        self.logger.info(f"交易结果更新: {symbol} PnL: {pnl:.2f}, 连续亏损: {metrics.consecutive_losses}")
    
    def update_position(self, symbol: str, position_size: float, position_value: float):
        """更新仓位信息"""
        if symbol not in self.risk_metrics:
            tier = self.classifier.get_symbol_tier(symbol)
            self.risk_metrics[symbol] = RiskMetrics(symbol=symbol, tier=tier)
        
        metrics = self.risk_metrics[symbol]
        
        # 更新仓位
        old_position = metrics.current_position
        metrics.current_position = position_size
        metrics.position_value = position_value
        metrics.max_position = max(metrics.max_position, abs(position_size))
        
        # 更新全局敞口
        self.global_risk_state['total_exposure'] += abs(position_value) - abs(old_position * position_value / position_size if position_size != 0 else 0)
        
        # 更新活跃仓位数
        active_positions = sum(1 for m in self.risk_metrics.values() if abs(m.current_position) > 0)
        self.global_risk_state['active_positions'] = active_positions
        
        metrics.last_update_time = time.time()
    
    def update_error_count(self, symbol: str, error_type: str):
        """更新错误计数"""
        if symbol not in self.risk_metrics:
            tier = self.classifier.get_symbol_tier(symbol)
            self.risk_metrics[symbol] = RiskMetrics(symbol=symbol, tier=tier)
        
        metrics = self.risk_metrics[symbol]
        
        if error_type == 'api':
            metrics.api_error_count += 1
        elif error_type == 'network':
            metrics.network_error_count += 1
        elif error_type == 'validation':
            metrics.validation_error_count += 1
        
        metrics.last_update_time = time.time()
    
    def check_global_risk(self) -> Tuple[RiskLevel, List[RiskAlert]]:
        """检查全局风险状态"""
        alerts = []
        risk_level = RiskLevel.LOW
        
        # 检查总敞口
        if self.global_risk_state['total_exposure'] > self.global_risk_state['max_exposure']:
            risk_level = RiskLevel.HIGH
            alerts.append(RiskAlert(
                symbol='GLOBAL',
                level=RiskLevel.HIGH,
                action=RiskAction.REDUCE_POSITION,
                message=f"总敞口超限: {self.global_risk_state['total_exposure']:.2f}",
                details={'total_exposure': self.global_risk_state['total_exposure']}
            ))
        
        # 检查活跃仓位数
        if self.global_risk_state['active_positions'] > self.global_risk_state['max_positions']:
            risk_level = max(risk_level, RiskLevel.MEDIUM)
            alerts.append(RiskAlert(
                symbol='GLOBAL',
                level=RiskLevel.MEDIUM,
                action=RiskAction.REDUCE_POSITION,
                message=f"活跃仓位过多: {self.global_risk_state['active_positions']}",
                details={'active_positions': self.global_risk_state['active_positions']}
            ))
        
        # 检查日亏损
        if abs(self.global_risk_state['daily_loss']) > self.global_risk_state['max_daily_loss']:
            risk_level = RiskLevel.CRITICAL
            alerts.append(RiskAlert(
                symbol='GLOBAL',
                level=RiskLevel.CRITICAL,
                action=RiskAction.EMERGENCY_EXIT,
                message=f"日亏损超限: {self.global_risk_state['daily_loss']:.2f}",
                details={'daily_loss': self.global_risk_state['daily_loss']}
            ))
            self.global_risk_state['emergency_stop'] = True
        
        return risk_level, alerts
    
    def should_allow_trade(self, symbol: str) -> Tuple[bool, str]:
        """判断是否允许交易"""
        # 检查紧急停止状态
        if self.global_risk_state['emergency_stop']:
            return False, "全局紧急停止状态"
        
        # 检查全局风险
        global_risk, global_alerts = self.check_global_risk()
        if global_risk == RiskLevel.CRITICAL:
            return False, "全局风险过高"
        
        # 检查币种风险
        symbol_risk, symbol_alerts = self.assess_symbol_risk(symbol)
        
        # 检查是否有停止交易的预警
        for alert in symbol_alerts:
            if alert.action in [RiskAction.STOP_TRADING, RiskAction.EMERGENCY_EXIT]:
                return False, f"币种风险: {alert.message}"
        
        return True, "允许交易"
    
    def get_recommended_position_size(self, symbol: str, base_size: float) -> float:
        """获取推荐仓位大小"""
        # 评估风险
        risk_level, alerts = self.assess_symbol_risk(symbol)
        
        # 根据风险等级调整仓位
        risk_multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.8,
            RiskLevel.HIGH: 0.5,
            RiskLevel.CRITICAL: 0.2
        }
        
        adjusted_size = base_size * risk_multipliers[risk_level]
        
        # 检查是否有减仓预警
        for alert in alerts:
            if alert.action == RiskAction.REDUCE_POSITION:
                adjusted_size *= 0.6  # 进一步减少60%
                break
        
        self.logger.info(f"仓位调整: {symbol} {base_size:.4f} -> {adjusted_size:.4f} (风险: {risk_level.value})")
        
        return adjusted_size
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        total_symbols = len(self.risk_metrics)
        if total_symbols == 0:
            return {'message': '暂无风险数据'}
        
        # 统计各风险等级的币种数量
        risk_distribution = {level.value: 0 for level in RiskLevel}
        total_pnl = 0.0
        total_trades = 0
        winning_trades = 0
        
        for symbol, metrics in self.risk_metrics.items():
            risk_level, _ = self.assess_symbol_risk(symbol)
            risk_distribution[risk_level.value] += 1
            total_pnl += metrics.total_pnl
            total_trades += metrics.total_trades
            winning_trades += metrics.winning_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        return {
            'total_symbols': total_symbols,
            'risk_distribution': risk_distribution,
            'global_state': self.global_risk_state,
            'performance': {
                'total_pnl': total_pnl,
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_pnl_per_trade': total_pnl / total_trades if total_trades > 0 else 0
            },
            'recent_alerts': len([a for a in self.risk_alerts if time.time() - a.timestamp < 3600])  # 最近1小时的预警
        }
    
    def reset_daily_stats(self):
        """重置日统计数据"""
        self.global_risk_state['daily_loss'] = 0.0
        self.global_risk_state['emergency_stop'] = False
        
        # 清理旧的预警记录
        cutoff_time = time.time() - 86400  # 24小时前
        self.risk_alerts = [alert for alert in self.risk_alerts if alert.timestamp > cutoff_time]
        
        self.logger.info("日统计数据已重置")

# 使用示例
if __name__ == "__main__":
    # 初始化风控管理器
    risk_manager = AdaptiveRiskManager()
    
    # 模拟交易结果更新
    risk_manager.update_trade_result('BTCUSDT', 10.5, True)
    risk_manager.update_trade_result('BTCUSDT', -5.2, False)
    risk_manager.update_trade_result('BTCUSDT', -3.8, False)
    
    # 评估风险
    risk_level, alerts = risk_manager.assess_symbol_risk('BTCUSDT')
    print(f"风险等级: {risk_level.value}")
    
    for alert in alerts:
        print(f"预警: {alert.message} -> {alert.action.value}")
    
    # 检查是否允许交易
    can_trade, reason = risk_manager.should_allow_trade('BTCUSDT')
    print(f"允许交易: {can_trade}, 原因: {reason}")
    
    # 获取风险摘要
    summary = risk_manager.get_risk_summary()
    print(f"风险摘要: {summary}")
Stack trace:
Frame         Function      Args
0007FFFFA780  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA780, 0007FFFF9680) msys-2.0.dll+0x2118E
0007FFFFA780  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA780  0002100469F2 (00021028DF99, 0007FFFFA638, 0007FFFFA780, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA780  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA780  00021006A545 (0007FFFFA790, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA790, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD64640000 ntdll.dll
7FFD62EE0000 KERNEL32.DLL
7FFD61AD0000 KERNELBASE.dll
7FFD643C0000 USER32.dll
7FFD622D0000 win32u.dll
7FFD627B0000 GDI32.dll
7FFD62040000 gdi32full.dll
7FFD61E60000 msvcp_win.dll
7FFD62300000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD62930000 advapi32.dll
7FFD62E10000 msvcrt.dll
7FFD640F0000 sechost.dll
7FFD62490000 RPCRT4.dll
7FFD611F0000 CRYPTBASE.DLL
7FFD61FC0000 bcryptPrimitives.dll
7FFD625C0000 IMM32.DLL

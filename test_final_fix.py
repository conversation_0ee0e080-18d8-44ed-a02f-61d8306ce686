#!/usr/bin/env python3
"""
测试最终修复方案
验证PIPPINUSDT精度问题是否彻底解决
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal, ROUND_DOWN, getcontext

def test_final_format_function():
    """测试最终的格式化函数"""
    print("=== 测试最终格式化函数 ===")
    
    # 设置高精度
    getcontext().prec = 50
    
    def format_to_tick_precision(value, tick_size):
        """根据tick_size格式化价格"""
        if not tick_size or tick_size <= 0:
            return str(float(value))
        
        try:
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_tick = Decimal(str(tick_size))
            
            if decimal_value <= 0:
                return str(float(value))
            
            # 计算应该保留的小数位数
            tick_str = f"{tick_size:.20f}".rstrip('0')
            if '.' in tick_str:
                decimal_places = len(tick_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到tick_size的倍数
            steps = (decimal_value / decimal_tick).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 关键修复：如果steps为0但原值大于0，设为1
            if steps == 0 and decimal_value > 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_tick
            
            # 转换为字符串，保持正确的小数位数
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0，但至少保留一位小数
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result += '0'
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"价格格式化失败: {e}")
            return str(float(value))
    
    def format_to_step_precision(value, step_size, min_qty):
        """根据step_size格式化数量"""
        if not step_size or step_size <= 0:
            return str(float(value))
        
        try:
            # 确保不小于最小数量
            actual_value = max(float(value), float(min_qty) if min_qty else 0)
            
            # 转换为Decimal确保精度
            decimal_value = Decimal(str(actual_value))
            decimal_step = Decimal(str(step_size))
            
            # 计算应该保留的小数位数
            step_str = f"{step_size:.20f}".rstrip('0')
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 向下取整到step_size的倍数
            steps = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 确保至少为1步
            if steps == 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_step
            
            # 转换为字符串
            if decimal_places > 0:
                result = f"{float(formatted_value):.{decimal_places}f}"
                # 移除末尾的0
                if '.' in result:
                    result = result.rstrip('0')
                    if result.endswith('.'):
                        result = result[:-1]
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"数量格式化失败: {e}")
            return str(float(value))
    
    # 测试PIPPINUSDT的关键场景
    test_cases = [
        # 这些是导致-1111错误的关键场景
        (0.4, 236.417, 0.00001, 0.001, 0.001, "PIPPINUSDT实际参数"),
        (0.00005, 1000.0, 0.00001, 0.001, 0.001, "极小价格测试"),
        (0.000001, 5000000.0, 0.00001, 0.001, 0.001, "超小价格测试"),
        (0.0000001, 50000000.0, 0.00001, 0.001, 0.001, "极超小价格测试"),
        (0.123456, 100.0, 0.00001, 0.001, 0.001, "中等价格测试"),
    ]
    
    all_passed = True
    
    for price, qty, tick_size, step_size, min_qty, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: price={price}, qty={qty}")
        print(f"规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}")
        
        # 格式化价格和数量
        price_str = format_to_tick_precision(price, tick_size)
        qty_str = format_to_step_precision(qty, step_size, min_qty)
        
        print(f"输出: price_str='{price_str}', qty_str='{qty_str}'")
        
        # 验证结果
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            notional = price_val * qty_val
            
            print(f"验证: price_val={price_val}, qty_val={qty_val}")
            print(f"名义价值: {notional:.6f} USDT")
            
            # 关键检查：价格不能为0
            if price_val <= 0:
                print("❌ 致命错误：价格为0或负数！这会导致-1111错误")
                all_passed = False
            elif qty_val <= 0:
                print("❌ 致命错误：数量为0或负数！这会导致-1111错误")
                all_passed = False
            elif price_str == '0' or price_str == '0.0':
                print("❌ 致命错误：价格字符串为'0'！这会导致-1111错误")
                all_passed = False
            elif qty_str == '0' or qty_str == '0.0':
                print("❌ 致命错误：数量字符串为'0'！这会导致-1111错误")
                all_passed = False
            else:
                print("✅ 关键检查通过：价格和数量都大于0")
                
                # 检查精度
                if '.' in price_str:
                    price_decimals = len(price_str.split('.')[1])
                    tick_decimals = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
                    if price_decimals <= tick_decimals:
                        print(f"✅ 价格精度正确: {price_decimals} <= {tick_decimals}")
                    else:
                        print(f"⚠️  价格精度可能超限: {price_decimals} > {tick_decimals}")
                
                if notional >= 5.0:
                    print("✅ 名义价值满足要求")
                else:
                    print("⚠️  名义价值可能不足")
                    
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            all_passed = False
    
    print(f"\n=== 测试总结 ===")
    if all_passed:
        print("🎉 所有测试通过！修复应该已经生效")
        print("✅ 价格不会再被格式化为0")
        print("✅ 应该不会再出现-1111精度错误")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed

def simulate_binance_order():
    """模拟币安下单验证"""
    print("\n=== 模拟币安下单验证 ===")
    
    # 模拟PIPPINUSDT的实际下单参数
    price = 0.4
    qty = 236.417
    tick_size = 0.00001
    step_size = 0.001
    min_qty = 0.001
    min_notional = 5.0
    
    print(f"模拟下单参数:")
    print(f"  symbol: PIPPINUSDT")
    print(f"  price: {price}")
    print(f"  quantity: {qty}")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 使用修复后的格式化函数
    from decimal import Decimal, ROUND_DOWN, getcontext
    getcontext().prec = 50
    
    def format_to_tick_precision(value, tick_size):
        decimal_value = Decimal(str(value))
        decimal_tick = Decimal(str(tick_size))
        
        if decimal_value <= 0:
            return str(float(value))
        
        tick_str = f"{tick_size:.20f}".rstrip('0')
        if '.' in tick_str:
            decimal_places = len(tick_str.split('.')[1])
        else:
            decimal_places = 0
        
        steps = (decimal_value / decimal_tick).quantize(Decimal('1'), rounding=ROUND_DOWN)
        
        # 关键修复
        if steps == 0 and decimal_value > 0:
            steps = Decimal('1')
        
        formatted_value = steps * decimal_tick
        
        if decimal_places > 0:
            result = f"{float(formatted_value):.{decimal_places}f}"
            if '.' in result:
                result = result.rstrip('0')
                if result.endswith('.'):
                    result += '0'
            return result
        else:
            return str(int(float(formatted_value)))
    
    price_str = format_to_tick_precision(price, tick_size)
    qty_str = format_to_tick_precision(qty, step_size)
    
    print(f"\n格式化后的参数:")
    print(f"  price: '{price_str}'")
    print(f"  quantity: '{qty_str}'")
    
    # 验证
    try:
        price_val = float(price_str)
        qty_val = float(qty_str)
        notional = price_val * qty_val
        
        print(f"\n验证结果:")
        print(f"  price_val: {price_val}")
        print(f"  qty_val: {qty_val}")
        print(f"  notional: {notional}")
        
        # 币安验证规则
        checks = []
        
        # 1. 价格不能为0
        if price_val > 0:
            checks.append("✅ 价格 > 0")
        else:
            checks.append("❌ 价格 <= 0 (会导致-1111)")
        
        # 2. 数量不能为0
        if qty_val > 0:
            checks.append("✅ 数量 > 0")
        else:
            checks.append("❌ 数量 <= 0 (会导致-1111)")
        
        # 3. 数量不能小于最小数量
        if qty_val >= min_qty:
            checks.append("✅ 数量 >= min_qty")
        else:
            checks.append("❌ 数量 < min_qty (会导致-1111)")
        
        # 4. 名义价值不能小于最小值
        if notional >= min_notional:
            checks.append("✅ 名义价值 >= min_notional")
        else:
            checks.append("❌ 名义价值 < min_notional (会导致-1111)")
        
        # 5. 价格精度检查
        if '.' in price_str:
            price_decimals = len(price_str.split('.')[1])
            tick_decimals = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
            if price_decimals <= tick_decimals:
                checks.append("✅ 价格精度正确")
            else:
                checks.append("❌ 价格精度超限 (会导致-1111)")
        else:
            checks.append("✅ 价格精度正确")
        
        print(f"\n币安验证检查:")
        for check in checks:
            print(f"  {check}")
        
        # 最终判断
        if all("✅" in check for check in checks):
            print(f"\n🎉 模拟下单成功！应该不会再出现-1111错误")
            return True
        else:
            print(f"\n❌ 模拟下单失败，仍可能出现-1111错误")
            return False
            
    except Exception as e:
        print(f"❌ 模拟验证失败: {e}")
        return False

if __name__ == '__main__':
    print("开始测试最终修复方案...")
    
    # 测试格式化函数
    format_test_passed = test_final_format_function()
    
    # 模拟币安下单
    order_test_passed = simulate_binance_order()
    
    print(f"\n" + "="*50)
    print(f"最终测试结果:")
    print(f"  格式化函数测试: {'✅ 通过' if format_test_passed else '❌ 失败'}")
    print(f"  模拟下单测试: {'✅ 通过' if order_test_passed else '❌ 失败'}")
    
    if format_test_passed and order_test_passed:
        print(f"\n🎉 修复完全成功！")
        print(f"✅ PIPPINUSDT的-1111精度错误应该已经彻底解决")
        print(f"✅ 可以重新启动策略进行验证")
    else:
        print(f"\n⚠️  修复可能不完整，需要进一步调试")
    
    print("="*50)
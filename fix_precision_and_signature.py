#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复PUMPBTCUSDT下单精度和签名问题的脚本
基于日志分析结果，提供完整的修复方案
"""

import yaml
import time
import hmac
import hashlib
import urllib.parse
import decimal as dec
import requests
from http_client import HttpClient

def get_safe_decimal_places(value):
    """安全地计算小数位数，避免浮点数精度问题"""
    if value == 0:
        return 0
    
    # 使用字符串方式计算，避免浮点数精度问题
    value_str = str(value)
    if 'e' in value_str.lower():
        # 处理科学计数法
        if 'e-' in value_str.lower():
            exp = int(value_str.split('e-')[1])
            return exp
        else:
            return 0
    elif '.' in value_str:
        return len(value_str.split('.')[1])
    else:
        return 0

def fixed_format_order_params(price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化函数"""
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        tick_decimals = get_safe_decimal_places(tick_size)
        # 限制最大小数位数为8位，避免精度错误
        max_decimals = min(tick_decimals, 8)
        price_str = f"{float(price_dec):.{max_decimals}f}".rstrip('0').rstrip('.')
        if not price_str or price_str == '':
            price_str = f"{float(price_dec):.{max(1, max_decimals)}f}"
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
        step_decimals = get_safe_decimal_places(step_size)
        # 限制最大小数位数为8位，避免精度错误
        max_decimals = min(step_decimals, 8)
        qty_str = f"{float(qty_dec):.{max_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str or qty_str == '':
            qty_str = f"{float(qty_dec):.{max(1, max_decimals)}f}"
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def test_pumpbtcusdt_precision():
    """测试PUMPBTCUSDT的精度处理"""
    print("=== PUMPBTCUSDT 精度测试 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 1268.352
    actual_price = 0.100000
    
    print(f"原始参数:")
    print(f"  数量: {actual_qty}")
    print(f"  价格: {actual_price}")
    print(f"  名义价值: {actual_qty * actual_price}")
    
    # 模拟PUMPBTCUSDT的交易规则（新币通常的规则）
    test_cases = [
        {"name": "高精度新币", "tick_size": 0.000001, "step_size": 0.1, "min_qty": 0.1},
        {"name": "中精度新币", "tick_size": 0.00001, "step_size": 1.0, "min_qty": 1.0},
        {"name": "低精度新币", "tick_size": 0.0001, "step_size": 10.0, "min_qty": 10.0},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"交易规则: tickSize={case['tick_size']}, stepSize={case['step_size']}, minQty={case['min_qty']}")
        
        # 使用修复后的格式化函数
        price_str, qty_str = fixed_format_order_params(
            actual_price, actual_qty, 
            case['tick_size'], case['step_size'], case['min_qty']
        )
        
        print(f"格式化结果:")
        print(f"  价格: '{price_str}' (长度: {len(price_str)})")
        print(f"  数量: '{qty_str}' (长度: {len(qty_str)})")
        
        # 检查是否有问题
        issues = []
        if 'e' in price_str.lower():
            issues.append("价格包含科学计数法")
        if 'e' in qty_str.lower():
            issues.append("数量包含科学计数法")
        if len(price_str) > 20:
            issues.append(f"价格字符串过长({len(price_str)})")
        if len(qty_str) > 20:
            issues.append(f"数量字符串过长({len(qty_str)})")
        
        if issues:
            print(f"  ⚠️  发现问题: {', '.join(issues)}")
        else:
            print(f"  ✅ 参数正常")

def test_signature_generation():
    """测试签名生成的稳定性"""
    print("\n=== 签名生成测试 ===")
    
    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 测试参数
    base_params = {
        'symbol': 'PUMPBTCUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'reduceOnly': False,
        'recvWindow': 60000
    }
    
    # 测试不同的数量和价格格式
    test_cases = [
        {'quantity': '1268.352', 'price': '0.1'},
        {'quantity': '1268.4', 'price': '0.1'},
        {'quantity': '1268', 'price': '0.1'},
        {'quantity': '1268.352', 'price': '0.10'},
        {'quantity': '1268.352', 'price': '0.100'},
    ]
    
    api_secret = cfg['api_secret']
    
    for i, case in enumerate(test_cases):
        timestamp = int(time.time() * 1000)
        params = {**base_params, **case, 'timestamp': timestamp}
        
        # 生成签名
        query_string = urllib.parse.urlencode(params)
        signature = hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        print(f"测试 {i+1}: quantity={case['quantity']}, price={case['price']}")
        print(f"  查询字符串长度: {len(query_string)}")
        print(f"  签名: {signature[:16]}...")
        
        # 检查查询字符串是否有异常
        if len(query_string) > 1000:
            print(f"  ⚠️  查询字符串过长: {len(query_string)}")
        if any(c in query_string for c in ['%2B', '%2F', '%3D']):
            print(f"  ⚠️  包含URL编码的特殊字符")

def test_real_api_call():
    """测试实际API调用"""
    print("\n=== 实际API调用测试 ===")
    
    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    http = HttpClient(cfg['api_key'], cfg['api_secret'], cfg.get('base_url', 'https://fapi.binance.com'))
    
    # 首先获取PUMPBTCUSDT的交易规则
    print("获取PUMPBTCUSDT交易规则...")
    exchange_info = http.get('/fapi/v1/exchangeInfo')
    if not exchange_info or 'symbols' not in exchange_info:
        print("❌ 获取交易规则失败")
        return
    
    symbol_info = None
    for s in exchange_info['symbols']:
        if s['symbol'] == 'PUMPBTCUSDT':
            symbol_info = s
            break
    
    if not symbol_info:
        print("❌ 未找到PUMPBTCUSDT的交易规则")
        return
    
    # 解析过滤器
    filters = symbol_info.get('filters', [])
    tick_size = None
    step_size = None
    min_qty = None
    
    for f in filters:
        ft = f.get('filterType')
        if ft == 'PRICE_FILTER':
            tick_size = float(f.get('tickSize', 0) or 0)
        elif ft == 'LOT_SIZE':
            step_size = float(f.get('stepSize', 0) or 0)
            min_qty = float(f.get('minQty', 0) or 0)
    
    print(f"交易规则: tickSize={tick_size}, stepSize={step_size}, minQty={min_qty}")
    
    # 使用修复后的格式化函数
    actual_qty = 1268.352
    actual_price = 0.100000
    
    price_str, qty_str = fixed_format_order_params(
        actual_price, actual_qty, tick_size, step_size, min_qty
    )
    
    print(f"修复后的参数:")
    print(f"  价格: '{price_str}'")
    print(f"  数量: '{qty_str}'")
    
    # 测试下单（使用测试接口）
    test_params = {
        'symbol': 'PUMPBTCUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'quantity': qty_str,
        'price': price_str,
        'timeInForce': 'GTC',
        'reduceOnly': False
    }
    
    print(f"\n测试下单参数: {test_params}")
    result = http.post('/fapi/v1/order/test', test_params)
    
    if result == {}:
        print("✅ 测试下单成功！")
    elif isinstance(result, dict) and 'code' in result:
        code = result.get('code')
        msg = result.get('msg', '')
        if code == -1111:
            print(f"❌ 精度错误仍然存在: {msg}")
        elif code == -1022:
            print(f"❌ 签名错误仍然存在: {msg}")
        else:
            print(f"❌ 其他错误: {code} - {msg}")
    else:
        print(f"⚠️  未知结果: {result}")

def main():
    """主函数"""
    print("🔧 PUMPBTCUSDT 下单问题修复测试")
    print("=" * 50)
    
    try:
        # 1. 测试精度处理
        test_pumpbtcusdt_precision()
        
        # 2. 测试签名生成
        test_signature_generation()
        
        # 3. 测试实际API调用
        test_real_api_call()
        
        print("\n" + "=" * 50)
        print("✅ 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
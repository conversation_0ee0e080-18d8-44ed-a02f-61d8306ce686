#!/usr/bin/env python3
"""
紧急修复：价格被格式化为0的严重bug
"""

from decimal import Decimal, ROUND_DOWN

def debug_format_to_precision():
    """调试format_to_precision函数的问题"""
    print("=== 调试format_to_precision函数 ===")
    
    def format_to_precision_buggy(value, precision_step):
        """有问题的版本（当前代码）"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出 - 这里有问题！
            if decimal_places > 0:
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 去除末尾的0，但保留必要的小数位
                if '.' in formatted:
                    int_part, dec_part = formatted.split('.')
                    dec_part = dec_part.rstrip('0')
                    if dec_part:
                        formatted = f"{int_part}.{dec_part}"
                    else:
                        formatted = int_part  # 这里返回整数部分，可能为0！
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            return str(value)
    
    def format_to_precision_fixed(value, precision_step):
        """修复后的版本"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 确保不会小于一个步长（除非原值就是0）
            if rounded == 0 and decimal_value > 0:
                rounded = decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出
            if decimal_places > 0:
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 智能去除末尾的0，但保留有意义的精度
                if '.' in formatted:
                    # 不要完全去除小数部分，至少保留一位有效数字
                    formatted = formatted.rstrip('0')
                    if formatted.endswith('.'):
                        formatted = formatted[:-1]
                    # 如果格式化后为空或只有小数点，返回原始格式
                    if not formatted or formatted in ['.', '']:
                        formatted = format_str.format(float(rounded))
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            return str(value)
    
    # 测试用例
    test_cases = [
        (0.4, 0.00001, "PIPPINUSDT价格"),
        (0.4, 0.0001, "中等精度价格"),
        (0.4, 0.001, "低精度价格"),
        (0.00005, 0.00001, "极小价格"),
        (1.23456, 0.0001, "正常价格"),
    ]
    
    for value, precision_step, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: value={value}, precision_step={precision_step}")
        
        buggy_result = format_to_precision_buggy(value, precision_step)
        fixed_result = format_to_precision_fixed(value, precision_step)
        
        print(f"有问题版本: '{buggy_result}'")
        print(f"修复版本: '{fixed_result}'")
        
        if buggy_result == '0' and value > 0:
            print(f"❌ 发现bug：价格{value}被格式化为0！")
        else:
            print(f"✅ 正常")

def create_emergency_fix():
    """创建紧急修复代码"""
    print("\n=== 紧急修复代码 ===")
    
    fixed_function = '''
def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
    """紧急修复：防止价格被格式化为0"""
    from decimal import Decimal, ROUND_DOWN
    
    def format_to_precision(value, precision_step):
        """修复后的精度格式化函数"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 关键修复：确保不会小于一个步长（除非原值就是0）
            if rounded == 0 and decimal_value > 0:
                rounded = decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出
            if decimal_places > 0:
                # 使用固定精度格式化
                formatted = f"{float(rounded):.{decimal_places}f}"
                # 智能去除末尾的0，但保留至少一位小数（如果原本有小数）
                if '.' in formatted:
                    formatted = formatted.rstrip('0')
                    if formatted.endswith('.'):
                        # 如果去除0后只剩小数点，保留一位0
                        formatted = formatted + '0'
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            # 如果Decimal处理失败，回退到简单字符串格式化
            return str(value)
    
    try:
        # 格式化价格
        price_str = format_to_precision(price, tick_size)
        
        # 格式化数量，确保不小于最小数量
        if min_qty and float(qty) < float(min_qty):
            qty = min_qty
        qty_str = format_to_precision(qty, step_size)
        
        # 最终验证和清理
        if not price_str or price_str.strip() in ['', '.', '0.']:
            price_str = str(float(price))  # 确保有效的浮点字符串
        
        if not qty_str or qty_str.strip() in ['', '.', '0.']:
            qty_str = str(float(qty))  # 确保有效的浮点字符串
        
        return price_str, qty_str
        
    except Exception as e:
        # 最后的回退方案
        return str(float(price)), str(float(qty))
'''
    
    print("修复后的_format_order_params函数：")
    print(fixed_function)
    
    return fixed_function

def test_emergency_fix():
    """测试紧急修复"""
    print("\n=== 测试紧急修复 ===")
    
    def format_to_precision_fixed(value, precision_step):
        """修复后的版本"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 关键修复：确保不会小于一个步长（除非原值就是0）
            if rounded == 0 and decimal_value > 0:
                rounded = decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出
            if decimal_places > 0:
                # 使用固定精度格式化
                formatted = f"{float(rounded):.{decimal_places}f}"
                # 智能去除末尾的0，但保留至少一位小数（如果原本有小数）
                if '.' in formatted:
                    formatted = formatted.rstrip('0')
                    if formatted.endswith('.'):
                        # 如果去除0后只剩小数点，保留一位0
                        formatted = formatted + '0'
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            return str(value)
    
    def format_order_params_emergency_fix(price, qty, tick_size, step_size, min_qty):
        """紧急修复版本"""
        try:
            # 格式化价格
            price_str = format_to_precision_fixed(price, tick_size)
            
            # 格式化数量，确保不小于最小数量
            if min_qty and float(qty) < float(min_qty):
                qty = min_qty
            qty_str = format_to_precision_fixed(qty, step_size)
            
            # 最终验证和清理
            if not price_str or price_str.strip() in ['', '.', '0.']:
                price_str = str(float(price))
            
            if not qty_str or qty_str.strip() in ['', '.', '0.']:
                qty_str = str(float(qty))
            
            return price_str, qty_str
            
        except Exception as e:
            return str(float(price)), str(float(qty))
    
    # 测试PIPPINUSDT的实际情况
    test_cases = [
        (0.4, 236.417, 0.00001, 0.001, 0.001, "PIPPINUSDT实际参数"),
        (0.00005, 1000.0, 0.00001, 0.001, 0.001, "极小价格"),
        (1.23456, 50.0, 0.0001, 0.01, 0.01, "正常价格"),
    ]
    
    for price, qty, tick_size, step_size, min_qty, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: price={price}, qty={qty}")
        print(f"规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}")
        
        price_str, qty_str = format_order_params_emergency_fix(price, qty, tick_size, step_size, min_qty)
        print(f"修复后: price_str='{price_str}', qty_str='{qty_str}'")
        
        # 验证
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            notional = price_val * qty_val
            
            if price_val > 0 and qty_val > 0:
                print(f"✅ 修复成功: 名义价值={notional:.6f}")
            else:
                print(f"❌ 仍有问题: price_val={price_val}, qty_val={qty_val}")
                
        except Exception as e:
            print(f"❌ 验证失败: {e}")

if __name__ == '__main__':
    debug_format_to_precision()
    create_emergency_fix()
    test_emergency_fix()
    
    print("\n=== 紧急修复总结 ===")
    print("问题：当价格向下取整后小于一个tick_size时，会被格式化为0")
    print("修复：确保格式化后的价格至少为一个tick_size（除非原值就是0）")
    print("建议：立即应用此修复到maker_channel_enhanced.py")
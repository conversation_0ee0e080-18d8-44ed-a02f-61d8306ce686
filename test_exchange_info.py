#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试币安期货API的exchangeInfo响应，分析过滤器结构
"""

import requests
import json
import sys

def test_exchange_info():
    """测试获取交易规则信息"""
    
    # 配置代理
    proxies = {
        'http': 'http://127.0.0.1:7897',
        'https': 'http://127.0.0.1:7897'
    }
    
    # 测试符号
    test_symbols = ['ETHUSDT', 'BTCUSDT', 'SOLUSDT']
    
    for symbol in test_symbols:
        print(f"\n=== 测试 {symbol} ===")
        
        try:
            # 获取单个交易对的信息
            url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
            params = {'symbol': symbol}
            
            response = requests.get(url, params=params, proxies=proxies, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'symbols' in data and len(data['symbols']) > 0:
                    symbol_info = data['symbols'][0]
                    filters = symbol_info.get('filters', [])
                    
                    print(f"交易对: {symbol_info.get('symbol')}")
                    print(f"状态: {symbol_info.get('status')}")
                    print(f"过滤器数量: {len(filters)}")
                    
                    # 分析每个过滤器
                    for i, filter_info in enumerate(filters):
                        filter_type = filter_info.get('filterType')
                        print(f"  [{i}] {filter_type}: {filter_info}")
                        
                        # 重点关注价格和数量过滤器
                        if filter_type == 'PRICE_FILTER':
                            print(f"    价格过滤器 - tickSize: {filter_info.get('tickSize')}")
                        elif filter_type == 'LOT_SIZE':
                            print(f"    数量过滤器 - stepSize: {filter_info.get('stepSize')}, minQty: {filter_info.get('minQty')}")
                        elif filter_type == 'MIN_NOTIONAL':
                            print(f"    最小名义价值 - notional: {filter_info.get('notional')}")
                
                else:
                    print("未找到交易对信息")
                    
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")
            
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_exchange_info()
#!/usr/bin/env python3
"""
PIPPINUSDT 实时精度调试脚本
捕获下单时的具体参数，分析-1111错误原因
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

import logging
from decimal import Decimal, ROUND_DOWN
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
log = logging.getLogger(__name__)

def analyze_current_format_function():
    """分析当前_format_order_params函数的问题"""
    print("=== 分析当前格式化函数 ===")
    
    # 模拟PIPPINUSDT的交易规则（基于其他新币的典型规则）
    test_rules = [
        # (tick_size, step_size, min_qty, 描述)
        (0.00001, 0.001, 0.001, "高精度新币"),
        (0.0001, 0.01, 0.01, "中精度新币"),
        (0.001, 0.1, 0.1, "低精度新币"),
        (0.01, 1.0, 1.0, "超低精度新币"),
    ]
    
    # 模拟可能的下单参数（基于日志中的降档重试）
    test_params = [
        (0.4, 236.417, "实际参数"),  # 假设的实际参数
        (0.4, 177.313, "降档75%"),
        (0.4, 118.209, "降档50%"),
        (0.4, 59.104, "降档25%"),
    ]
    
    for tick_size, step_size, min_qty, rule_desc in test_rules:
        print(f"\n--- {rule_desc} ---")
        print(f"交易规则:")
        print(f"  tick_size: {tick_size}")
        print(f"  step_size: {step_size}")
        print(f"  min_qty: {min_qty}")
        print(f"  min_notional: 5.0")
        
        for price, qty, param_desc in test_params:
            print(f"\n{param_desc}:")
            print(f"  输入: price={price}, qty={qty}")
            
            # 使用修复后的格式化函数
            price_str, qty_str = format_order_params_fixed(price, qty, tick_size, step_size, min_qty)
            print(f"修复后格式化结果:")
            print(f"  price_str: '{price_str}'")
            print(f"  qty_str: '{qty_str}'")
            
            # 精度检查
            try:
                price_val = float(price_str)
                qty_val = float(qty_str)
                notional = price_val * qty_val
                
                # 检查价格精度
                if '.' in price_str:
                    price_decimals = len(price_str.split('.')[1])
                else:
                    price_decimals = 0
                
                tick_decimals = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
                
                print(f"精度检查:")
                print(f"  价格小数位: {price_decimals} (≤{tick_decimals})")
                
                # 检查数量精度
                if '.' in qty_str:
                    qty_decimals = len(qty_str.split('.')[1])
                else:
                    qty_decimals = 0
                
                step_decimals = len(str(step_size).split('.')[1]) if '.' in str(step_size) else 0
                print(f"  数量小数位: {qty_decimals} (≤{step_decimals})")
                print(f"  名义价值: {notional:.6f} (最小: 5.0) {'✅' if notional >= 5.0 else '⚠️'}")
                
                if price_decimals <= tick_decimals and qty_decimals <= step_decimals:
                    print(f"  ✅ 精度检查通过，应该不会出现-1111错误")
                else:
                    print(f"  ❌ 精度超限，可能出现-1111错误")
                
                # 检查参数有效性
                if price_str and qty_str and price_str not in ['', '.', '0.'] and qty_str not in ['', '.', '0.']:
                    print(f"  ✅ 参数有效性检查通过")
                else:
                    print(f"  ❌ 参数无效: price_str='{price_str}', qty_str='{qty_str}'")
                    
            except Exception as e:
                print(f"  ❌ 验证失败: {e}")

def format_order_params_fixed(price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化，使用Decimal确保精度"""
    
    def format_to_precision(value, precision_step):
        """根据精度步长格式化数值"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出 - 修复精度问题
            if decimal_places > 0:
                # 使用格式化字符串，确保不丢失精度
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 去除末尾的0，但保留必要的小数位
                if '.' in formatted:
                    # 分割整数和小数部分
                    int_part, dec_part = formatted.split('.')
                    # 去除小数部分末尾的0
                    dec_part = dec_part.rstrip('0')
                    if dec_part:
                        formatted = f"{int_part}.{dec_part}"
                    else:
                        formatted = int_part
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            # 如果Decimal处理失败，回退到简单字符串格式化
            return str(value)
    
    try:
        # 格式化价格
        price_str = format_to_precision(price, tick_size)
        
        # 格式化数量，确保不小于最小数量
        if min_qty and float(qty) < float(min_qty):
            qty = min_qty
        qty_str = format_to_precision(qty, step_size)
        
        # 最终验证和清理
        if not price_str or price_str.strip() in ['', '.', '0.']:
            price_str = str(float(price))  # 确保有效的浮点字符串
        
        if not qty_str or qty_str.strip() in ['', '.', '0.']:
            qty_str = str(float(qty))  # 确保有效的浮点字符串
        
        # 最终精度检查
        if tick_size and '.' in str(tick_size):
            max_decimals = len(str(tick_size).split('.')[1])
            if '.' in price_str:
                actual_decimals = len(price_str.split('.')[1])
                if actual_decimals > max_decimals:
                    # 重新格式化以符合精度要求
                    price_str = f"{float(price_str):.{max_decimals}f}".rstrip('0').rstrip('.')
        
        return price_str, qty_str
        
    except Exception as e:
        # 最后的回退方案
        return str(float(price)), str(float(qty))

def create_debug_patch():
    """创建调试补丁，在下单前记录参数"""
    print("\n=== 创建调试补丁 ===")
    
    debug_code = '''
# 在place_maker_order函数中添加调试日志
# 在调用_format_order_params之前和之后添加：

# 调用前
self.log.info(f"{symbol} 格式化前参数: price={price}, qty={qty}, tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}")

# 调用后
price_str, qty_str = self._format_order_params(price, qty, tick_size, step_size, min_qty)
self.log.info(f"{symbol} 格式化后参数: price_str='{price_str}', qty_str='{qty_str}'")

# 检查精度
if '.' in price_str:
    price_decimals = len(price_str.split('.')[1])
    tick_decimals = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
    if price_decimals > tick_decimals:
        self.log.warning(f"{symbol} 价格精度超限: {price_decimals} > {tick_decimals}")

if '.' in qty_str:
    qty_decimals = len(qty_str.split('.')[1])
    step_decimals = len(str(step_size).split('.')[1]) if '.' in str(step_size) else 0
    if qty_decimals > step_decimals:
        self.log.warning(f"{symbol} 数量精度超限: {qty_decimals} > {step_decimals}")
'''
    
    print("建议在place_maker_order函数中添加以下调试代码：")
    print(debug_code)
    
    return debug_code

def simulate_binance_validation():
    """模拟币安交易所的参数验证"""
    print("\n=== 模拟币安参数验证 ===")
    
    # 假设的PIPPINUSDT规则
    rules = {
        'tick_size': 0.00001,  # 价格精度：5位小数
        'step_size': 0.001,    # 数量精度：3位小数
        'min_qty': 0.001,      # 最小数量
        'min_notional': 5.0    # 最小名义价值
    }
    
    # 测试参数
    test_cases = [
        (0.4, 236.417, "可能的实际参数"),
        (0.400001, 236.417, "价格精度超限"),
        (0.4, 236.4175, "数量精度超限"),
        (0.4, 0.0005, "数量小于最小值"),
        (0.001, 1000.0, "名义价值不足"),
    ]
    
    print(f"PIPPINUSDT 交易规则:")
    for key, value in rules.items():
        print(f"  {key}: {value}")
    
    for price, qty, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"输入: price={price}, qty={qty}")
        
        # 格式化参数
        price_str, qty_str = format_order_params_fixed(price, qty, rules['tick_size'], rules['step_size'], rules['min_qty'])
        print(f"格式化后: price_str='{price_str}', qty_str='{qty_str}'")
        
        # 验证规则
        errors = []
        
        try:
            price_val = float(price_str)
            qty_val = float(qty_str)
            notional = price_val * qty_val
            
            # 检查价格精度
            if '.' in price_str:
                price_decimals = len(price_str.split('.')[1])
                max_price_decimals = len(str(rules['tick_size']).split('.')[1])
                if price_decimals > max_price_decimals:
                    errors.append(f"价格精度超限: {price_decimals} > {max_price_decimals}")
            
            # 检查数量精度
            if '.' in qty_str:
                qty_decimals = len(qty_str.split('.')[1])
                max_qty_decimals = len(str(rules['step_size']).split('.')[1])
                if qty_decimals > max_qty_decimals:
                    errors.append(f"数量精度超限: {qty_decimals} > {max_qty_decimals}")
            
            # 检查最小数量
            if qty_val < rules['min_qty']:
                errors.append(f"数量小于最小值: {qty_val} < {rules['min_qty']}")
            
            # 检查最小名义价值
            if notional < rules['min_notional']:
                errors.append(f"名义价值不足: {notional:.6f} < {rules['min_notional']}")
            
            if errors:
                print(f"❌ 验证失败: {'; '.join(errors)}")
                print(f"   预期错误码: -1111 (Precision is over the maximum defined for this asset)")
            else:
                print(f"✅ 验证通过，应该可以成功下单")
                print(f"   名义价值: {notional:.6f} USDT")
                
        except Exception as e:
            print(f"❌ 参数解析失败: {e}")

if __name__ == '__main__':
    analyze_current_format_function()
    create_debug_patch()
    simulate_binance_validation()
    
    print("\n=== 调试建议 ===")
    print("1. 在策略代码中添加详细的参数日志")
    print("2. 确认PIPPINUSDT的实际交易规则")
    print("3. 测试修复后的格式化函数")
    print("4. 监控下单过程中的参数变化")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORDERUSDT开仓失败问题综合调试脚本
分析精度超限和签名验证失败的根本原因
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import decimal as dec
from decimal import Decimal
import json
import hashlib
import hmac
import time
from urllib.parse import urlencode

def debug_orderusdt_issue():
    """调试ORDERUSDT开仓失败问题"""
    print("=== ORDERUSDT开仓失败问题调试 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际下单参数:")
    print(f"  数量: {actual_qty}")
    print(f"  价格: {actual_price}")
    print(f"  名义价值: {actual_qty * actual_price:.6f} USDT")
    
    # 分析精度问题
    analyze_precision_issue(actual_qty, actual_price)
    
    # 分析签名问题
    analyze_signature_issue(actual_qty, actual_price)

def analyze_precision_issue(qty, price):
    """分析精度问题"""
    print("\n=== 精度问题分析 ===")
    
    # 基于常见新币的可能交易规则
    possible_rules = [
        {
            "name": "规则1: 高精度",
            "tick_size": 0.00001,    # 5位小数
            "step_size": 0.001,      # 3位小数
            "min_qty": 0.001,
            "min_notional": 5.0
        },
        {
            "name": "规则2: 中精度", 
            "tick_size": 0.0001,     # 4位小数
            "step_size": 0.01,       # 2位小数
            "min_qty": 0.01,
            "min_notional": 5.0
        },
        {
            "name": "规则3: 低精度",
            "tick_size": 0.001,      # 3位小数
            "step_size": 0.1,        # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0
        }
    ]
    
    for rules in possible_rules:
        print(f"\n--- {rules['name']} ---")
        test_precision_with_rules(qty, price, rules)

def test_precision_with_rules(qty, price, rules):
    """测试特定规则下的精度处理"""
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules['min_notional']
    
    print(f"交易规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}, min_notional={min_notional}")
    
    # 使用策略中的精度处理函数
    rounded_price = _round_price(price, tick_size)
    rounded_qty = _round_qty(qty, step_size, min_qty, rounded_price, min_notional)
    
    print(f"精度处理后: price={rounded_price}, qty={rounded_qty}")
    
    # 格式化参数（这是关键步骤）
    price_str, qty_str = format_order_params(rounded_price, rounded_qty, tick_size, step_size, min_qty)
    
    print(f"格式化后: price_str='{price_str}', qty_str='{qty_str}'")
    
    # 检查精度是否超限
    price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
    qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
    
    # 计算允许的最大小数位数
    tick_decimals = get_decimal_places(tick_size)
    step_decimals = get_decimal_places(step_size)
    
    print(f"精度检查:")
    print(f"  价格小数位: {price_decimals} (允许: {tick_decimals}) {'✅' if price_decimals <= tick_decimals else '❌'}")
    print(f"  数量小数位: {qty_decimals} (允许: {step_decimals}) {'✅' if qty_decimals <= step_decimals else '❌'}")
    
    # 检查名义价值
    notional = float(price_str) * float(qty_str)
    print(f"  名义价值: {notional:.6f} (最小: {min_notional}) {'✅' if notional >= min_notional else '❌'}")

def analyze_signature_issue(qty, price):
    """分析签名问题"""
    print("\n=== 签名问题分析 ===")
    
    # 模拟下单参数
    symbol = "ORDERUSDT"
    side = "BUY"
    type_order = "LIMIT"
    timeInForce = "GTC"
    timestamp = int(time.time() * 1000)
    
    # 测试不同的精度格式化对签名的影响
    test_cases = [
        {"name": "原始参数", "qty": qty, "price": price},
        {"name": "格式化参数1", "qty": 236.417, "price": 0.4},
        {"name": "格式化参数2", "qty": 236.4, "price": 0.4},
        {"name": "格式化参数3", "qty": 236, "price": 0.4},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        test_signature_with_params(symbol, side, type_order, timeInForce, case['qty'], case['price'], timestamp)

def test_signature_with_params(symbol, side, type_order, timeInForce, qty, price, timestamp):
    """测试特定参数下的签名生成"""
    # 构建参数字典
    params = {
        'symbol': symbol,
        'side': side,
        'type': type_order,
        'timeInForce': timeInForce,
        'quantity': str(qty),
        'price': str(price),
        'timestamp': timestamp
    }
    
    print(f"参数: {params}")
    
    # 生成查询字符串
    query_string = urlencode(sorted(params.items()))
    print(f"查询字符串: {query_string}")
    
    # 模拟签名生成（使用假密钥）
    secret_key = "fake_secret_key_for_testing"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        query_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    print(f"签名: {signature}")

def format_order_params(price, qty, tick_size, step_size, min_qty):
    """格式化下单参数（复制自策略代码的逻辑）"""
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (Decimal(str(price)) / Decimal(str(tick_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(tick_size))
        price_str = format(price_dec.normalize(), 'f')
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (Decimal(str(qty)) / Decimal(str(step_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(step_size))
        qty_dec = max(qty_dec, Decimal(str(min_qty or 0)))
        qty_str = format(qty_dec.normalize(), 'f')
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def _round_price(price, tick_size):
    """价格精度处理"""
    if price <= 0 or tick_size <= 0:
        return price
    dec.getcontext().prec = 18
    tick_d = Decimal(str(tick_size))
    price_d = Decimal(str(price))
    rounded = float((price_d / tick_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
    return max(rounded, tick_size)

def _round_qty(q, step, min_q, price=None, min_notional=None):
    """数量精度处理"""
    if q <= 0 or step <= 0:
        return min_q
    dec.getcontext().prec = 18
    step_d = Decimal(str(step))
    
    # 先按步长向下取整
    rounded_down = float((Decimal(str(q)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
    rounded_down = max(rounded_down, min_q)
    
    # 如果提供了价格和最小名义价值，检查是否满足要求
    if price and min_notional:
        notional = rounded_down * price
        if notional < min_notional:
            # 向上调整到满足最小名义价值的数量
            required_qty = min_notional / price
            rounded_up = float((Decimal(str(required_qty)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_UP) * step_d)
            return max(rounded_up, min_q)
    
    return rounded_down

def get_decimal_places(value):
    """获取数值的小数位数"""
    if value == 0:
        return 0
    
    # 转换为字符串并去除科学计数法
    value_str = f"{value:.10f}".rstrip('0').rstrip('.')
    
    if '.' in value_str:
        return len(value_str.split('.')[1])
    else:
        return 0

if __name__ == '__main__':
    debug_orderusdt_issue()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取ORDERUSDT的真实交易规则
"""

import requests
import json

def get_orderusdt_rules():
    """获取ORDERUSDT的交易规则"""
    print("=== 获取ORDERUSDT交易规则 ===")
    
    try:
        # 获取币安期货交易规则
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        params = {"symbol": "ORDERUSDT"}
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if 'symbols' not in data or len(data['symbols']) == 0:
            print("❌ 未找到ORDERUSDT交易对")
            return
        
        symbol_info = data['symbols'][0]
        print(f"交易对: {symbol_info['symbol']}")
        print(f"状态: {symbol_info['status']}")
        print(f"基础资产: {symbol_info['baseAsset']}")
        print(f"报价资产: {symbol_info['quoteAsset']}")
        
        # 解析过滤器
        filters = symbol_info['filters']
        
        print("\n=== 交易规则 ===")
        for filter_info in filters:
            filter_type = filter_info.get('filterType')
            
            if filter_type == 'PRICE_FILTER':
                tick_size = float(filter_info['tickSize'])
                min_price = float(filter_info['minPrice'])
                max_price = float(filter_info['maxPrice'])
                print(f"价格过滤器:")
                print(f"  tick_size: {tick_size}")
                print(f"  min_price: {min_price}")
                print(f"  max_price: {max_price}")
                
            elif filter_type == 'LOT_SIZE':
                step_size = float(filter_info['stepSize'])
                min_qty = float(filter_info['minQty'])
                max_qty = float(filter_info['maxQty'])
                print(f"数量过滤器:")
                print(f"  step_size: {step_size}")
                print(f"  min_qty: {min_qty}")
                print(f"  max_qty: {max_qty}")
                
            elif filter_type == 'MIN_NOTIONAL':
                min_notional = float(filter_info['notional'])
                print(f"最小名义价值:")
                print(f"  min_notional: {min_notional}")
                
            elif filter_type == 'MARKET_LOT_SIZE':
                step_size_market = float(filter_info['stepSize'])
                min_qty_market = float(filter_info['minQty'])
                max_qty_market = float(filter_info['maxQty'])
                print(f"市价单数量过滤器:")
                print(f"  step_size: {step_size_market}")
                print(f"  min_qty: {min_qty_market}")
                print(f"  max_qty: {max_qty_market}")
        
        # 计算小数位数
        def get_decimal_places(value):
            if value == 0:
                return 0
            str_val = f"{value:.20f}".rstrip('0')
            if '.' in str_val:
                return len(str_val.split('.')[-1])
            return 0
        
        # 找到关键过滤器
        tick_size = None
        step_size = None
        min_qty = None
        min_notional = None
        
        for filter_info in filters:
            filter_type = filter_info.get('filterType')
            if filter_type == 'PRICE_FILTER':
                tick_size = float(filter_info['tickSize'])
            elif filter_type == 'LOT_SIZE':
                step_size = float(filter_info['stepSize'])
                min_qty = float(filter_info['minQty'])
            elif filter_type == 'MIN_NOTIONAL':
                min_notional = float(filter_info['notional'])
        
        if all([tick_size, step_size, min_qty, min_notional]):
            print(f"\n=== 精度要求 ===")
            tick_decimals = get_decimal_places(tick_size)
            step_decimals = get_decimal_places(step_size)
            print(f"价格最大小数位: {tick_decimals}")
            print(f"数量最大小数位: {step_decimals}")
            
            # 测试实际下单参数
            print(f"\n=== 测试实际下单参数 ===")
            test_price = 0.400000
            test_qty = 236.417
            
            price_decimals = len(str(test_price).split('.')[-1]) if '.' in str(test_price) else 0
            qty_decimals = len(str(test_qty).split('.')[-1]) if '.' in str(test_qty) else 0
            
            print(f"测试价格: {test_price} ({price_decimals}位小数)")
            print(f"测试数量: {test_qty} ({qty_decimals}位小数)")
            print(f"名义价值: {test_price * test_qty:.6f} USDT")
            
            print(f"\n=== 精度检查 ===")
            if price_decimals > tick_decimals:
                print(f"❌ 价格精度过高: {price_decimals} > {tick_decimals}")
            else:
                print(f"✅ 价格精度符合要求: {price_decimals} ≤ {tick_decimals}")
                
            if qty_decimals > step_decimals:
                print(f"❌ 数量精度过高: {qty_decimals} > {step_decimals}")
            else:
                print(f"✅ 数量精度符合要求: {qty_decimals} ≤ {step_decimals}")
                
            if test_price * test_qty < min_notional:
                print(f"❌ 名义价值不足: {test_price * test_qty:.6f} < {min_notional}")
            else:
                print(f"✅ 名义价值符合要求: {test_price * test_qty:.6f} ≥ {min_notional}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    get_orderusdt_rules()
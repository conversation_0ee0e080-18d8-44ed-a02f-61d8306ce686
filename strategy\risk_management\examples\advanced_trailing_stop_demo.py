"""
阶梯式移动止损策略演示
Advanced Trailing Stop Strategy Demo

演示优化后的阶梯式移动止损机制，解决原有参数冲突问题
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import Dict, Any

# 导入风控模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from five_layer_risk_control import (
        FiveLayerRiskControl,
        RiskConfig,
        MarketDataCalculator
    )
except ImportError:
    # 如果直接导入失败，尝试相对导入
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from five_layer_risk_control import (
        FiveLayerRiskControl,
        RiskConfig,
        MarketDataCalculator
    )


def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    return logging.getLogger(__name__)


def generate_market_data(base_price: float, volatility: float = 0.02) -> Dict:
    """生成模拟市场数据"""
    return {
        'atr': base_price * volatility,
        'low_min': base_price * 0.95,
        'high_max': base_price * 1.05,
        'close_std': base_price * 0.015,
        'depth': 150000.0
    }


def demo_normal_market_trailing():
    """演示常规市场下的阶梯式移动止损"""
    print("\n" + "="*80)
    print("演示1：常规市场环境下的阶梯式移动止损")
    print("="*80)
    
    logger = setup_logging()
    
    # 配置常规模式参数
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%最小启用阈值
        trailing_profit_step_normal=0.10,     # 每10%盈利一个阶梯
        trailing_stop_ratio_normal=0.70,      # 保留70%利润
        extreme_market_atr_multiplier=2.5,    # ATR极端判定倍数
        extreme_market_daily_gain=0.15        # 15%单日涨幅判定
    )
    
    risk_control = FiveLayerRiskControl(config, logger)
    
    # 设置持仓：BTC 50000入场，0.1数量
    symbol = 'BTCUSDT'
    entry_price = 50000.0
    quantity = 0.1
    risk_control.set_position(symbol, entry_price, quantity, entry_price * quantity)
    
    # 模拟价格上涨序列
    price_sequence = [
        50000,  # 入场价
        52500,  # +5% (触发保本止损)
        55000,  # +10% (第1阶梯，止损价=50000*1.07=53500)
        57500,  # +15%
        60000,  # +20% (第2阶梯，止损价=50000*1.14=57000)
        62500,  # +25%
        65000,  # +30% (第3阶梯，止损价=50000*1.21=60500)
        63000,  # 回调但未触发止损
        67500,  # +35%
        70000   # +40% (第4阶梯，止损价=50000*1.28=64000)
    ]
    
    print(f"常规模式参数：每{config.trailing_profit_step_normal:.0%}盈利一个阶梯，保留{config.trailing_stop_ratio_normal:.0%}利润")
    print(f"入场价格：{entry_price:.0f} USDT")
    print("-" * 80)
    
    for i, current_price in enumerate(price_sequence):
        print(f"\n第{i+1}步：价格 {current_price:.0f} USDT")
        
        # 生成市场数据（常规波动）
        market_data = generate_market_data(current_price, volatility=0.015)
        
        # 执行风控监控
        result = risk_control.monitor_position(symbol, current_price, market_data)
        
        # 显示当前状态
        profit_pct = (current_price - entry_price) / entry_price
        current_stop = risk_control.position.get('trail_stop', 0)
        trailing_level = risk_control.position.get('trailing_level', 0)
        
        print(f"  当前盈利：{profit_pct:.1%}")
        print(f"  阶梯级别：第{trailing_level}阶梯")
        print(f"  止损价格：{current_stop:.0f} USDT")
        
        if current_stop > 0:
            protected_gain = (current_stop - entry_price) / entry_price
            print(f"  保护利润：{protected_gain:.1%}")
        
        # 检查是否触发止损
        if result['action'] == 'stop_loss':
            print(f"  🛑 触发止损！最终保护利润：{protected_gain:.1%}")
            break
        
        time.sleep(0.1)


def demo_extreme_market_trailing():
    """演示极端市场下的阶梯式移动止损"""
    print("\n" + "="*80)
    print("演示2：极端市场环境下的阶梯式移动止损")
    print("="*80)
    
    logger = setup_logging()
    
    # 配置极端模式参数
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%最小启用阈值
        trailing_profit_step_extreme=0.05,    # 每5%盈利一个阶梯
        trailing_stop_ratio_extreme=0.80,     # 保留80%利润
        extreme_market_atr_multiplier=2.5,    # ATR极端判定倍数
        extreme_market_daily_gain=0.15        # 15%单日涨幅判定
    )
    
    risk_control = FiveLayerRiskControl(config, logger)
    
    # 设置持仓：ETH 3000入场，1.0数量
    symbol = 'ETHUSDT'
    entry_price = 3000.0
    quantity = 1.0
    risk_control.set_position(symbol, entry_price, quantity, entry_price * quantity)
    
    # 模拟极端上涨序列（高波动）
    price_sequence = [
        3000,   # 入场价
        3150,   # +5% (第1阶梯，止损价=3000*1.04=3120)
        3300,   # +10% (第2阶梯，止损价=3000*1.08=3240)
        3450,   # +15% (第3阶梯，止损价=3000*1.12=3360)
        3600,   # +20% (第4阶梯，止损价=3000*1.16=3480)
        3750,   # +25% (第5阶梯，止损价=3000*1.20=3600)
        3500,   # 大幅回调但未触发止损
        3900,   # +30% (第6阶梯，止损价=3000*1.24=3720)
        4200    # +40% (第8阶梯，止损价=3000*1.32=3960)
    ]
    
    print(f"极端模式参数：每{config.trailing_profit_step_extreme:.0%}盈利一个阶梯，保留{config.trailing_stop_ratio_extreme:.0%}利润")
    print(f"入场价格：{entry_price:.0f} USDT")
    print("-" * 80)
    
    for i, current_price in enumerate(price_sequence):
        print(f"\n第{i+1}步：价格 {current_price:.0f} USDT")
        
        # 生成市场数据（高波动，触发极端模式）
        market_data = generate_market_data(current_price, volatility=0.05)  # 高波动
        market_data['close_std'] = current_price * 0.01  # 较小的标准差，使ATR显得更大
        
        # 执行风控监控
        result = risk_control.monitor_position(symbol, current_price, market_data)
        
        # 显示当前状态
        profit_pct = (current_price - entry_price) / entry_price
        current_stop = risk_control.position.get('trail_stop', 0)
        trailing_level = risk_control.position.get('trailing_level', 0)
        trailing_mode = risk_control.position.get('trailing_mode', '常规模式')
        
        print(f"  当前盈利：{profit_pct:.1%}")
        print(f"  市场模式：{trailing_mode}")
        print(f"  阶梯级别：第{trailing_level}阶梯")
        print(f"  止损价格：{current_stop:.0f} USDT")
        
        if current_stop > 0:
            protected_gain = (current_stop - entry_price) / entry_price
            print(f"  保护利润：{protected_gain:.1%}")
        
        # 检查是否触发止损
        if result['action'] == 'stop_loss':
            print(f"  🛑 触发止损！最终保护利润：{protected_gain:.1%}")
            break
        
        time.sleep(0.1)


def demo_comparison_old_vs_new():
    """对比演示：旧版本vs新版本移动止损"""
    print("\n" + "="*80)
    print("演示3：旧版本 vs 新版本移动止损对比")
    print("="*80)
    
    logger = setup_logging()
    
    # 模拟价格序列：正常波动后大涨
    price_sequence = [
        50000,  # 入场
        51000,  # +2%
        50500,  # 回调
        52000,  # +4%
        51500,  # 回调
        53000,  # +6%
        52000,  # 回调
        55000,  # +10%
        60000   # +20%
    ]
    
    print("价格序列：", [f"{p:.0f}" for p in price_sequence])
    print("-" * 80)
    
    # 旧版本配置（激进）
    old_config = RiskConfig(
        trailing_trigger_pct=0.02,     # 2%启用
        trailing_stop_pct=0.01         # 1%距离
    )
    
    # 新版本配置（稳健）
    new_config = RiskConfig(
        trailing_trigger_pct=0.05,            # 5%最小启用
        trailing_profit_step_normal=0.10,     # 10%阶梯
        trailing_stop_ratio_normal=0.70       # 保留70%
    )
    
    print("旧版本（激进）：2%启用，1%距离跟随")
    print("新版本（稳健）：5%启用，10%阶梯，保留70%利润")
    print("-" * 80)
    
    # 模拟对比
    for i, price in enumerate(price_sequence):
        profit_pct = (price - 50000) / 50000
        
        # 旧版本逻辑
        old_stop = 0
        if profit_pct >= 0.02:  # 2%启用
            old_stop = price * 0.99  # 1%距离
        
        # 新版本逻辑（简化）
        new_stop = 0
        if profit_pct >= 0.05:  # 5%启用
            level = int(profit_pct / 0.10)  # 10%阶梯
            if level >= 1:
                protected_profit = level * 0.10 * 0.70  # 保留70%
                new_stop = 50000 * (1 + protected_profit)
        
        print(f"价格{price:.0f} (盈利{profit_pct:.1%}): 旧版止损{old_stop:.0f}, 新版止损{new_stop:.0f}")
        
        # 检查是否被止损
        if old_stop > 0 and price <= old_stop:
            print(f"  ❌ 旧版本在{price:.0f}被止损")
        if new_stop > 0 and price <= new_stop:
            print(f"  ❌ 新版本在{price:.0f}被止损")


def demo_breakeven_compatibility():
    """演示保本止损与阶梯式移动止损的兼容性"""
    print("\n" + "="*80)
    print("演示4：保本止损与阶梯式移动止损的兼容性")
    print("="*80)
    
    logger = setup_logging()
    
    config = RiskConfig(
        breakeven_trigger_pct=0.05,           # 5%触发保本
        trailing_trigger_pct=0.05,            # 5%启用移动止损
        trailing_profit_step_normal=0.10,     # 10%阶梯
        trailing_stop_ratio_normal=0.70       # 保留70%
    )
    
    risk_control = FiveLayerRiskControl(config, logger)
    
    # 设置持仓
    symbol = 'BTCUSDT'
    entry_price = 50000.0
    risk_control.set_position(symbol, entry_price, 0.1, 5000.0)
    
    # 价格序列：触发保本后继续上涨
    price_sequence = [
        50000,  # 入场
        52500,  # +5% (触发保本止损：50200)
        55000,  # +10% (第1阶梯：53500，但保本止损更高)
        60000   # +20% (第2阶梯：57000，超过保本止损)
    ]
    
    print("测试保本止损(5%触发，成本+0.4%)与阶梯式移动止损的协同工作")
    print("-" * 80)
    
    for i, current_price in enumerate(price_sequence):
        print(f"\n第{i+1}步：价格 {current_price:.0f} USDT")
        
        market_data = generate_market_data(current_price)
        result = risk_control.monitor_position(symbol, current_price, market_data)
        
        profit_pct = (current_price - entry_price) / entry_price
        breakeven_stop = entry_price * 1.004 if risk_control.position.get('breakeven') else 0
        trail_stop = risk_control.position.get('trail_stop', 0)
        final_stop = max(breakeven_stop, trail_stop)
        
        print(f"  当前盈利：{profit_pct:.1%}")
        print(f"  保本止损：{breakeven_stop:.0f} USDT")
        print(f"  移动止损：{trail_stop:.0f} USDT")
        print(f"  最终止损：{final_stop:.0f} USDT (取较高者)")
        
        # 显示风控动作
        for detail in result.get('details', []):
            if detail['type'] == 'breakeven':
                print(f"  ✅ 保本止损已激活")
            elif detail['type'] == 'trailing':
                print(f"  ✅ 阶梯式移动止损已更新")


if __name__ == "__main__":
    print("阶梯式移动止损策略演示")
    print("Advanced Trailing Stop Strategy Demo")
    
    # 运行所有演示
    demo_normal_market_trailing()
    demo_extreme_market_trailing()
    demo_comparison_old_vs_new()
    demo_breakeven_compatibility()
    
    print("\n" + "="*80)
    print("所有演示完成！")
    print("\n核心优势总结：")
    print("✅ 减少正常波动中的误触发")
    print("✅ 在趋势行情中保留更多利润") 
    print("✅ 极端行情下提供更灵活的保护")
    print("✅ 与保本止损完美兼容")
    print("="*80)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试_format_order_params函数在不同精度规则下的表现
"""

import decimal as dec

def _format_order_params(price, qty, tick_size, step_size, min_qty):
    """安全的下单参数格式化，避免精度错误（复制自策略代码）"""
    dec.getcontext().prec = 18
    
    # 计算允许的最大小数位数
    def get_decimal_places(value):
        if value == 0:
            return 0
        str_val = f"{value:.20f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[-1])
        return 0
    
    # 价格格式化
    if tick_size:
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        tick_decimals = get_decimal_places(tick_size)
        # 限制小数位数，避免精度错误
        price_str = f"{float(price_dec):.{tick_decimals}f}".rstrip('0').rstrip('.')
        if not price_str or price_str == '':
            price_str = f"{float(price_dec):.{max(1, tick_decimals)}f}"
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
        step_decimals = get_decimal_places(step_size)
        # 限制小数位数，避免精度错误
        qty_str = f"{float(qty_dec):.{step_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str or qty_str == '':
            qty_str = f"{float(qty_dec):.{max(1, step_decimals)}f}"
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def test_format_precision():
    """测试格式化函数的精度处理"""
    print("=== 测试_format_order_params函数精度处理 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际参数: qty={actual_qty}, price={actual_price}")
    
    # 测试不同的交易规则
    test_cases = [
        {
            "name": "高精度新币",
            "tick_size": 0.00001,    # 5位小数
            "step_size": 0.001,      # 3位小数
            "min_qty": 0.001,
            "min_notional": 5.0
        },
        {
            "name": "中精度新币", 
            "tick_size": 0.0001,     # 4位小数
            "step_size": 0.01,       # 2位小数
            "min_qty": 0.01,
            "min_notional": 5.0
        },
        {
            "name": "低精度新币",
            "tick_size": 0.001,      # 3位小数
            "step_size": 0.1,        # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0
        },
        {
            "name": "超低精度新币",
            "tick_size": 0.01,       # 2位小数
            "step_size": 1.0,        # 整数
            "min_qty": 1.0,
            "min_notional": 5.0
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        test_case_precision(actual_qty, actual_price, case)

def test_case_precision(qty, price, rules):
    """测试特定规则下的精度处理"""
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules['min_notional']
    
    print(f"交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 使用格式化函数
    price_str, qty_str = _format_order_params(price, qty, tick_size, step_size, min_qty)
    
    print(f"格式化结果:")
    print(f"  price_str: '{price_str}'")
    print(f"  qty_str: '{qty_str}'")
    
    # 检查精度
    price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
    qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
    
    # 计算允许的最大小数位数
    def get_decimal_places(value):
        if value == 0:
            return 0
        str_val = f"{value:.20f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[-1])
        return 0
    
    tick_decimals = get_decimal_places(tick_size)
    step_decimals = get_decimal_places(step_size)
    
    print(f"精度检查:")
    print(f"  价格小数位: {price_decimals} (允许: {tick_decimals}) {'✅' if price_decimals <= tick_decimals else '❌'}")
    print(f"  数量小数位: {qty_decimals} (允许: {step_decimals}) {'✅' if qty_decimals <= step_decimals else '❌'}")
    
    # 检查名义价值
    try:
        notional = float(price_str) * float(qty_str)
        print(f"  名义价值: {notional:.6f} (最小: {min_notional}) {'✅' if notional >= min_notional else '❌'}")
    except ValueError as e:
        print(f"  名义价值计算错误: {e}")
    
    # 检查是否可能导致精度错误
    if price_decimals > tick_decimals or qty_decimals > step_decimals:
        print(f"  ⚠️  可能导致精度错误 (-1111)")
    else:
        print(f"  ✅ 精度检查通过")

if __name__ == '__main__':
    test_format_precision()
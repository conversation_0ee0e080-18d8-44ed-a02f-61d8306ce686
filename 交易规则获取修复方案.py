#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易规则获取修复方案
解决 step_size=None 导致的精度错误问题
"""

import decimal as dec
from typing import Dict, Optional, Any
import logging

class TradingRulesManager:
    """增强的交易规则管理器"""
    
    def __init__(self, client):
        self.client = client
        self.rules_cache = {}
        self.logger = logging.getLogger(__name__)
    
    def get_trading_rules_enhanced(self, symbol: str) -> Optional[Dict[str, Any]]:
        """增强的交易规则获取方法"""
        
        # 方法1: 缓存查询
        if symbol in self.rules_cache:
            rules = self.rules_cache[symbol]
            if self._validate_rules(symbol, rules):
                self.logger.info(f"✅ {symbol} 使用缓存交易规则: {rules}")
                return rules
        
        # 方法2: 实时API获取
        try:
            self.logger.info(f"🔄 {symbol} 实时获取交易规则...")
            exchange_info = self.client.futures_exchange_info()
            
            for symbol_info in exchange_info.get('symbols', []):
                if symbol_info['symbol'] == symbol:
                    rules = self._extract_rules(symbol_info)
                    if self._validate_rules(symbol, rules):
                        self.rules_cache[symbol] = rules
                        self.logger.info(f"✅ {symbol} 实时获取成功: {rules}")
                        return rules
                    
        except Exception as e:
            self.logger.error(f"❌ {symbol} 实时获取失败: {e}")
        
        # 方法3: 默认规则 (基于交易对特征)
        default_rules = self._get_default_rules(symbol)
        if default_rules:
            self.logger.warning(f"⚠️ {symbol} 使用默认规则: {default_rules}")
            return default_rules
        
        self.logger.error(f"❌ {symbol} 所有方法都失败，无法获取交易规则")
        return None
    
    def _extract_rules(self, symbol_info: Dict) -> Dict[str, Any]:
        """从交易所信息中提取规则"""
        
        rules = {
            'tick_size': None,
            'step_size': None,
            'min_qty': None,
            'max_qty': None,
            'min_notional': None,
        }
        
        # 提取过滤器信息
        for filter_info in symbol_info.get('filters', []):
            filter_type = filter_info.get('filterType')
            
            if filter_type == 'PRICE_FILTER':
                rules['tick_size'] = filter_info.get('tickSize')
            elif filter_type == 'LOT_SIZE':
                rules['step_size'] = filter_info.get('stepSize')
                rules['min_qty'] = filter_info.get('minQty')
                rules['max_qty'] = filter_info.get('maxQty')
            elif filter_type == 'MIN_NOTIONAL':
                rules['min_notional'] = filter_info.get('notional')
        
        return rules
    
    def _validate_rules(self, symbol: str, rules: Dict) -> bool:
        """验证交易规则的有效性"""
        
        if not rules:
            self.logger.error(f"❌ {symbol} 规则为空")
            return False
        
        # 检查关键字段
        required_fields = ['tick_size', 'step_size', 'min_qty']
        for field in required_fields:
            value = rules.get(field)
            if value is None or value == 'None' or value == '0':
                self.logger.error(f"❌ {symbol} {field} 无效: {value}")
                return False
        
        # 检查数值有效性
        try:
            step_size = dec.Decimal(str(rules['step_size']))
            tick_size = dec.Decimal(str(rules['tick_size']))
            min_qty = dec.Decimal(str(rules['min_qty']))
            
            if step_size <= 0 or tick_size <= 0 or min_qty <= 0:
                self.logger.error(f"❌ {symbol} 规则数值无效")
                return False
                
        except (ValueError, TypeError, dec.InvalidOperation) as e:
            self.logger.error(f"❌ {symbol} 规则格式错误: {e}")
            return False
        
        self.logger.info(f"✅ {symbol} 规则验证通过")
        return True
    
    def _get_default_rules(self, symbol: str) -> Optional[Dict[str, Any]]:
        """基于交易对特征的默认规则"""
        
        # 常见交易对的默认规则
        default_rules_map = {
            # 主流币种
            'BTCUSDT': {
                'tick_size': '0.01',
                'step_size': '0.001',
                'min_qty': '0.001',
                'min_notional': '5',
            },
            'ETHUSDT': {
                'tick_size': '0.01',
                'step_size': '0.001',
                'min_qty': '0.001',
                'min_notional': '5',
            },
            # 小币种通用规则
            'DEFAULT_SMALL': {
                'tick_size': '0.00001',
                'step_size': '0.1',
                'min_qty': '0.1',
                'min_notional': '5',
            },
            # 新币通用规则
            'DEFAULT_NEW': {
                'tick_size': '0.0001',
                'step_size': '1',
                'min_qty': '1',
                'min_notional': '5',
            }
        }
        
        # 直接匹配
        if symbol in default_rules_map:
            return default_rules_map[symbol]
        
        # 基于价格范围推测
        try:
            # 获取当前价格来推测规则
            ticker = self.client.futures_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])
            
            if price >= 100:  # 高价币
                return {
                    'tick_size': '0.01',
                    'step_size': '0.001',
                    'min_qty': '0.001',
                    'min_notional': '5',
                }
            elif price >= 1:  # 中价币
                return {
                    'tick_size': '0.0001',
                    'step_size': '0.01',
                    'min_qty': '0.01',
                    'min_notional': '5',
                }
            else:  # 低价币
                return {
                    'tick_size': '0.00001',
                    'step_size': '1',
                    'min_qty': '1',
                    'min_notional': '5',
                }
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 无法获取价格推测规则: {e}")
        
        # 最后的默认规则
        return default_rules_map['DEFAULT_NEW']


class SmartNominalValueManager:
    """智能名义价值管理器"""
    
    def __init__(self, client):
        self.client = client
        self.logger = logging.getLogger(__name__)
    
    def adjust_nominal_value(self, symbol: str, calculated_nominal: float) -> Optional[float]:
        """智能名义价值调整"""
        
        if calculated_nominal >= 5.0:
            return calculated_nominal
        
        self.logger.warning(f"⚠️ {symbol} 计算名义价值不足: {calculated_nominal:.2f} < 5.0")
        
        # 检查账户余额
        try:
            account = self.client.futures_account()
            usdt_balance = 0
            
            for balance in account.get('assets', []):
                if balance['asset'] == 'USDT':
                    usdt_balance = float(balance['availableBalance'])
                    break
            
            self.logger.info(f"💰 账户USDT余额: {usdt_balance:.2f}")
            
            if usdt_balance >= 6.0:
                self.logger.info(f"✅ {symbol} 余额充足，调整为最小开仓要求: 6.0 USDT")
                return 6.0
            else:
                self.logger.warning(f"❌ {symbol} 余额不足 ({usdt_balance:.2f}), 跳过交易")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 获取账户余额失败: {e}")
            return None
    
    def calculate_adjusted_quantity(self, symbol: str, target_nominal: float, price: float, rules: Dict) -> Optional[float]:
        """根据调整后的名义价值计算数量"""
        
        try:
            # 基础数量计算
            base_qty = target_nominal / price
            
            # 根据step_size调整
            step_size = dec.Decimal(str(rules['step_size']))
            min_qty = dec.Decimal(str(rules['min_qty']))
            
            # 向下取整到step_size的倍数
            qty_decimal = dec.Decimal(str(base_qty))
            adjusted_qty = (qty_decimal // step_size) * step_size
            
            # 确保不小于最小数量
            if adjusted_qty < min_qty:
                adjusted_qty = min_qty
            
            self.logger.info(f"📊 {symbol} 数量调整: {base_qty:.8f} → {adjusted_qty}")
            return float(adjusted_qty)
            
        except Exception as e:
            self.logger.error(f"❌ {symbol} 数量调整失败: {e}")
            return None


# 使用示例
def example_usage():
    """使用示例"""
    
    # 假设的客户端
    class MockClient:
        def futures_exchange_info(self):
            return {'symbols': []}
        
        def futures_symbol_ticker(self, symbol):
            return {'price': '0.042'}
        
        def futures_account(self):
            return {'assets': [{'asset': 'USDT', 'availableBalance': '100.0'}]}
    
    client = MockClient()
    
    # 创建管理器
    rules_manager = TradingRulesManager(client)
    nominal_manager = SmartNominalValueManager(client)
    
    # 获取交易规则
    symbol = 'PLAYUSDT'
    rules = rules_manager.get_trading_rules_enhanced(symbol)
    
    if rules:
        print(f"✅ {symbol} 交易规则: {rules}")
        
        # 智能名义价值调整
        calculated_nominal = 3.11  # 模拟计算出的不足值
        adjusted_nominal = nominal_manager.adjust_nominal_value(symbol, calculated_nominal)
        
        if adjusted_nominal:
            print(f"💰 {symbol} 名义价值调整: {calculated_nominal} → {adjusted_nominal}")
            
            # 计算调整后的数量
            price = 0.042
            adjusted_qty = nominal_manager.calculate_adjusted_quantity(
                symbol, adjusted_nominal, price, rules
            )
            
            if adjusted_qty:
                print(f"📊 {symbol} 最终数量: {adjusted_qty}")
            else:
                print(f"❌ {symbol} 数量计算失败")
        else:
            print(f"❌ {symbol} 名义价值调整失败")
    else:
        print(f"❌ {symbol} 交易规则获取失败")


if __name__ == "__main__":
    example_usage()

# -*- coding: utf-8 -*-
"""
极简新币通道突破策略 - 修复版 (增强日志功能)
修复了开仓、平仓、止盈止损逻辑中的问题
增加了详细的日志输出功能，便于问题排查和分析
python maker_channel_light_fixed.py
"""
import time, logging, datetime as dt
import pandas as pd
import requests
import traceback
import os
from decimal import Decimal, ROUND_DOWN
from logging.handlers import RotatingFileHandler

API_KEY = "nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s"
SECRET  = "HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u"
BASE    = 'https://fapi.binance.com'

session = requests.Session()
session.headers.update({'X-MBX-APIKEY': API_KEY})

def setup_logger(log_level='INFO', log_file=None, max_bytes=10*1024*1024, backup_count=5):
    """
    配置日志系统
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，None则只输出到控制台
        max_bytes: 单个日志文件最大字节数
        backup_count: 保留的日志文件备份数量
    """
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除已有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(funcName)-20s | %(lineno)-4d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件输出（如果指定了文件路径）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        # 使用RotatingFileHandler实现日志轮转
        file_handler = RotatingFileHandler(
            log_file, 
            maxBytes=max_bytes, 
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
    return logger

def log_function_entry(func_name, **kwargs):
    """记录函数入口日志"""
    params_str = ', '.join([f"{k}={v}" for k, v in kwargs.items()])
    logging.info(f"🔵 ENTRY: {func_name}({params_str})")

def log_function_exit(func_name, result=None, duration=None):
    """记录函数出口日志"""
    duration_str = f" | Duration: {duration:.3f}s" if duration else ""
    result_str = f" | Result: {result}" if result is not None else ""
    logging.info(f"🔴 EXIT:  {func_name}{result_str}{duration_str}")

def log_exception(func_name, exception, context=None):
    """记录异常日志"""
    context_str = f" | Context: {context}" if context else ""
    logging.error(f"❌ EXCEPTION in {func_name}: {str(exception)}{context_str}")
    logging.error(f"📋 TRACEBACK:\n{traceback.format_exc()}")

def log_state_change(field, old_value, new_value, context=None):
    """记录状态变化日志"""
    context_str = f" | Context: {context}" if context else ""
    logging.info(f"🔄 STATE_CHANGE: {field} | {old_value} → {new_value}{context_str}")

def log_order_operation(operation, symbol, order_data, result=None):
    """记录订单操作日志"""
    logging.info(f"📋 ORDER_{operation.upper()}: {symbol} | Data: {order_data}")
    if result:
        logging.info(f"✅ ORDER_RESULT: {result}")

def log_status_summary(strategy_instance):
    """记录策略状态摘要"""
    summary = {
        'symbol': strategy_instance.symbol,
        'entry_price': strategy_instance.entry,
        'quantity': strategy_instance.qty,
        'stop_order_id': strategy_instance.stop_order_id,
        'take_profit_order_id': strategy_instance.take_profit_order_id,
        'max_profit': strategy_instance.max_profit,
        'position_opened_time': strategy_instance.position_opened_time
    }
    logging.info(f"📊 STATUS_SUMMARY: {summary}")

def get(path, params=None):
    start_time = time.time()
    params = params or {}
    log_function_entry("API_GET", path=path, params=params)
    
    try:
        from hashlib import sha256
        import hmac
        from urllib.parse import urlencode
        
        # Add timestamp and signature for private endpoints
        params['timestamp'] = int(time.time() * 1000)
        query = urlencode(params)
        params['signature'] = hmac.new(SECRET.encode(), query.encode(), sha256).hexdigest()
        
        # Rebuild query with signature
        query = urlencode(params)
        url = BASE + path + ('?' + query if query else '')
        r = session.get(url).json()
        
        duration = time.time() - start_time
        
        if 'code' in r:
            log_exception("API_GET", RuntimeError(r), context=f"URL: {url}")
            raise RuntimeError(r)
            
        log_function_exit("API_GET", result="SUCCESS", duration=duration)
        return r
        
    except Exception as e:
        duration = time.time() - start_time
        log_exception("API_GET", e, context=f"URL: {BASE + path}")
        log_function_exit("API_GET", result="FAILED", duration=duration)
        raise

def post(path, params):
    log_function_entry("API_POST", path=path, params={k: v for k, v in params.items() if k != 'signature'})
    
    try:
        from hashlib import sha256
        import hmac
        start_time = time.time()
        params['timestamp'] = int(time.time()*1000)
        query = '&'.join([f"{k}={v}" for k,v in params.items()])
        params['signature'] = hmac.new(SECRET.encode(), query.encode(), sha256).hexdigest()
        r = session.post(BASE + path, data=params).json()
        
        duration = time.time() - start_time
        
        if 'code' in r:
            log_exception("API_POST", RuntimeError(r), context=f"Path: {path}")
            raise RuntimeError(r)
            
        log_function_exit("API_POST", result="SUCCESS", duration=duration)
        return r
        
    except Exception as e:
        duration = time.time() - start_time
        log_exception("API_POST", e, context=f"Path: {path}")
        log_function_exit("API_POST", result="FAILED", duration=duration)
        raise

# ---------- 策略 ----------
class LightNewCoinBreakout:
    def __init__(self, log_level='INFO', log_file='logs/strategy_enhanced.log'):
        # 初始化日志系统
        self.logger = setup_logger(log_level, log_file)
        
        # 策略状态变量
        self.symbol = None
        self.entry = None
        self.qty = None
        self.day = None
        self.stop_order_id = None  # 跟踪止损单ID
        self.take_profit_order_id = None  # 跟踪止盈单ID
        self.position_opened_time = None  # 开仓时间
        self.max_profit = 0  # 最大盈利
        
        # 手动干预文件路径
        self.manual_symbol_file = "manual_symbol.txt"
        
        # 记录策略启动
        self.start_time = dt.datetime.now(dt.timezone.utc)
        logging.info("🚀 STRATEGY_START: LightNewCoinBreakout Enhanced Logging Version")
        logging.info(f"📅 START_TIME: {self.start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        logging.info(f"📊 LOG_LEVEL: {log_level}")
        logging.info(f"📁 LOG_FILE: {log_file}")
        
        # 启动时立即执行币种选择
        logging.info("🎯 STARTUP_COIN_SELECTION: Executing initial coin selection")
        startup_symbol = self.pick()
        if startup_symbol:
            self.symbol = startup_symbol
            logging.info(f'✅ STARTUP_PICK_SUCCESS: Selected {self.symbol} at startup')
        else:
            logging.warning("⚠️ STARTUP_PICK_FAILED: No suitable coin found at startup")
        
        log_status_summary(self)

    def format_quantity(self, qty, step_size):
        """精确格式化数量，避免精度问题"""
        step_decimal = Decimal(str(step_size))
        qty_decimal = Decimal(str(qty))
        return float(qty_decimal.quantize(step_decimal, rounding=ROUND_DOWN))

    def format_price(self, price, tick_size):
        """精确格式化价格，避免精度问题"""
        tick_decimal = Decimal(str(tick_size))
        price_decimal = Decimal(str(price))
        return float(price_decimal.quantize(tick_decimal, rounding=ROUND_DOWN))

    def cancel_order(self, order_id):
        """取消订单"""
        start_time = time.time()
        log_function_entry("cancel_order", order_id=order_id, symbol=self.symbol)
        
        try:
            if order_id:
                order_data = {'symbol': self.symbol, 'orderId': order_id}
                log_order_operation("CANCEL", self.symbol, order_data)
                
                result = post('/fapi/v1/order', order_data)
                
                duration = time.time() - start_time
                log_order_operation("CANCEL", self.symbol, order_data, result)
                log_function_exit("cancel_order", result=True, duration=duration)
                logging.info(f'✅ ORDER_CANCELLED: {order_id} for {self.symbol}')
                return True
        except Exception as e:
            duration = time.time() - start_time
            log_exception("cancel_order", e, context=f"order_id={order_id}, symbol={self.symbol}")
            log_function_exit("cancel_order", result=False, duration=duration)
            return False

    def get_position_info(self):
        """获取当前持仓信息"""
        start_time = time.time()
        log_function_entry("get_position_info", symbol=self.symbol)
        
        try:
            positions = get('/fapi/v2/positionRisk')
            for pos in positions:
                if pos['symbol'] == self.symbol:
                    position_info = {
                        'size': float(pos['positionAmt']),
                        'entry_price': float(pos['entryPrice']),
                        'unrealized_pnl': float(pos['unRealizedProfit']),
                        'percentage': float(pos['percentage'])
                    }
                    
                    duration = time.time() - start_time
                    log_function_exit("get_position_info", result=position_info, duration=duration)
                    logging.info(f"📊 POSITION_INFO: {position_info}")
                    return position_info
                    
            duration = time.time() - start_time
            log_function_exit("get_position_info", result=None, duration=duration)
            return None
            
        except Exception as e:
            duration = time.time() - start_time
            log_exception("get_position_info", e, context=f"symbol={self.symbol}")
            log_function_exit("get_position_info", result=None, duration=duration)
            return None

    # 1. 每日选币
    def pick(self):
        start_time = time.time()
        log_function_entry("pick")
        
        try:
            logging.info("🔍 COIN_SELECTION: Starting daily coin selection process")
            
            tickers = get('/fapi/v1/ticker/24hr')
            exch = get('/fapi/v1/exchangeInfo')
            
            # 修复时区问题
            current_time = pd.Timestamp.utcnow()
            logging.info(f"⏰ CURRENT_TIME: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            
            age = {}
            for s in exch['symbols']:
                if s['status'] == 'TRADING' and s['symbol'].endswith('USDT'):
                    onboard_time = pd.to_datetime(s['onboardDate'], unit='ms', utc=True)
                    age[s['symbol']] = (current_time - onboard_time).days
            
            logging.info(f"📈 FILTERING: Found {len(age)} USDT trading pairs")
            
            cand = []
            for t in tickers:
                sym = t['symbol']
                if sym not in age or age[sym] > 30: continue
                chg = float(t['priceChangePercent'])
                vol = float(t['quoteVolume'])
                if 10 <= chg <= 50 and vol >= 200000:
                    cand.append((sym, vol))
            
            logging.info(f"🎯 CANDIDATES: Found {len(cand)} coins matching criteria (age≤30d, 10%≤change≤50%, vol≥200k)")
            
            if not cand:
                duration = time.time() - start_time
                log_function_exit("pick", result=None, duration=duration)
                logging.warning("⚠️ NO_CANDIDATES: No coins match selection criteria")
                return None
                
            selected_coin = sorted(cand, key=lambda x: x[1], reverse=True)[0][0]
            duration = time.time() - start_time
            
            log_function_exit("pick", result=selected_coin, duration=duration)
            logging.info(f"🏆 COIN_SELECTED: {selected_coin} (highest volume among candidates)")
            
            return selected_coin
            
        except Exception as e:
            duration = time.time() - start_time
            log_exception("pick", e)
            log_function_exit("pick", result=None, duration=duration)
            return None

    # 2. 下单工具
    def order(self, side, qty, price=None, order_type='MARKET', stop_price=None):
        start_time = time.time()
        log_function_entry("order", side=side, qty=qty, price=price, order_type=order_type, stop_price=stop_price)
        
        try:
            logging.info(f"📋 ORDER_PREPARATION: {order_type} {side} {qty} {self.symbol}")
            
            info = get('/fapi/v1/exchangeInfo', {'symbol': self.symbol})
            f = {x['filterType']: x for x in info['symbols'][0]['filters']}
            tick = float(f['PRICE_FILTER']['tickSize'])
            step = float(f['LOT_SIZE']['stepSize'])
            min_notional = float(f['MIN_NOTIONAL']['notional'])
            
            logging.info(f"📊 EXCHANGE_INFO: tick={tick}, step={step}, min_notional={min_notional}")
            
            # 精确格式化数量
            original_qty = qty
            qty = self.format_quantity(qty, step)
            if original_qty != qty:
                log_state_change("quantity", original_qty, qty, "precision formatting")
            
            # 检查最小名义价值
            if price:
                notional = qty * price
            else:
                # 市价单使用当前价格估算
                ticker = get('/fapi/v1/ticker/price', {'symbol': self.symbol})
                current_price = float(ticker['price'])
                notional = qty * current_price
                
            logging.info(f"💰 NOTIONAL_CHECK: {notional} (min: {min_notional})")
                
            if notional < min_notional:
                duration = time.time() - start_time
                log_function_exit("order", result=None, duration=duration)
                logging.warning(f'⚠️ ORDER_REJECTED: Notional {notional} below minimum {min_notional}')
                return None
            
            params = {
                'symbol': self.symbol,
                'side': side,
                'type': order_type,
                'quantity': f"{qty:.8f}".rstrip('0').rstrip('.')
            }
            
            if order_type == 'LIMIT' and price:
                original_price = price
                price = self.format_price(price, tick)
                if original_price != price:
                    log_state_change("price", original_price, price, "precision formatting")
                params['price'] = f"{price:.8f}".rstrip('0').rstrip('.')
                params['timeInForce'] = 'GTC'
            elif order_type == 'STOP_MARKET' and stop_price:
                original_stop = stop_price
                stop_price = self.format_price(stop_price, tick)
                if original_stop != stop_price:
                    log_state_change("stop_price", original_stop, stop_price, "precision formatting")
                params['stopPrice'] = f"{stop_price:.8f}".rstrip('0').rstrip('.')
            elif order_type == 'TAKE_PROFIT_MARKET' and stop_price:
                original_stop = stop_price
                stop_price = self.format_price(stop_price, tick)
                if original_stop != stop_price:
                    log_state_change("stop_price", original_stop, stop_price, "precision formatting")
                params['stopPrice'] = f"{stop_price:.8f}".rstrip('0').rstrip('.')
            
            log_order_operation("CREATE", self.symbol, params)
            result = post('/fapi/v1/order', params)
            
            duration = time.time() - start_time
            log_order_operation("CREATE", self.symbol, params, result)
            log_function_exit("order", result=result, duration=duration)
            
            if result:
                logging.info(f"✅ ORDER_SUCCESS: {result.get('orderId')} - {order_type} {side} {qty} @ {price or stop_price or 'MARKET'}")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            log_exception("order", e, context=f"side={side}, qty={qty}, type={order_type}")
            log_function_exit("order", result=None, duration=duration)
            return None

    # 3. 每日 09:30 选币 + 14:25 平仓
    def daily(self):
        """每日检查和币种选择 - 增强版多时间窗口"""
        start_time = time.time()
        log_function_entry("daily")
        
        now = dt.datetime.now(dt.timezone.utc)
        logging.info(f"⏰ DAILY_CHECK: Current time {now.strftime('%H:%M UTC')}")
        
        # 强制平仓时间 (UTC 06:25 = 北京时间 14:25)
        if now.hour == 6 and now.minute == 25:
            logging.info("🕕 DAILY_CLOSE_TIME: Executing daily position close")
            if self.entry:
                self.close_position("Daily close")
            self.reset_position()
            log_function_exit("daily", result="daily_close_executed", duration=time.time() - start_time)
            return
            
        # 多时间窗口选币机制
        coin_selection_windows = [
            (1, 30, "主选币时间 (北京时间 09:30)"),    # UTC 01:30 = 北京时间 09:30
            (0, 0, "亚洲早盘 (北京时间 08:00)"),      # UTC 00:00 = 北京时间 08:00
            (6, 0, "欧洲早盘 (北京时间 14:00)"),      # UTC 06:00 = 北京时间 14:00
            (12, 0, "欧洲午盘/美洲早盘 (北京时间 20:00)"), # UTC 12:00 = 北京时间 20:00
            (18, 0, "美洲午盘 (北京时间 02:00)"),     # UTC 18:00 = 北京时间 02:00
            (20, 0, "美洲晚盘 (北京时间 04:00)")      # UTC 20:00 = 北京时间 04:00
        ]
        
        # 检查是否在任何选币时间窗口内
        for hour, minute, description in coin_selection_windows:
            if now.hour == hour and now.minute == minute:
                logging.info(f"🕘 COIN_SELECTION_TIME: {description}")
                
                # 如果没有选中的币种，或者是主选币时间，执行选币
                should_select = (self.symbol is None) or (hour == 1 and minute == 30)
                
                if should_select:
                    # 如果有持仓，先平仓
                    if self.entry:
                        logging.info("📤 CLOSING_EXISTING_POSITION: Before new coin selection")
                        self.close_position("New coin selection")
                        
                    old_symbol = self.symbol
                    self.reset_position()
                    
                    new_symbol = self.pick()
                    if new_symbol != old_symbol:
                        log_state_change("symbol", old_symbol, new_symbol, f"coin selection - {description}")
                    
                    self.symbol = new_symbol
                    if self.symbol:
                        logging.info(f'🎯 COIN_SELECTED: Selected {self.symbol} during {description}')
                    else:
                        logging.warning(f"⚠️ NO_COIN_SELECTED: No suitable coin found during {description}")
                        
                    # 只在主选币时间更新日期
                    if hour == 1 and minute == 30:
                        self.day = now.date()
                        log_state_change("day", None, self.day, "daily reset")
                else:
                    logging.info(f"ℹ️ COIN_ALREADY_SELECTED: Skipping selection, {self.symbol} already active")
                
                break
                
        duration = time.time() - start_time
        log_function_exit("daily", result="completed", duration=duration)

    def check_manual_intervention(self):
        """检查手动干预文件，允许交易员手动指定币种"""
        try:
            if os.path.exists(self.manual_symbol_file):
                with open(self.manual_symbol_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip().upper()
                    
                if content and content != self.symbol:
                    # 验证币种格式
                    if content.endswith('USDT') and len(content) > 4:
                        old_symbol = self.symbol
                        
                        # 如果有持仓，先平仓
                        if self.entry:
                            logging.info("📤 MANUAL_INTERVENTION_CLOSE: Closing position for manual symbol change")
                            self.close_position("Manual intervention")
                            
                        self.reset_position()
                        self.symbol = content
                        
                        logging.info(f'🎯 MANUAL_INTERVENTION: Symbol changed from {old_symbol} to {self.symbol}')
                        log_state_change("symbol", old_symbol, self.symbol, "manual intervention")
                        
                        # 删除干预文件，避免重复执行
                        os.remove(self.manual_symbol_file)
                        logging.info(f'🗑️ MANUAL_FILE_REMOVED: {self.manual_symbol_file} deleted after processing')
                        
                        return True
                    else:
                        logging.warning(f"⚠️ INVALID_MANUAL_SYMBOL: {content} is not a valid USDT pair format")
                        
        except Exception as e:
            logging.error(f"💥 MANUAL_INTERVENTION_ERROR: {e}")
            
        return False
 
    def reset_position(self):
        """重置持仓状态"""
        start_time = time.time()
        log_function_entry("reset_position")
        
        old_state = {
            'symbol': self.symbol,
            'entry': self.entry,
            'qty': self.qty,
            'stop_order_id': self.stop_order_id,
            'take_profit_order_id': self.take_profit_order_id,
            'position_opened_time': self.position_opened_time,
            'max_profit': self.max_profit
        }
        
        logging.info("🔄 POSITION_RESET: Clearing all position data")
        
        self.symbol = None
        self.entry = None
        self.qty = None
        self.stop_order_id = None
        self.take_profit_order_id = None
        self.position_opened_time = None
        self.max_profit = 0
        
        new_state = {
            'symbol': self.symbol,
            'entry': self.entry,
            'qty': self.qty,
            'stop_order_id': self.stop_order_id,
            'take_profit_order_id': self.take_profit_order_id,
            'position_opened_time': self.position_opened_time,
            'max_profit': self.max_profit
        }
        
        log_state_change("position_state", old_state, new_state, "position reset")
        
        duration = time.time() - start_time
        log_function_exit("reset_position", result="completed", duration=duration)
        log_status_summary(self)

    def close_position(self, reason="Manual close"):
        """平仓并清理所有相关订单"""
        start_time = time.time()
        log_function_entry("close_position", reason=reason, symbol=self.symbol, qty=self.qty)
        
        try:
            if self.qty and self.symbol:
                logging.info(f"📤 POSITION_CLOSING: {reason} - {self.symbol} qty={self.qty}")
                
                # 取消所有挂单
                if self.stop_order_id:
                    logging.info(f"❌ CANCELLING_STOP_ORDER: {self.stop_order_id}")
                    self.cancel_order(self.stop_order_id)
                    log_state_change("stop_order_id", self.stop_order_id, None, "order cancelled")
                    self.stop_order_id = None
                    
                if self.take_profit_order_id:
                    logging.info(f"❌ CANCELLING_TP_ORDER: {self.take_profit_order_id}")
                    self.cancel_order(self.take_profit_order_id)
                    log_state_change("take_profit_order_id", self.take_profit_order_id, None, "order cancelled")
                    self.take_profit_order_id = None
                
                # 市价平仓
                logging.info(f"💸 MARKET_SELL: Closing position {self.qty} {self.symbol}")
                result = self.order('SELL', self.qty, order_type='MARKET')
                
                if result:
                    # 计算盈亏
                    if self.entry:
                        current_price = float(get('/fapi/v1/ticker/price', {'symbol': self.symbol})['price'])
                        pnl_pct = (current_price - self.entry) / self.entry * 100
                        pnl_amount = (current_price - self.entry) * self.qty
                        
                        logging.info(f"💰 PNL_CALCULATION: Entry={self.entry:.6f}, Exit={current_price:.6f}")
                        logging.info(f"📊 PNL_RESULT: {pnl_pct:.2f}% | ${pnl_amount:.2f}")
                        
                        # 记录持仓时间
                        if self.position_opened_time:
                            holding_duration = dt.datetime.now(dt.timezone.utc) - self.position_opened_time
                            logging.info(f"⏱️ HOLDING_TIME: {holding_duration}")
                    
                    duration = time.time() - start_time
                    log_function_exit("close_position", result="success", duration=duration)
                    logging.info(f'✅ POSITION_CLOSED: {reason} - Order ID: {result.get("orderId")}')
                else:
                    duration = time.time() - start_time
                    log_function_exit("close_position", result="failed", duration=duration)
                    logging.error(f'❌ CLOSE_FAILED: Market sell order failed')
                    
        except Exception as e:
            duration = time.time() - start_time
            log_exception("close_position", e, context=f"reason={reason}, symbol={self.symbol}")
            log_function_exit("close_position", result="error", duration=duration)

    def manage_position(self):
        """动态管理持仓 - 移动止损和止盈"""
        start_time = time.time()
        log_function_entry("manage_position", symbol=self.symbol, entry=self.entry)
        
        if not self.entry or not self.symbol:
            duration = time.time() - start_time
            log_function_exit("manage_position", result="no_position", duration=duration)
            return
            
        try:
            logging.info(f"🔧 POSITION_MANAGEMENT: Managing {self.symbol} position")
            
            # 获取当前价格
            ticker = get('/fapi/v1/ticker/price', {'symbol': self.symbol})
            current_price = float(ticker['price'])
            
            # 计算当前盈亏
            current_profit_pct = (current_price - self.entry) / self.entry * 100
            
            logging.info(f"📈 CURRENT_PROFIT: {current_profit_pct:.2f}% (Entry: {self.entry:.6f}, Current: {current_price:.6f})")
            
            # 更新最大盈利
            old_max_profit = self.max_profit
            if current_profit_pct > self.max_profit:
                self.max_profit = current_profit_pct
                log_state_change("max_profit", old_max_profit, self.max_profit, "profit update")
                
            # 移动止损逻辑：盈利超过6%时，止损移动到盈利3%
            if current_profit_pct > 6 and self.max_profit > 6:
                new_stop_price = self.entry * 1.03  # 保护3%盈利
                
                logging.info(f"📊 TRAILING_STOP_TRIGGER: Profit {current_profit_pct:.2f}% > 6%, moving stop to protect 3%")
                
                # 取消旧止损单，设置新止损单
                if self.stop_order_id:
                    logging.info(f"❌ CANCELLING_OLD_STOP: {self.stop_order_id}")
                    self.cancel_order(self.stop_order_id)
                    log_state_change("stop_order_id", self.stop_order_id, None, "trailing stop update")
                    
                stop_result = self.order('SELL', self.qty, order_type='STOP_MARKET', stop_price=new_stop_price)
                if stop_result:
                    old_stop_id = self.stop_order_id
                    self.stop_order_id = stop_result.get('orderId')
                    log_state_change("stop_order_id", old_stop_id, self.stop_order_id, "trailing stop set")
                    logging.info(f'✅ TRAILING_STOP_SET: {new_stop_price:.6f} (3% profit protection) - Order ID: {self.stop_order_id}')
            
            # 分批止盈：盈利达到15%时，平仓50%
            if current_profit_pct > 15 and not self.take_profit_order_id:
                logging.info(f"💰 PARTIAL_PROFIT_TRIGGER: Profit {current_profit_pct:.2f}% > 15%, taking 50% profit")
                
                partial_qty = self.qty * 0.5
                tp_result = self.order('SELL', partial_qty, order_type='MARKET')
                if tp_result:
                    old_qty = self.qty
                    self.qty -= partial_qty  # 更新剩余数量
                    log_state_change("quantity", old_qty, self.qty, "partial profit taking")
                    logging.info(f'✅ PARTIAL_PROFIT_TAKEN: 50% at {current_profit_pct:.2f}% profit - Remaining qty: {self.qty}')
                    
                    # 更新止损单数量
                    if self.stop_order_id:
                        logging.info(f"🔄 UPDATING_STOP_QUANTITY: Adjusting for remaining position")
                        self.cancel_order(self.stop_order_id)
                        stop_result = self.order('SELL', self.qty, order_type='STOP_MARKET', 
                                               stop_price=self.entry * 0.97)
                        if stop_result:
                            old_stop_id = self.stop_order_id
                            self.stop_order_id = stop_result.get('orderId')
                            log_state_change("stop_order_id", old_stop_id, self.stop_order_id, "quantity adjustment")
                            logging.info(f'✅ STOP_UPDATED: New quantity {self.qty} - Order ID: {self.stop_order_id}')
            
            # 持仓时间管理：超过4小时强制平仓
            if self.position_opened_time:
                holding_time = dt.datetime.now(dt.timezone.utc) - self.position_opened_time
                holding_hours = holding_time.total_seconds() / 3600
                
                logging.info(f"⏱️ HOLDING_TIME_CHECK: {holding_hours:.2f} hours (max: 4.0)")
                
                if holding_time.total_seconds() > 4 * 3600:  # 4小时
                    logging.info("⏰ MAX_HOLDING_TIME: 4 hours reached, forcing position close")
                    self.close_position("Max holding time reached")
                    self.reset_position()
                    
            duration = time.time() - start_time
            log_function_exit("manage_position", result="completed", duration=duration)
            log_status_summary(self)
                    
        except Exception as e:
            duration = time.time() - start_time
            log_exception("manage_position", e, context=f"symbol={self.symbol}, entry={self.entry}")
            log_function_exit("manage_position", result="error", duration=duration)

    # 4. 3m 突破
    def run(self):
        start_time = time.time()
        log_function_entry("run")
        
        logging.info('🚀 STRATEGY_MAIN_LOOP: LightNewCoinBreakout Fixed Version start')
        
        try:
            while True:
                try:
                    loop_start = time.time()
                    
                    # 检查手动干预
                    if self.check_manual_intervention():
                        logging.info("🎯 MANUAL_INTERVENTION_PROCESSED: Symbol changed by manual intervention")
                    
                    self.daily()
                    
                    # 如果有持仓，进行持仓管理
                    if self.entry:
                        self.manage_position()
                    
                    if not self.symbol: 
                        logging.debug("⏳ WAITING: No symbol selected, sleeping 30s")
                        time.sleep(30)
                        continue
                        
                    # 防止重复开仓
                    if self.entry is not None:
                        logging.debug("📊 POSITION_EXISTS: Already in position, managing existing position")
                        time.sleep(30)
                        continue
                    
                    logging.info(f"🔍 BREAKOUT_CHECK: Analyzing {self.symbol} for breakout signals")
                    
                    kl = get('/fapi/v1/klines', {'symbol': self.symbol, 'interval': '3m', 'limit': 21})
                    if len(kl) < 21: 
                        logging.warning(f"⚠️ INSUFFICIENT_DATA: Only {len(kl)} K-lines available, need 21")
                        time.sleep(30)
                        continue
                        
                    hh = max(float(k[2]) for k in kl[-21:-1])  # 前20根最高价
                    close = float(kl[-1][4])  # 当前收盘价
                    volume = float(kl[-1][5])  # 当前成交量
                    
                    # 增加成交量确认
                    avg_volume = sum(float(k[5]) for k in kl[-10:]) / 10  # 前10根平均成交量
                    
                    logging.info(f"📊 MARKET_DATA: Price={close:.6f}, HighestHigh={hh:.6f}, Volume={volume:.0f}, AvgVol={avg_volume:.0f}")
                    
                    # 突破条件：价格突破 + 成交量放大
                    price_breakout = close > hh
                    volume_confirmation = volume > avg_volume * 1.5
                    
                    logging.info(f"🎯 BREAKOUT_ANALYSIS: Price breakout={price_breakout}, Volume confirmation={volume_confirmation}")
                    
                    if price_breakout and volume_confirmation:
                        logging.info(f'🚨 BREAKOUT_DETECTED: {self.symbol} price={close:.6f} > high={hh:.6f}, volume={volume:.0f} > {avg_volume*1.5:.0f}')
                        
                        # 设置杠杆
                        try:
                            logging.info("⚙️ SETTING_LEVERAGE: Configuring 3x leverage")
                            lev = post('/fapi/v1/leverage', {'symbol': self.symbol, 'leverage': 3})
                            logging.info(f'✅ LEVERAGE_SET: {lev}')
                        except Exception as e:
                            log_exception("leverage_setting", e, context=f"symbol={self.symbol}")
                            logging.error(f'❌ LEVERAGE_FAILED: {e}')
                            time.sleep(30)
                            continue
                        
                        # 获取余额
                        try:
                            logging.info("💰 BALANCE_CHECK: Retrieving USDT balance")
                            balance_info = get('/fapi/v2/balance')
                            usdt_balance = None
                            for b in balance_info:
                                if b['asset'] == 'USDT':
                                    usdt_balance = float(b['balance'])
                                    break
                            
                            logging.info(f"💵 BALANCE_INFO: USDT balance = {usdt_balance}")
                            
                            if not usdt_balance or usdt_balance < 10:
                                logging.warning(f'⚠️ INSUFFICIENT_BALANCE: {usdt_balance} < 10 USDT minimum')
                                time.sleep(30)
                                continue
                                
                        except Exception as e:
                            log_exception("balance_check", e)
                            logging.error(f'❌ BALANCE_CHECK_FAILED: {e}')
                            time.sleep(30)
                            continue
                        
                        # 计算开仓数量 (使用95%资金，留5%缓冲)
                        qty = usdt_balance * 0.95 / close
                        logging.info(f"📊 POSITION_CALCULATION: Using {usdt_balance * 0.95:.2f} USDT (95%) for {qty:.6f} {self.symbol}")
                        
                        # 执行开仓
                        logging.info(f"📈 OPENING_POSITION: Market BUY {qty:.6f} {self.symbol}")
                        order_result = self.order('BUY', qty, order_type='MARKET')
                        
                        if order_result and order_result.get('status') in ['FILLED', 'NEW']:
                            old_entry = self.entry
                            old_qty = self.qty
                            
                            self.entry = close
                            self.qty = qty
                            self.position_opened_time = dt.datetime.now(dt.timezone.utc)
                            
                            log_state_change("entry_price", old_entry, self.entry, "position opened")
                            log_state_change("quantity", old_qty, self.qty, "position opened")
                            log_state_change("position_opened_time", None, self.position_opened_time, "position opened")
                            
                            logging.info(f'✅ POSITION_OPENED: {self.symbol} {qty:.6f} @ {close:.6f} at {self.position_opened_time.strftime("%H:%M:%S")}')
                            
                            # 设置止损单 (3%止损)
                            stop_price = close * 0.97
                            logging.info(f"🛡️ SETTING_STOP_LOSS: 3% stop at {stop_price:.6f}")
                            stop_result = self.order('SELL', qty, order_type='STOP_MARKET', stop_price=stop_price)
                            if stop_result:
                                self.stop_order_id = stop_result.get('orderId')
                                log_state_change("stop_order_id", None, self.stop_order_id, "stop loss set")
                                logging.info(f'✅ STOP_LOSS_SET: {stop_price:.6f} - Order ID: {self.stop_order_id}')
                            
                            # 设置止盈单 (10%止盈)
                            tp_price = close * 1.10
                            logging.info(f"🎯 SETTING_TAKE_PROFIT: 10% target at {tp_price:.6f}")
                            tp_result = self.order('SELL', qty, order_type='TAKE_PROFIT_MARKET', stop_price=tp_price)
                            if tp_result:
                                self.take_profit_order_id = tp_result.get('orderId')
                                log_state_change("take_profit_order_id", None, self.take_profit_order_id, "take profit set")
                                logging.info(f'✅ TAKE_PROFIT_SET: {tp_price:.6f} - Order ID: {self.take_profit_order_id}')
                            
                            log_status_summary(self)
                            
                        else:
                            logging.error(f'❌ POSITION_OPEN_FAILED: {order_result}')
                    
                    loop_duration = time.time() - loop_start
                    logging.debug(f"🔄 LOOP_COMPLETED: Duration {loop_duration:.3f}s, sleeping 30s")
                    time.sleep(30)
                    
                except KeyboardInterrupt:
                    logging.info('⏹️ USER_INTERRUPT: Strategy stopped by user')
                    if self.entry:
                        logging.info("📤 EMERGENCY_CLOSE: Closing position due to user interrupt")
                        self.close_position("User interrupt")
                    
                    # 记录策略停止时间
                    stop_time = dt.datetime.now(dt.timezone.utc)
                    total_runtime = stop_time - self.start_time
                    logging.info(f"🏁 STRATEGY_STOP: {stop_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                    logging.info(f"⏱️ TOTAL_RUNTIME: {total_runtime}")
                    
                    duration = time.time() - start_time
                    log_function_exit("run", result="user_interrupt", duration=duration)
                    break
                    
                except Exception as e:
                    log_exception("main_loop", e, context=f"symbol={self.symbol}")
                    logging.error(f'❌ MAIN_LOOP_ERROR: {e}')
                    logging.info("⏳ ERROR_RECOVERY: Sleeping 60s before retry")
                    time.sleep(60)
                    
        except Exception as e:
            duration = time.time() - start_time
            log_exception("run", e)
            log_function_exit("run", result="fatal_error", duration=duration)
            
            # 记录策略异常停止
            stop_time = dt.datetime.now(dt.timezone.utc)
            total_runtime = stop_time - self.start_time
            logging.error(f"💥 STRATEGY_FATAL_ERROR: {stop_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            logging.error(f"⏱️ RUNTIME_BEFORE_ERROR: {total_runtime}")
            raise

if __name__ == '__main__':
    try:
        strategy = LightNewCoinBreakout()
        strategy.run()
    except Exception as e:
        logging.error(f"💥 STRATEGY_INITIALIZATION_ERROR: {e}")
        logging.error(f"📋 FULL_TRACEBACK:\n{traceback.format_exc()}")
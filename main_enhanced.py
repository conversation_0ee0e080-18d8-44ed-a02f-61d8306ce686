import yaml
import json
import time
import logging
from http_client import HttpClient
from strategy.maker_channel_enhanced import MakerChannelEnhanced


def legacy_main_disabled():
    # 关闭旧入口，避免重复启动
    # 旧的 YAML+JSON 合并逻辑已在下方统一实现
    pass


def main():
    # 读取原有 YAML 配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        base_cfg = yaml.safe_load(f)
    # 读取新增 JSON 配置（可选）
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            json_cfg = json.load(f)
    except Exception:
        json_cfg = {}
    # 合并配置（JSON 优先）
    cfg = {**base_cfg, **json_cfg}

    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)s %(name)s: %(message)s')
    http = HttpClient(cfg['api_key'], cfg['api_secret'], cfg.get('base_url', 'https://fapi.binance.com'), cfg.get('verify_ssl', True))
    strat = MakerChannelEnhanced(http, cfg)
    strat.loop()


if __name__ == '__main__':
    # 禁用顶部旧入口
    pass
import logging
import warnings
import urllib3
import os
from datetime import datetime
from strategy.maker_channel_enhanced import MakerChannelEnhanced
from http_client import HttpClient

# 禁用所有警告
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 创建日志目录
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 配置详细的日志记录
log_filename = f"{log_dir}/strategy_enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# 创建格式化器
formatter = logging.Formatter('%(asctime)s [%(name)s] [%(levelname)s] %(message)s')

# 文件处理器
file_handler = logging.FileHandler(log_filename, encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# 配置策略专用日志记录器
strategy_logger = logging.getLogger('StrategyEnhanced')
strategy_logger.setLevel(logging.INFO)
strategy_logger.addHandler(file_handler)
strategy_logger.addHandler(console_handler)

# 配置HTTP客户端日志记录器
http_logger = logging.getLogger('HttpClient')
http_logger.setLevel(logging.INFO)
http_logger.addHandler(file_handler)
http_logger.addHandler(console_handler)

logging.info(f"增强策略日志文件: {log_filename}")

def main():
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        base_cfg = yaml.safe_load(f)
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            json_cfg = json.load(f)
    except Exception:
        json_cfg = {}
    cfg = {**base_cfg, **json_cfg}
    
    http = HttpClient(
        cfg['api_key'],
        cfg['api_secret'],
        base_url=cfg.get('base_url', 'https://fapi.binance.com'),
        verify_ssl=cfg.get('verify_ssl', True)
    )
    
    # 创建增强版策略实例
    strategy = MakerChannelEnhanced(http, cfg)
    
    logging.info("=== 增强版策略启动 ===")
    logging.info("功能特性:")
    logging.info("  - 四级缓存引擎 (TTL+LRU+内存红线)")
    logging.info("  - 双引擎币种加载 (00:10冷启 + WebSocket实时补票)")
    logging.info("  - 3mTick合成Bar")
    logging.info("  - 0.38%回踩限价单")
    logging.info("  - 四重风控 (保本+移动+降档+Top3+止血贴)")
    logging.info("  - 龙头池筛选 (24h涨幅前25+成交额前25)")
    logging.info("  - 增强评分系统 (0-10分)")
    logging.info("=== 策略开始运行 ===")
    
    strategy.loop()

if __name__ == '__main__':
    main()
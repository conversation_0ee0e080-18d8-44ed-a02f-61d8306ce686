#!/usr/bin/env python3
"""
获取PIPPINUSDT的实际交易规则
确定正确的tick_size和step_size
"""

import requests
import json

def get_pippinusdt_trading_rules():
    """获取PIPPINUSDT的实际交易规则"""
    print("=== 获取PIPPINUSDT交易规则 ===")
    
    try:
        # 获取币安期货交易规则
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            # 查找PIPPINUSDT
            for symbol_info in data['symbols']:
                if symbol_info['symbol'] == 'PIPPINUSDT':
                    print(f"找到PIPPINUSDT交易规则:")
                    print(f"  symbol: {symbol_info['symbol']}")
                    print(f"  status: {symbol_info['status']}")
                    print(f"  baseAsset: {symbol_info['baseAsset']}")
                    print(f"  quoteAsset: {symbol_info['quoteAsset']}")
                    
                    # 解析过滤器
                    filters = symbol_info['filters']
                    
                    price_filter = None
                    lot_size_filter = None
                    min_notional_filter = None
                    
                    for f in filters:
                        if f['filterType'] == 'PRICE_FILTER':
                            price_filter = f
                        elif f['filterType'] == 'LOT_SIZE':
                            lot_size_filter = f
                        elif f['filterType'] == 'MIN_NOTIONAL':
                            min_notional_filter = f
                    
                    print(f"\n价格过滤器 (PRICE_FILTER):")
                    if price_filter:
                        tick_size = float(price_filter['tickSize'])
                        print(f"  tickSize: {price_filter['tickSize']} ({tick_size})")
                        print(f"  minPrice: {price_filter['minPrice']}")
                        print(f"  maxPrice: {price_filter['maxPrice']}")
                        
                        # 计算tick_size的小数位数
                        tick_str = price_filter['tickSize']
                        if '.' in tick_str:
                            tick_decimals = len(tick_str.split('.')[1].rstrip('0'))
                        else:
                            tick_decimals = 0
                        print(f"  价格小数位数: {tick_decimals}")
                    
                    print(f"\n数量过滤器 (LOT_SIZE):")
                    if lot_size_filter:
                        step_size = float(lot_size_filter['stepSize'])
                        min_qty = float(lot_size_filter['minQty'])
                        print(f"  stepSize: {lot_size_filter['stepSize']} ({step_size})")
                        print(f"  minQty: {lot_size_filter['minQty']} ({min_qty})")
                        print(f"  maxQty: {lot_size_filter['maxQty']}")
                        
                        # 计算step_size的小数位数
                        step_str = lot_size_filter['stepSize']
                        if '.' in step_str:
                            step_decimals = len(step_str.split('.')[1].rstrip('0'))
                        else:
                            step_decimals = 0
                        print(f"  数量小数位数: {step_decimals}")
                    
                    print(f"\n最小名义价值过滤器 (MIN_NOTIONAL):")
                    if min_notional_filter:
                        min_notional = float(min_notional_filter['notional'])
                        print(f"  minNotional: {min_notional_filter['notional']} ({min_notional})")
                    
                    # 返回关键参数
                    return {
                        'symbol': 'PIPPINUSDT',
                        'tick_size': tick_size if price_filter else None,
                        'step_size': step_size if lot_size_filter else None,
                        'min_qty': min_qty if lot_size_filter else None,
                        'min_notional': min_notional if min_notional_filter else None,
                        'tick_decimals': tick_decimals if price_filter else None,
                        'step_decimals': step_decimals if lot_size_filter else None,
                    }
            
            print("❌ 未找到PIPPINUSDT交易规则")
            return None
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取交易规则失败: {e}")
        return None

def test_with_real_rules():
    """使用真实交易规则测试格式化"""
    print("\n=== 使用真实交易规则测试 ===")
    
    rules = get_pippinusdt_trading_rules()
    if not rules:
        print("无法获取交易规则，使用默认值测试")
        rules = {
            'tick_size': 0.00001,
            'step_size': 0.001,
            'min_qty': 0.001,
            'min_notional': 5.0,
            'tick_decimals': 5,
            'step_decimals': 3,
        }
    
    print(f"\n使用的交易规则:")
    for key, value in rules.items():
        print(f"  {key}: {value}")
    
    # 测试格式化函数
    from decimal import Decimal, ROUND_DOWN, getcontext
    getcontext().prec = 50
    
    def format_price_correct(price, tick_size, tick_decimals):
        """正确的价格格式化"""
        try:
            decimal_price = Decimal(str(price))
            decimal_tick = Decimal(str(tick_size))
            
            if decimal_price <= 0:
                return str(float(price))
            
            # 向下取整到tick_size的倍数
            steps = (decimal_price / decimal_tick).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 如果steps为0但原值大于0，设为1
            if steps == 0 and decimal_price > 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_tick
            
            # 使用正确的小数位数格式化
            if tick_decimals > 0:
                result = f"{float(formatted_value):.{tick_decimals}f}"
                # 移除末尾的0，但保持至少一位有效数字
                result = result.rstrip('0')
                if result.endswith('.'):
                    result += '0'
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"价格格式化失败: {e}")
            return str(float(price))
    
    def format_qty_correct(qty, step_size, step_decimals, min_qty):
        """正确的数量格式化"""
        try:
            actual_qty = max(float(qty), float(min_qty))
            
            decimal_qty = Decimal(str(actual_qty))
            decimal_step = Decimal(str(step_size))
            
            # 向下取整到step_size的倍数
            steps = (decimal_qty / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # 确保至少为1步
            if steps == 0:
                steps = Decimal('1')
            
            # 计算格式化后的值
            formatted_value = steps * decimal_step
            
            # 使用正确的小数位数格式化
            if step_decimals > 0:
                result = f"{float(formatted_value):.{step_decimals}f}"
                result = result.rstrip('0')
                if result.endswith('.'):
                    result = result[:-1]
                return result
            else:
                return str(int(float(formatted_value)))
                
        except Exception as e:
            print(f"数量格式化失败: {e}")
            return str(float(qty))
    
    # 测试PIPPINUSDT的实际参数
    test_price = 0.4
    test_qty = 236.417
    
    print(f"\n测试参数:")
    print(f"  原始价格: {test_price}")
    print(f"  原始数量: {test_qty}")
    
    # 格式化
    price_str = format_price_correct(test_price, rules['tick_size'], rules['tick_decimals'])
    qty_str = format_qty_correct(test_qty, rules['step_size'], rules['step_decimals'], rules['min_qty'])
    
    print(f"\n格式化结果:")
    print(f"  价格: '{price_str}'")
    print(f"  数量: '{qty_str}'")
    
    # 验证
    try:
        price_val = float(price_str)
        qty_val = float(qty_str)
        notional = price_val * qty_val
        
        print(f"\n验证:")
        print(f"  price_val: {price_val}")
        print(f"  qty_val: {qty_val}")
        print(f"  notional: {notional}")
        
        # 检查精度
        if '.' in price_str:
            actual_price_decimals = len(price_str.split('.')[1])
            print(f"  价格小数位数: {actual_price_decimals} (要求≤{rules['tick_decimals']})")
            price_precision_ok = actual_price_decimals <= rules['tick_decimals']
        else:
            price_precision_ok = True
            print(f"  价格小数位数: 0 (整数)")
        
        if '.' in qty_str:
            actual_qty_decimals = len(qty_str.split('.')[1])
            print(f"  数量小数位数: {actual_qty_decimals} (要求≤{rules['step_decimals']})")
            qty_precision_ok = actual_qty_decimals <= rules['step_decimals']
        else:
            qty_precision_ok = True
            print(f"  数量小数位数: 0 (整数)")
        
        # 最终判断
        all_ok = (
            price_val > 0 and
            qty_val > 0 and
            qty_val >= rules['min_qty'] and
            notional >= rules['min_notional'] and
            price_precision_ok and
            qty_precision_ok
        )
        
        print(f"\n最终结果:")
        if all_ok:
            print("✅ 所有检查通过，应该不会出现-1111错误")
        else:
            print("❌ 仍有问题，可能出现-1111错误")
            
        return all_ok, rules
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False, rules

if __name__ == '__main__':
    success, rules = test_with_real_rules()
    
    if success:
        print(f"\n🎉 测试成功！")
        print(f"建议使用以下参数更新代码:")
        print(f"  tick_size: {rules['tick_size']}")
        print(f"  step_size: {rules['step_size']}")
        print(f"  tick_decimals: {rules['tick_decimals']}")
        print(f"  step_decimals: {rules['step_decimals']}")
    else:
        print(f"\n⚠️  需要进一步调试")
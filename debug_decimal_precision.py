#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试Decimal精度计算问题
"""

from decimal import Decimal, ROUND_DOWN

def debug_decimal_formatting():
    """调试Decimal格式化过程"""
    
    # 测试案例：BTCUSDT数量格式化
    value = 0.001234567
    step_size = 1e-05
    
    print(f"原始值: {value}")
    print(f"step_size: {step_size}")
    
    # 转换为Decimal
    decimal_value = Decimal(str(value))
    decimal_step = Decimal(str(step_size))
    
    print(f"Decimal值: {decimal_value}")
    print(f"Decimal step: {decimal_step}")
    
    # 计算步数
    steps = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
    print(f"步数: {steps}")
    
    # 计算格式化后的值
    formatted_decimal = steps * decimal_step
    print(f"格式化后的Decimal: {formatted_decimal}")
    
    # 确定显示精度
    step_str = f"{float(decimal_step):.10f}".rstrip('0').rstrip('.')
    print(f"step_str: {step_str}")
    
    if '.' in step_str:
        decimal_part = step_str.split('.')[1]
        step_decimal_places = len(decimal_part)
        display_precision = min(step_decimal_places, 8)  # 假设max_decimal_places=8
        print(f"step_decimal_places: {step_decimal_places}")
        print(f"display_precision: {display_precision}")
        
        # 使用Decimal的quantize方法
        quantized_decimal = formatted_decimal.quantize(Decimal('0.' + '0' * display_precision))
        result = f"{quantized_decimal:.{display_precision}f}"
        print(f"quantized_decimal: {quantized_decimal}")
        print(f"最终结果: {result}")
        
        # 验证是否为step_size的整数倍
        result_decimal = Decimal(result)
        verification_steps = result_decimal / decimal_step
        print(f"验证: {result} / {decimal_step} = {verification_steps}")
        print(f"是否为整数: {verification_steps == verification_steps.quantize(Decimal('1'))}")
    
    print("\n" + "="*50)
    
    # 测试另一个案例
    print("测试案例2: 更精确的step_size")
    value2 = 0.012345678
    step_size2 = 1e-05
    
    decimal_value2 = Decimal(str(value2))
    decimal_step2 = Decimal(str(step_size2))
    
    steps2 = (decimal_value2 / decimal_step2).quantize(Decimal('1'), rounding=ROUND_DOWN)
    formatted_decimal2 = steps2 * decimal_step2
    
    print(f"原始值: {value2}")
    print(f"步数: {steps2}")
    print(f"格式化后: {formatted_decimal2}")
    
    # 5位小数精度
    quantized_decimal2 = formatted_decimal2.quantize(Decimal('0.00000'))
    result2 = f"{quantized_decimal2:.5f}"
    print(f"5位小数结果: {result2}")
    
    # 验证
    result_decimal2 = Decimal(result2)
    verification_steps2 = result_decimal2 / decimal_step2
    print(f"验证: {result2} / {decimal_step2} = {verification_steps2}")
    print(f"是否为整数: {verification_steps2 == verification_steps2.quantize(Decimal('1'))}")

if __name__ == "__main__":
    debug_decimal_formatting()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试脚本
用于快速验证币安API的访问情况
"""

import requests
import time
import json
from datetime import datetime

def test_basic_network():
    """测试基础网络连接"""
    print("=" * 50)
    print("🌐 基础网络连接测试")
    print("=" * 50)
    
    test_urls = [
        "https://www.google.com",
        "https://www.baidu.com",
        "https://httpbin.org/ip"
    ]
    
    for url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            elapsed = time.time() - start_time
            print(f"✅ {url} - 状态码: {response.status_code}, 耗时: {elapsed:.2f}s")
        except Exception as e:
            print(f"❌ {url} - 错误: {str(e)}")

def test_binance_api():
    """测试币安API连接"""
    print("\n" + "=" * 50)
    print("🏦 币安API连接测试")
    print("=" * 50)
    
    # 币安API端点
    binance_endpoints = [
        {
            "name": "现货API - 服务器时间",
            "url": "https://api.binance.com/api/v3/time",
            "type": "spot"
        },
        {
            "name": "合约API - 服务器时间", 
            "url": "https://fapi.binance.com/fapi/v1/time",
            "type": "futures"
        },
        {
            "name": "合约API - 交易规则",
            "url": "https://fapi.binance.com/fapi/v1/exchangeInfo",
            "type": "futures"
        },
        {
            "name": "合约API - 24h行情",
            "url": "https://fapi.binance.com/fapi/v1/ticker/24hr",
            "type": "futures"
        }
    ]
    
    results = {}
    
    for endpoint in binance_endpoints:
        print(f"\n📡 测试: {endpoint['name']}")
        try:
            start_time = time.time()
            response = requests.get(endpoint['url'], timeout=15)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功 - 状态码: {response.status_code}, 耗时: {elapsed:.2f}s")
                
                # 显示部分响应数据
                if endpoint['type'] == 'futures' and 'time' in endpoint['url']:
                    server_time = data.get('serverTime', 0)
                    local_time = int(time.time() * 1000)
                    time_diff = abs(server_time - local_time)
                    print(f"   服务器时间: {datetime.fromtimestamp(server_time/1000)}")
                    print(f"   本地时间: {datetime.fromtimestamp(local_time/1000)}")
                    print(f"   时间差: {time_diff}ms")
                    
                elif 'exchangeInfo' in endpoint['url']:
                    symbols_count = len(data.get('symbols', []))
                    print(f"   交易对数量: {symbols_count}")
                    
                elif 'ticker/24hr' in endpoint['url']:
                    tickers_count = len(data) if isinstance(data, list) else 1
                    print(f"   行情数据数量: {tickers_count}")
                    
                results[endpoint['name']] = {
                    'status': 'success',
                    'response_time': elapsed,
                    'status_code': response.status_code
                }
            else:
                print(f"❌ 失败 - 状态码: {response.status_code}, 耗时: {elapsed:.2f}s")
                results[endpoint['name']] = {
                    'status': 'failed',
                    'response_time': elapsed,
                    'status_code': response.status_code,
                    'error': f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.Timeout:
            print(f"⏰ 超时 - 请求超过15秒")
            results[endpoint['name']] = {
                'status': 'timeout',
                'error': 'Request timeout (15s)'
            }
        except requests.exceptions.ConnectionError as e:
            print(f"🔌 连接错误: {str(e)}")
            results[endpoint['name']] = {
                'status': 'connection_error',
                'error': str(e)
            }
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            results[endpoint['name']] = {
                'status': 'error',
                'error': str(e)
            }
    
    return results

def test_proxy_connection():
    """测试代理连接"""
    print("\n" + "=" * 50)
    print("🔄 代理连接测试")
    print("=" * 50)
    
    proxy_configs = [
        {
            "name": "HTTP代理 127.0.0.1:7897",
            "proxies": {
                "http": "http://127.0.0.1:7897",
                "https": "http://127.0.0.1:7897"
            }
        },
        {
            "name": "SOCKS5代理 127.0.0.1:7897", 
            "proxies": {
                "http": "socks5://127.0.0.1:7897",
                "https": "socks5://127.0.0.1:7897"
            }
        }
    ]
    
    test_url = "https://fapi.binance.com/fapi/v1/time"
    
    for config in proxy_configs:
        print(f"\n🔄 测试代理: {config['name']}")
        try:
            start_time = time.time()
            response = requests.get(test_url, proxies=config['proxies'], timeout=15)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                server_time = data.get('serverTime', 0)
                print(f"✅ 代理连接成功 - 耗时: {elapsed:.2f}s")
                print(f"   服务器时间: {datetime.fromtimestamp(server_time/1000)}")
            else:
                print(f"❌ 代理连接失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 代理连接错误: {str(e)}")

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 50)
    print("📊 测试报告总结")
    print("=" * 50)
    
    success_count = sum(1 for r in results.values() if r.get('status') == 'success')
    total_count = len(results)
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    
    print(f"总测试项目: {total_count}")
    print(f"成功项目: {success_count}")
    print(f"成功率: {success_rate:.1f}%")
    
    print("\n📋 详细结果:")
    for name, result in results.items():
        status_icon = "✅" if result.get('status') == 'success' else "❌"
        print(f"{status_icon} {name}: {result.get('status', 'unknown')}")
        if 'response_time' in result:
            print(f"   响应时间: {result['response_time']:.2f}s")
        if 'error' in result:
            print(f"   错误信息: {result['error']}")
    
    # 保存详细报告到文件
    report_file = f"network_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_count,
                'successful_tests': success_count,
                'success_rate': success_rate
            },
            'detailed_results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细报告已保存到: {report_file}")
    
    return success_rate >= 50  # 如果成功率>=50%认为网络基本可用

def main():
    """主测试函数"""
    print("🚀 开始网络连接测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 基础网络测试
    test_basic_network()
    
    # 2. 币安API测试
    api_results = test_binance_api()
    
    # 3. 代理连接测试
    test_proxy_connection()
    
    # 4. 生成报告
    network_ok = generate_test_report(api_results)
    
    print("\n" + "=" * 50)
    if network_ok:
        print("🎉 网络连接测试基本通过，可以尝试运行策略程序")
        print("💡 建议: 如果币安API访问正常，可以继续测试策略逻辑")
    else:
        print("⚠️  网络连接存在问题，需要解决网络配置")
        print("💡 建议: 检查代理设置、防火墙配置或网络环境")
    print("=" * 50)

if __name__ == "__main__":
    main()
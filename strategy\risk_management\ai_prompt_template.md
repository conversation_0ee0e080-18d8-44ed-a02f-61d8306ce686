# 五重风控机制 AI 提示词模板

## 🎯 风控目标

请为我的量化交易策略实现一套完整的五重风控机制，包含以下核心组件：

1. **保本止损** - 确保盈利单不变成亏损单
2. **移动止损** - 锁定利润，防止大幅回撤  
3. **降档复位** - 动态调整仓位应对市场波动
4. **Top3筛选** - 只交易最优质的币种
5. **止血贴** - 防止频繁止损造成过度亏损

## 📋 实现要点

### 1. 保本止损机制
```
触发条件：浮盈 >= 5%
执行动作：将止损价调整至成本价 + 0.4%
保护目标：确保盈利单永远不会变成亏损单
实现逻辑：
- 监控当前价格与入场价格的盈亏比例
- 达到5%浮盈时，自动上移止损价至成本价上方0.4%
- 设置标志位防止重复触发
```

### 2. 移动止损机制  
```
触发条件：浮盈 >= 2%
执行动作：止损价跟随当前价格-1%向上移动
保护目标：锁定利润，防止大幅回撤
实现逻辑：
- 浮盈达到2%后启用移动止损
- 止损价 = 当前价格 × 0.99
- 只允许向上移动，不允许向下调整
- 实时更新止损订单
```

### 3. 降档复位机制
```
降档触发条件：
- 价格跌破20日最低价 - ATR
- 市场深度 < 8万USDT  
- ATR > 60日价格标准差 × 2

降档动作：卖出50%仓位

复位触发条件：
- 价格突破20日最高价
- 市场深度 > 20万USDT
- ATR < 60日价格标准差 × 1.5

复位动作：买回之前卖出的仓位
```

### 4. Top3筛选机制
```
刷新频率：每小时一次
选择标准：综合评分最高的币种
评分维度：
- 通道突破强度
- 动量指标
- 成交量放大
- 市场深度
- 币种年龄

筛选逻辑：
- 只持有评分最高的1个币种
- 其他币种自动平仓
- 评分低于门槛时空仓等待
```

### 5. 止血贴机制
```
保护规则：同一币种4小时内最多允许2次止损
冷冻机制：超过限制后自动冷冻4小时
记录逻辑：
- 每次止损都记录到缓存
- 4小时内累计计数
- 超过4小时重置计数
- 开仓前检查冷冻状态
```

## 🔧 关键参数说明

### 基础参数
```python
# 保本止损参数
BREAKEVEN_TRIGGER = 0.05      # 5%浮盈触发保本
BREAKEVEN_STOP = 0.004        # 保本止损位：成本+0.4%

# 移动止损参数  
TRAILING_TRIGGER = 0.02       # 2%浮盈启用移动止损
TRAILING_DISTANCE = 0.01      # 1%移动止损距离

# 降档复位参数
DOWNGRADE_RATIO = 0.5         # 降档比例50%
ATR_PERIOD = 14               # ATR计算周期
MIN_PERIOD = 20               # 支撑阻力计算周期
STD_PERIOD = 60               # 标准差计算周期
DEPTH_DOWNGRADE = 80000       # 降档深度阈值
DEPTH_RESET = 200000          # 复位深度阈值

# 止血贴参数
MAX_STOPS = 2                 # 最大止损次数
FREEZE_HOURS = 4              # 冷冻时间

# Top3筛选参数
REFRESH_INTERVAL = 60         # 刷新间隔(分钟)
SCORE_THRESHOLD = 7.0         # 评分门槛
```

### 技术指标计算
```python
# ATR计算
def calculate_atr(high, low, close, period=14):
    tr = max(high-low, abs(high-prev_close), abs(low-prev_close))
    return tr.rolling(period).mean()

# 支撑阻力计算  
def calculate_support_resistance(df, period=20):
    support = df['low'].rolling(period).min()
    resistance = df['high'].rolling(period).max()
    return support, resistance

# 波动率计算
def calculate_volatility(close, period=60):
    return close.rolling(period).std()
```

## 💡 使用示例

### 基础集成示例
```python
# 1. 导入风控模块
from risk_management.five_layer_risk_control import FiveLayerRiskControl, RiskConfig

# 2. 创建风控配置
config = RiskConfig(
    breakeven_trigger_pct=0.05,
    trailing_trigger_pct=0.02,
    max_stops_per_period=2,
    freeze_hours=4
)

# 3. 初始化风控系统
risk_control = FiveLayerRiskControl(config, logger)

# 4. 在策略中集成
def monitor_position(symbol, current_price):
    # 准备市场数据
    market_data = prepare_market_data(symbol)
    
    # 执行风控监控
    result = risk_control.monitor_position(symbol, current_price, market_data)
    
    # 处理风控决策
    handle_risk_decisions(result)

# 5. 开仓前检查
def can_open_position(symbol):
    return risk_control.can_open_position(symbol)
```

### 快速集成现有策略
```python
# 使用集成器快速添加风控
from risk_management.five_layer_risk_control import RiskControlIntegrator

# 集成到现有策略
integrator = RiskControlIntegrator(existing_strategy)

# 设置持仓信息
integrator.set_position('BTCUSDT', 50000.0, 0.1, 5000.0)
```

## 🚨 重要注意事项

### 1. 数据依赖
- 需要实时价格数据
- 需要K线历史数据（至少60根15分钟K线）
- 需要市场深度数据
- 需要可靠的时间戳

### 2. 订单管理
- 确保止损订单能够正确更新
- 处理订单失败的重试机制
- 考虑网络延迟和滑点影响

### 3. 异常处理
- 数据缺失时的降级处理
- API调用失败的容错机制
- 计算异常时的安全退出

### 4. 性能优化
- 合理使用缓存减少API调用
- 避免频繁的数据库读写
- 优化技术指标计算效率

## 📊 效果评估指标

### 风控效果指标
```
1. 最大回撤控制：目标 < 15%
2. 盈利保护率：盈利单变亏损的比例 < 5%
3. 止损频率：平均每月止损次数 < 10次
4. 冷冻效果：震荡市场中的过度交易减少 > 60%
5. 整体收益：年化收益率提升 > 20%
```

### 监控指标
```
1. 保本止损触发次数
2. 移动止损更新次数  
3. 降档复位操作次数
4. Top1选择变更次数
5. 止血贴冷冻次数
```

## 🔄 持续优化建议

### 1. 参数调优
- 根据不同市场环境调整参数
- 基于历史回测优化阈值设置
- 考虑币种特性的个性化参数

### 2. 策略增强
- 添加市场情绪指标
- 集成宏观经济数据
- 引入机器学习预测模型

### 3. 风控升级
- 增加组合风险管理
- 添加流动性风险控制
- 实现动态风险预算分配

---

**请基于以上模板，为我的策略实现完整的五重风控机制。如果需要针对特定交易所或特定币种进行定制，请告知具体需求。**

# 🚀 策略程序快速测试指南

## 📋 测试概述

本指南提供了一套完整的测试流程，帮助您在服务器上快速验证策略程序的运行状态和交易逻辑。测试分为三个层次：网络连接测试、策略逻辑测试和完整程序测试。

## 🔧 测试环境准备

### 1. 确认文件完整性
```bash
# 检查关键文件是否存在
ls -la main_enhanced.py
ls -la config/config.yaml
ls -la config/config.json
ls -la test_network_connection.py
ls -la test_strategy_quick.py
```

### 2. 安装依赖包
```bash
# 如果缺少依赖，安装必要的包
pip install requests pyyaml pandas
```

## 🌐 第一步：网络连接测试

### 执行网络测试
```bash
python test_network_connection.py
```

### 预期输出解读

#### ✅ 正常情况
```
🌐 基础网络连接测试
==================================================
✅ https://www.google.com - 状态码: 200, 耗时: 1.23s
✅ https://www.baidu.com - 状态码: 200, 耗时: 0.45s

🏦 币安API连接测试
==================================================
📡 测试: 合约API - 服务器时间
✅ 成功 - 状态码: 200, 耗时: 0.89s
   服务器时间: 2024-09-30 16:45:23
   本地时间: 2024-09-30 16:45:23
   时间差: 123ms

📡 测试: 合约API - 24h行情
✅ 成功 - 状态码: 200, 耗时: 1.45s
   行情数据数量: 591

📊 测试报告总结
==================================================
总测试项目: 4
成功项目: 4
成功率: 100.0%

🎉 网络连接测试基本通过，可以尝试运行策略程序
```

#### ❌ 异常情况
```
🏦 币安API连接测试
==================================================
📡 测试: 合约API - 服务器时间
⏰ 超时 - 请求超过15秒

🔄 代理连接测试
==================================================
🔄 测试代理: HTTP代理 127.0.0.1:7897
❌ 代理连接错误: HTTPSConnectionPool(host='fapi.binance.com', port=443)

⚠️  网络连接存在问题，需要解决网络配置
💡 建议: 检查代理设置、防火墙配置或网络环境
```

### 网络问题排查

1. **代理问题**：
   ```bash
   # 检查代理服务是否运行
   netstat -an | grep 7897
   # 或者
   lsof -i :7897
   ```

2. **防火墙问题**：
   ```bash
   # 检查防火墙状态
   sudo ufw status
   # 临时允许出站连接
   sudo ufw allow out 443
   ```

3. **DNS问题**：
   ```bash
   # 测试DNS解析
   nslookup fapi.binance.com
   dig fapi.binance.com
   ```

## 🎯 第二步：策略逻辑测试

### 执行策略测试
```bash
python test_strategy_quick.py
```

### 预期输出解读

#### ✅ 正常情况
```
🚀 开始策略快速测试...
测试时间: 2024-09-30 16:45:00

📋 加载配置文件...
✅ YAML配置加载成功: 25 项
✅ JSON配置加载成功: 4 项

🔧 关键配置参数:
   score_gate: 2.0
   rsi_max: 90
   rsi_min: 30
   vol_mult: 0.5
   ultra_new_coin_score_gate: 1.0

🔌 测试API连接...
✅ 服务器时间同步成功: 2024-09-30 16:45:01

📊 获取样本行情数据...
✅ 获取到 503 个USDT永续合约行情

🎯 模拟币种评分...
📊 评分参数: score_gate=2.0, vol_mult=0.5, RSI范围=[30, 90]
✅ 评分完成，45 个币种满足条件

🚦 模拟入场信号...
📡 信号参数: MAC=[5, 5, 1, 1], Donchian周期=5
✅ 信号生成完成，2/5 个币种产生入场信号

💰 模拟下单流程...
💼 下单参数: 杠杆=10x, 最小金额=10 USDT
✅ 下单模拟完成，生成 2 个模拟订单

📊 策略测试总结报告
============================================================
📈 行情数据: 503 个USDT永续合约
🎯 评分筛选: 45 个币种满足评分条件
🚦 入场信号: 2 个币种产生入场信号
💰 模拟订单: 2 个订单
📊 筛选转化率: 8.95%
📡 信号转化率: 4.44%

🔧 当前配置效果评估:
   ✅ 评估: 筛选条件基本合理
   ✅ 信号: 信号生成频率基本合理

🎉 策略快速测试完成！
```

#### ⚠️ 需要调整的情况

**情况1：筛选过于严格**
```
🎯 模拟币种评分...
✅ 评分完成，0 个币种满足条件
   ⚠️  评估: 筛选条件过于严格，建议进一步降低参数
```
**解决方案**：进一步降低 `score_gate`、`vol_mult` 等参数

**情况2：筛选过于宽松**
```
🎯 模拟币种评分...
✅ 评分完成，150 个币种满足条件
   ⚠️  评估: 筛选条件过于宽松，可能需要适当提高参数
```
**解决方案**：适当提高筛选参数

## 🏃 第三步：完整程序测试

### 启动完整策略
```bash
# 后台运行策略程序
nohup python main_enhanced.py > strategy_output.log 2>&1 &

# 记录进程ID
echo $! > strategy.pid
```

### 监控程序运行
```bash
# 实时查看日志
tail -f strategy_output.log

# 或者查看策略日志文件
tail -f logs/strategy_enhanced_$(date +%Y%m%d)_*.log
```

### 关键日志标识

#### ✅ 正常启动标识
```
[INFO] 策略启动成功 - 增强版全币种均线通道策略
[INFO] 代理连接测试成功
[INFO] 服务器时间同步完成
[INFO] 获取到 591 个行情数据
[INFO] 币种信息加载完成，共 503 个币种
[INFO] 使用缓存的币种信息，共 503 个币种
```

#### 🎯 交易信号标识
```
[INFO] 龙头池扫描完成，发现 X 个潜力币种
[INFO] XXXUSDT 评分: X.XX, 满足入场条件
[INFO] XXXUSDT 技术指标确认，准备入场
[INFO] XXXUSDT 下单成功: 订单ID XXXXXXX
```

#### ❌ 异常情况标识
```
[ERROR] 代理连接失败，尝试直连模式
[ERROR] 服务器时间同步异常
[ERROR] 获取行情数据失败
[ERROR] XXXUSDT 下单失败: 错误信息
```

## 📊 测试结果收集

### 1. 网络测试结果
```bash
# 查找网络测试报告
ls -la network_test_report_*.json
cat network_test_report_*.json
```

### 2. 策略测试结果
```bash
# 查找策略测试报告
ls -la strategy_test_report_*.json
cat strategy_test_report_*.json
```

### 3. 完整程序日志
```bash
# 最新的策略日志
ls -la logs/strategy_enhanced_*.log | tail -1
tail -100 logs/strategy_enhanced_*.log | tail -1
```

### 4. 系统资源使用
```bash
# 检查程序是否在运行
ps aux | grep main_enhanced.py

# 检查内存和CPU使用
top -p $(cat strategy.pid)
```

## 🔧 常见问题排查

### 问题1：网络连接超时
**现象**：所有币安API请求都超时
**排查**：
1. 检查代理服务：`systemctl status v2ray` 或类似
2. 测试直连：临时禁用代理设置
3. 检查防火墙：`sudo ufw status`

### 问题2：配置参数过严
**现象**：策略测试显示0个币种满足条件
**解决**：
1. 进一步降低 `score_gate`（如改为1.0）
2. 降低 `vol_mult`（如改为0.3）
3. 扩大RSI范围（如20-95）

### 问题3：程序启动后立即退出
**现象**：程序启动几秒后就停止
**排查**：
1. 查看错误日志：`tail -50 strategy_output.log`
2. 检查配置文件格式：`python -c "import yaml; yaml.safe_load(open('config/config.yaml'))"`
3. 检查权限：`ls -la config/`

### 问题4：没有交易信号
**现象**：程序运行正常但长时间无交易信号
**调整**：
1. 降低技术指标参数
2. 缩短刷新周期
3. 检查市场活跃度

## 📤 结果反馈格式

请将以下信息发送给我：

### 基础信息
```
测试时间: 2024-09-30 16:45:00
服务器环境: Ubuntu 20.04 / CentOS 7 / 其他
Python版本: 3.x.x
```

### 网络测试结果
```bash
# 执行并复制输出
python test_network_connection.py
```

### 策略测试结果
```bash
# 执行并复制输出
python test_strategy_quick.py
```

### 完整程序状态
```bash
# 程序运行状态
ps aux | grep main_enhanced.py

# 最新日志（最后50行）
tail -50 logs/strategy_enhanced_*.log | tail -1
```

### 配置快照
```bash
# 当前配置参数
grep -E "(score_gate|vol_mult|rsi_|ultra_new)" config/config.yaml
cat config/config.json
```

## 🎯 测试成功标准

### 网络测试通过标准
- 币安API连接成功率 ≥ 75%
- 服务器时间同步正常
- 能够获取行情数据

### 策略测试通过标准
- 配置文件加载成功
- 能够筛选出候选币种（建议10-100个）
- 能够生成入场信号（建议1-10个）
- 模拟订单生成正常

### 完整程序通过标准
- 程序持续运行超过5分钟
- 日志显示正常的数据获取和处理
- 没有重复的错误信息
- 系统资源使用正常（CPU < 50%, 内存 < 1GB）

---

**💡 提示**：如果任何一个测试步骤失败，请先解决该步骤的问题再进行下一步。这样可以快速定位问题并节省调试时间。
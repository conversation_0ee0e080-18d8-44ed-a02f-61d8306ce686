#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def check_xan_exists():
    """检查XANUSDT是否存在于币安永续合约中"""
    try:
        print("=== 检查XANUSDT是否存在 ===")
        
        # 直接请求币安API
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            
            print(f"总共获取到 {len(symbols)} 个交易对")
            
            # 查找XANUSDT
            xan_found = False
            for symbol in symbols:
                if symbol['symbol'] == 'XANUSDT':
                    xan_found = True
                    print(f"\n✓ 找到XANUSDT:")
                    print(f"  状态: {symbol['status']}")
                    print(f"  基础资产: {symbol['baseAsset']}")
                    print(f"  报价资产: {symbol['quoteAsset']}")
                    print(f"  合约类型: {symbol['contractType']}")
                    print(f"  上线时间: {symbol.get('onboardDate', 'N/A')}")
                    break
            
            if not xan_found:
                print("\n✗ 未找到XANUSDT永续合约")
                
                # 查找类似的XAN相关交易对
                xan_related = [s for s in symbols if 'XAN' in s['symbol']]
                if xan_related:
                    print(f"\n找到 {len(xan_related)} 个XAN相关交易对:")
                    for s in xan_related:
                        print(f"  {s['symbol']} - 状态: {s['status']}")
                else:
                    print("\n未找到任何XAN相关交易对")
                    
                # 显示最新上线的几个币种
                print(f"\n最新上线的10个永续合约:")
                usdt_symbols = [s for s in symbols if s['symbol'].endswith('USDT') and s['status'] == 'TRADING']
                # 按上线时间排序（如果有的话）
                recent_symbols = sorted(usdt_symbols, key=lambda x: x.get('onboardDate', ''), reverse=True)[:10]
                for s in recent_symbols:
                    print(f"  {s['symbol']} - 上线时间: {s.get('onboardDate', 'N/A')}")
        else:
            print(f"请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"检查异常: {e}")

if __name__ == '__main__':
    check_xan_exists()
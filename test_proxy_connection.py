import requests
import socket
import time

def test_proxy_connection():
    """测试代理服务器连接状态"""
    print("=== 代理连接测试 ===")
    
    # 获取本地IP
    try:
        host_ip = socket.gethostbyname(socket.gethostname())
        print(f"本地IP地址: {host_ip}")
    except Exception as e:
        print(f"获取本地IP失败: {e}")
        host_ip = "未知"
    
    # 测试直连模式
    print("\n1. 测试直连模式...")
    session_direct = requests.Session()
    session_direct.proxies = {}
    
    try:
        start_time = time.time()
        response = session_direct.get('https://fapi.binance.com/fapi/v1/ping', timeout=10)
        elapsed = time.time() - start_time
        if response.status_code == 200:
            print(f"✅ 直连模式成功 (响应时间: {elapsed:.2f}s)")
        else:
            print(f"❌ 直连模式失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 直连模式异常: {e}")
    
    # 测试代理模式
    print("\n2. 测试代理模式...")
    proxy_url = "http://127.0.0.1:7897"
    session_proxy = requests.Session()
    session_proxy.proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    try:
        start_time = time.time()
        response = session_proxy.get('https://fapi.binance.com/fapi/v1/ping', timeout=10, verify=False)
        elapsed = time.time() - start_time
        if response.status_code == 200:
            print(f"✅ 代理模式成功 (响应时间: {elapsed:.2f}s)")
        else:
            print(f"❌ 代理模式失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 代理模式异常: {e}")
    
    # 测试账户余额获取
    print("\n3. 测试账户信息获取...")
    # 使用直连模式测试账户信息
    try:
        response = session_direct.get('https://fapi.binance.com/fapi/v1/time', timeout=10)
        if response.status_code == 200:
            print("✅ 服务器时间获取成功")
            print(f"服务器时间: {response.json()}")
        else:
            print(f"❌ 服务器时间获取失败")
    except Exception as e:
        print(f"❌ 服务器时间获取异常: {e}")
    
    # 网络诊断
    print("\n4. 网络诊断...")
    try:
        # 测试DNS解析
        import socket
        binance_ip = socket.gethostbyname('fapi.binance.com')
        print(f"✅ Binance服务器IP: {binance_ip}")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_proxy_connection()
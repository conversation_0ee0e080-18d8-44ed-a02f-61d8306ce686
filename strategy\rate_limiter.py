import time
import threading
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class TokenBucketRateLimiter:
    """令牌桶限流器 - 更平滑的限流算法"""
    
    def __init__(self, capacity: int, refill_rate: float, name: str = "default"):
        """
        初始化令牌桶限流器
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌补充速率（令牌/秒）
            name: 限流器名称，用于日志
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.name = name
        self.tokens = float(capacity)  # 当前令牌数
        self.last_refill = time.time()
        self.lock = threading.Lock()
        
        logger.debug(f"TokenBucket '{name}' initialized: capacity={capacity}, rate={refill_rate}")
    
    def _refill_tokens(self):
        """补充令牌"""
        now = time.time()
        time_passed = now - self.last_refill
        
        # 计算应该补充的令牌数
        tokens_to_add = time_passed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now
    
    def acquire(self, tokens: int = 1, timeout: Optional[float] = None) -> bool:
        """
        获取令牌
        
        Args:
            tokens: 需要的令牌数
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            bool: 是否成功获取令牌
        """
        start_time = time.time()
        
        with self.lock:
            while True:
                self._refill_tokens()
                
                if self.tokens >= tokens:
                    self.tokens -= tokens
                    logger.debug(f"TokenBucket '{self.name}': acquired {tokens} tokens, remaining: {self.tokens:.2f}")
                    return True
                
                # 检查超时
                if timeout is not None:
                    elapsed = time.time() - start_time
                    if elapsed >= timeout:
                        logger.warning(f"TokenBucket '{self.name}': timeout waiting for {tokens} tokens")
                        return False
                
                # 计算等待时间
                tokens_needed = tokens - self.tokens
                wait_time = tokens_needed / self.refill_rate
                wait_time = min(wait_time, 0.1)  # 最多等待0.1秒后重试
                
                logger.debug(f"TokenBucket '{self.name}': waiting {wait_time:.3f}s for {tokens} tokens")
                time.sleep(wait_time)
    
    def get_available_tokens(self) -> float:
        """获取当前可用令牌数"""
        with self.lock:
            self._refill_tokens()
            return self.tokens


class TieredRateLimiter:
    """分层限流器 - 支持不同类型的API调用限制"""
    
    def __init__(self, limits_config: Dict[str, Dict[str, Any]]):
        """
        初始化分层限流器
        
        Args:
            limits_config: 限制配置
            例如: {
                'query': {'capacity': 1200, 'refill_rate': 20.0},  # 查询类API：1200/min
                'trade': {'capacity': 100, 'refill_rate': 1.67},   # 交易类API：100/min
                'order': {'capacity': 50, 'refill_rate': 0.83}     # 订单类API：50/min
            }
        """
        self.limiters = {}
        
        for limit_type, config in limits_config.items():
            self.limiters[limit_type] = TokenBucketRateLimiter(
                capacity=config['capacity'],
                refill_rate=config['refill_rate'],
                name=limit_type
            )
        
        logger.info(f"TieredRateLimiter initialized with {len(self.limiters)} tiers: {list(self.limiters.keys())}")
    
    def acquire(self, limit_type: str, tokens: int = 1, timeout: Optional[float] = None) -> bool:
        """
        获取指定类型的限流许可
        
        Args:
            limit_type: 限流类型
            tokens: 需要的令牌数
            timeout: 超时时间
            
        Returns:
            bool: 是否成功获取许可
        """
        if limit_type not in self.limiters:
            logger.error(f"Unknown limit type: {limit_type}")
            return False
        
        return self.limiters[limit_type].acquire(tokens, timeout)
    
    def get_limiter(self, limit_type: str) -> TokenBucketRateLimiter:
        """
        获取指定类型的限流器
        
        Args:
            limit_type: 限流类型
            
        Returns:
            TokenBucketRateLimiter: 对应的限流器实例
        """
        if limit_type not in self.limiters:
            logger.error(f"Unknown limit type: {limit_type}")
            raise KeyError(f"Unknown limit type: {limit_type}")
        
        return self.limiters[limit_type]
    
    def get_status(self) -> Dict[str, float]:
        """获取所有限流器的状态"""
        status = {}
        for limit_type, limiter in self.limiters.items():
            status[limit_type] = limiter.get_available_tokens()
        return status


class ApiRateLimiter:
    """API限流器 - 兼容原有接口的包装器"""
    
    def __init__(self, max_requests: int, time_window: int):
        """
        初始化API限流器（兼容原有接口）
        
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口（秒）
        """
        # 转换为令牌桶参数
        refill_rate = max_requests / time_window
        self.limiter = TokenBucketRateLimiter(
            capacity=max_requests,
            refill_rate=refill_rate,
            name="legacy_api"
        )
        
        logger.info(f"ApiRateLimiter initialized: {max_requests} requests per {time_window}s")
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """获取请求许可（兼容原有接口）"""
        return self.limiter.acquire(tokens=1, timeout=timeout)
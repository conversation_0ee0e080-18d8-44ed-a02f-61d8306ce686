import time
import threading
import queue
import logging
from typing import Callable, Optional, Dict, Any, List
from enum import Enum
import traceback
from dataclasses import dataclass
import uuid

logger = logging.getLogger(__name__)

class OperationStatus(Enum):
    """操作状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class OperationResult:
    """操作结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    retry_count: int = 0
    execution_time: float = 0.0

class OrderOperation:
    """订单操作封装"""
    
    def __init__(self, 
                 operation_id: str,
                 operation_func: Callable,
                 callback: Optional[Callable[[OperationResult], None]] = None,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 timeout: float = 30.0,
                 priority: int = 0,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化订单操作
        
        Args:
            operation_id: 操作唯一标识
            operation_func: 要执行的操作函数
            callback: 操作完成后的回调函数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout: 操作超时时间（秒）
            priority: 优先级（数字越大优先级越高）
            metadata: 附加元数据
        """
        self.operation_id = operation_id
        self.operation_func = operation_func
        self.callback = callback
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.priority = priority
        self.metadata = metadata or {}
        
        self.status = OperationStatus.PENDING
        self.retry_count = 0
        self.created_at = time.time()
        self.started_at = None
        self.completed_at = None
        self.result = None
    
    def __lt__(self, other):
        """支持优先级队列排序"""
        return self.priority > other.priority  # 高优先级排在前面

class SmartRetryStrategy:
    """智能重试策略"""
    
    # 可重试的错误代码
    RETRYABLE_ERRORS = {
        '-1021',  # 时间戳错误
        '-1022',  # 签名错误
        '-2010',  # 余额不足
        '-2011',  # 取消订单失败
        'TIMEOUT',  # 超时
        'NETWORK_ERROR',  # 网络错误
        'SERVER_ERROR'  # 服务器错误
    }
    
    # 不可重试的错误代码
    NON_RETRYABLE_ERRORS = {
        '-1013',  # 过滤器失败
        '-1111',  # 精度过高
        '-1112',  # 没有订单
        '-2013',  # 订单不存在
        '-2014'   # API密钥格式无效
    }
    
    @classmethod
    def should_retry(cls, error_code: str, retry_count: int, max_retries: int) -> bool:
        """判断是否应该重试"""
        if retry_count >= max_retries:
            return False
        
        if error_code in cls.NON_RETRYABLE_ERRORS:
            return False
        
        return error_code in cls.RETRYABLE_ERRORS
    
    @classmethod
    def calculate_delay(cls, retry_count: int, base_delay: float = 1.0) -> float:
        """计算重试延迟（指数退避 + 随机抖动）"""
        import random
        delay = base_delay * (2 ** retry_count)
        jitter = random.uniform(0.1, 0.3) * delay
        return min(delay + jitter, 30.0)  # 最大延迟30秒

class EnhancedOrderQueue:
    """增强的订单操作队列"""
    
    def __init__(self, rate_limiter, max_workers: int = 1, queue_size: int = 1000):
        """
        初始化增强订单队列
        
        Args:
            rate_limiter: 限流器
            max_workers: 最大工作线程数
            queue_size: 队列最大大小
        """
        self.rate_limiter = rate_limiter
        self.max_workers = max_workers
        self.queue_size = queue_size
        
        # 使用优先级队列
        self.operation_queue = queue.PriorityQueue(maxsize=queue_size)
        self.retry_queue = queue.Queue()
        
        # 操作跟踪
        self.operations: Dict[str, OrderOperation] = {}
        self.operations_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_operations': 0,
            'completed_operations': 0,
            'failed_operations': 0,
            'retried_operations': 0,
            'queue_full_count': 0
        }
        
        # 工作线程
        self.workers = []
        self.retry_worker = None
        self.running = True
        
        self._start_workers()
        logger.info(f"EnhancedOrderQueue initialized with {max_workers} workers, queue size: {queue_size}")
    
    def _start_workers(self):
        """启动工作线程"""
        # 主工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker, args=(f"worker-{i}",), daemon=True)
            worker.start()
            self.workers.append(worker)
        
        # 重试工作线程
        self.retry_worker = threading.Thread(target=self._retry_worker, daemon=True)
        self.retry_worker.start()
    
    def add_operation(self, 
                     operation_func: Callable,
                     callback: Optional[Callable[[OperationResult], None]] = None,
                     max_retries: int = 3,
                     retry_delay: float = 1.0,
                     timeout: float = 30.0,
                     priority: int = 0,
                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        添加操作到队列
        
        Returns:
            str: 操作ID
        """
        operation_id = str(uuid.uuid4())
        operation = OrderOperation(
            operation_id=operation_id,
            operation_func=operation_func,
            callback=callback,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout,
            priority=priority,
            metadata=metadata
        )
        
        try:
            # 使用优先级和创建时间作为排序键
            priority_key = (-priority, operation.created_at)
            self.operation_queue.put((priority_key, operation), timeout=1.0)
            
            with self.operations_lock:
                self.operations[operation_id] = operation
                self.stats['total_operations'] += 1
            
            logger.debug(f"Operation {operation_id} added to queue with priority {priority}")
            return operation_id
            
        except queue.Full:
            self.stats['queue_full_count'] += 1
            logger.error(f"Queue is full, operation {operation_id} rejected")
            raise RuntimeError("Order queue is full")
    
    def get_operation_status(self, operation_id: str) -> Optional[OperationStatus]:
        """获取操作状态"""
        with self.operations_lock:
            operation = self.operations.get(operation_id)
            return operation.status if operation else None
    
    def get_operation_result(self, operation_id: str) -> Optional[OperationResult]:
        """获取操作结果"""
        with self.operations_lock:
            operation = self.operations.get(operation_id)
            return operation.result if operation else None
    
    def _worker(self, worker_name: str):
        """工作线程处理队列中的操作"""
        logger.info(f"Worker {worker_name} started")
        
        while self.running:
            try:
                # 获取操作（带超时）
                try:
                    priority_key, operation = self.operation_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 获取限流许可
                if not self.rate_limiter.acquire(timeout=5.0):
                    logger.warning(f"Rate limiter timeout for operation {operation.operation_id}")
                    self._handle_operation_failure(operation, "RATE_LIMIT_TIMEOUT", "Rate limiter timeout")
                    continue
                
                # 执行操作
                self._execute_operation(operation, worker_name)
                self.operation_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
                logger.error(traceback.format_exc())
    
    def _retry_worker(self):
        """重试工作线程"""
        logger.info("Retry worker started")
        
        while self.running:
            try:
                operation = self.retry_queue.get(timeout=1.0)
                
                # 等待重试延迟
                delay = SmartRetryStrategy.calculate_delay(operation.retry_count, operation.retry_delay)
                time.sleep(delay)
                
                # 重新加入主队列
                priority_key = (-operation.priority, time.time())
                self.operation_queue.put((priority_key, operation))
                
                logger.debug(f"Operation {operation.operation_id} re-queued for retry {operation.retry_count}")
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Retry worker error: {e}")
    
    def _execute_operation(self, operation: OrderOperation, worker_name: str):
        """执行操作"""
        operation.status = OperationStatus.PROCESSING
        operation.started_at = time.time()
        
        logger.debug(f"Worker {worker_name} executing operation {operation.operation_id}")
        
        try:
            # 执行操作函数
            start_time = time.time()
            result_data = operation.operation_func()
            execution_time = time.time() - start_time
            
            # 创建成功结果
            result = OperationResult(
                success=True,
                data=result_data,
                retry_count=operation.retry_count,
                execution_time=execution_time
            )
            
            self._handle_operation_success(operation, result)
            
        except Exception as e:
            error_msg = str(e)
            error_code = getattr(e, 'code', 'UNKNOWN_ERROR')
            
            logger.warning(f"Operation {operation.operation_id} failed: {error_msg}")
            
            # 判断是否需要重试
            if SmartRetryStrategy.should_retry(error_code, operation.retry_count, operation.max_retries):
                self._handle_operation_retry(operation, error_code, error_msg)
            else:
                self._handle_operation_failure(operation, error_code, error_msg)
    
    def _handle_operation_success(self, operation: OrderOperation, result: OperationResult):
        """处理操作成功"""
        operation.status = OperationStatus.COMPLETED
        operation.completed_at = time.time()
        operation.result = result
        
        with self.operations_lock:
            self.stats['completed_operations'] += 1
        
        logger.info(f"Operation {operation.operation_id} completed successfully in {result.execution_time:.3f}s")
        
        # 执行回调
        if operation.callback:
            try:
                operation.callback(result)
            except Exception as e:
                logger.error(f"Callback error for operation {operation.operation_id}: {e}")
    
    def _handle_operation_retry(self, operation: OrderOperation, error_code: str, error_msg: str):
        """处理操作重试"""
        operation.status = OperationStatus.RETRYING
        operation.retry_count += 1
        
        with self.operations_lock:
            self.stats['retried_operations'] += 1
        
        logger.info(f"Operation {operation.operation_id} will retry ({operation.retry_count}/{operation.max_retries})")
        
        # 加入重试队列
        self.retry_queue.put(operation)
    
    def _handle_operation_failure(self, operation: OrderOperation, error_code: str, error_msg: str):
        """处理操作失败"""
        operation.status = OperationStatus.FAILED
        operation.completed_at = time.time()
        
        result = OperationResult(
            success=False,
            error=error_msg,
            error_code=error_code,
            retry_count=operation.retry_count
        )
        operation.result = result
        
        with self.operations_lock:
            self.stats['failed_operations'] += 1
        
        logger.error(f"Operation {operation.operation_id} failed permanently: {error_msg}")
        
        # 执行回调
        if operation.callback:
            try:
                operation.callback(result)
            except Exception as e:
                logger.error(f"Callback error for operation {operation.operation_id}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.operations_lock:
            return {
                **self.stats,
                'queue_size': self.operation_queue.qsize(),
                'retry_queue_size': self.retry_queue.qsize(),
                'active_operations': len([op for op in self.operations.values() 
                                        if op.status == OperationStatus.PROCESSING])
            }
    
    def shutdown(self):
        """关闭队列"""
        logger.info("Shutting down EnhancedOrderQueue")
        self.running = False
        
        # 等待队列清空
        self.operation_queue.join()

# 兼容性包装器
class OrderOperationQueue:
    """订单操作队列 - 兼容原有接口的包装器"""
    
    def __init__(self, rate_limiter):
        """初始化订单操作队列（兼容原有接口）"""
        self.enhanced_queue = EnhancedOrderQueue(rate_limiter)
        logger.info("OrderOperationQueue initialized with enhanced backend")
    
    def add_operation(self, operation):
        """添加操作到队列（兼容原有接口）"""
        return self.enhanced_queue.add_operation(operation)
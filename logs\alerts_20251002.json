[{"timestamp": "2025-10-02T08:13:34.855853", "level": "WARNING", "category": "TRADING_RULES", "symbol": "PLAYUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:16:30.064955", "level": "WARNING", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:16:30.110511", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 5 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 5}, "resolved": false}, {"timestamp": "2025-10-02T08:16:30.113854", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 5 次", "details": {"consecutive_failures": 5, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:16:30.117854", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 6 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 6}, "resolved": false}, {"timestamp": "2025-10-02T08:16:30.125461", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 6 次", "details": {"consecutive_failures": 6, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:22:22.512853", "level": "WARNING", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:22:22.548346", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 5 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 5}, "resolved": false}, {"timestamp": "2025-10-02T08:22:22.551378", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 5 次", "details": {"consecutive_failures": 5, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:22:22.553378", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 6 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 6}, "resolved": false}, {"timestamp": "2025-10-02T08:22:22.557157", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 6 次", "details": {"consecutive_failures": 6, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:24:12.805897", "level": "WARNING", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:24:12.831190", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 5 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 5}, "resolved": false}, {"timestamp": "2025-10-02T08:24:12.834190", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 5 次", "details": {"consecutive_failures": 5, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:24:12.838691", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 6 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 6}, "resolved": false}, {"timestamp": "2025-10-02T08:24:12.842437", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 6 次", "details": {"consecutive_failures": 6, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:25:33.817277", "level": "WARNING", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:25:33.848331", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 5 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 5}, "resolved": false}, {"timestamp": "2025-10-02T08:25:33.851331", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 5 次", "details": {"consecutive_failures": 5, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:25:33.854160", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 6 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 6}, "resolved": false}, {"timestamp": "2025-10-02T08:25:33.858160", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 6 次", "details": {"consecutive_failures": 6, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:31:07.805760", "level": "WARNING", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "交易规则获取失败率过高: 50.00%", "details": {"failure_rate": 0.5, "method": "api"}, "resolved": false}, {"timestamp": "2025-10-02T08:31:07.833740", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 5 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 5}, "resolved": false}, {"timestamp": "2025-10-02T08:31:07.839565", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 5 次", "details": {"consecutive_failures": 5, "error_code": -1111}, "resolved": false}, {"timestamp": "2025-10-02T08:31:07.840074", "level": "CRITICAL", "category": "TRADING_RULES", "symbol": "TESTUSDT", "message": "1小时内精度错误 6 次，可能存在交易规则获取问题", "details": {"error_code": -1111, "recent_count": 6}, "resolved": false}, {"timestamp": "2025-10-02T08:31:07.844095", "level": "ERROR", "category": "ORDER_EXECUTION", "symbol": "TESTUSDT", "message": "连续订单失败 6 次", "details": {"consecutive_failures": 6, "error_code": -1111}, "resolved": false}]
# -*- coding: utf-8 -*-
"""
极简新币通道突破策略
python maker_channel_light.py
"""
import time, logging, datetime as dt
import pandas as pd
import requests

API_KEY = "nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s"
SECRET  = "HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u"
BASE    = 'https://fapi.binance.com'

session = requests.Session()
session.headers.update({'X-MBX-APIKEY': API_KEY})

def get(path, params=None):
    params = params or {}
    from urllib.parse import urlencode
    query = urlencode(params)
    url = BASE + path + ('?' + query if query else '')
    r = session.get(url).json()
    if 'code' in r:
        raise RuntimeError(r)
    return r

def post(path, params):
    from hashlib import sha256
    import hmac, time
    params['timestamp'] = int(time.time()*1000)
    query = '&'.join([f"{k}={v}" for k,v in params.items()])
    params['signature'] = hmac.new(SECRET.encode(), query.encode(), sha256).hexdigest()
    r = session.post(BASE + path, data=params).json()
    if 'code' in r:
        raise RuntimeError(r)
    return r

# ---------- 策略 ----------
class LightNewCoinBreakout:
    def __init__(self):
        self.symbol = None
        self.entry  = None
        self.qty    = None
        self.day    = None
        logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s')

    # 1. 每日选币
    def pick(self):
        tickers = get('/fapi/v1/ticker/24hr')
        exch = get('/fapi/v1/exchangeInfo')
        age = {s['symbol']: (pd.Timestamp.utcnow() - pd.to_datetime(s['onboardDate'], unit='ms')).days
               for s in exch['symbols'] if s['status']=='TRADING' and s['symbol'].endswith('USDT')}
        cand = []
        for t in tickers:
            sym = t['symbol']
            if sym not in age or age[sym] > 30: continue
            chg = float(t['priceChangePercent'])
            vol = float(t['quoteVolume'])
            if 10 <= chg <= 50 and vol >= 200000:
                cand.append((sym, vol))
        if not cand: return None
        return sorted(cand, key=lambda x: x[1], reverse=True)[0][0]

    # 2. 下单工具
    def order(self, side, qty, price=None):
        info = get('/fapi/v1/exchangeInfo', {'symbol': self.symbol})
        f = {x['filterType']: x for x in info['symbols'][0]['filters']}
        tick = float(f['PRICE_FILTER']['tickSize'])
        step = float(f['LOT_SIZE']['stepSize'])
        min_notional = float(f['MIN_NOTIONAL']['notional'])
        qty = round(qty / step) * step
        if price:
            price = round(price / tick) * tick
            return post('/fapi/v1/order', dict(symbol=self.symbol, side=side, type='LIMIT',
                                               quantity=f"{qty:.6f}", price=f"{price:.6f}",
                                               timeInForce='GTC'))
        else:
            return post('/fapi/v1/order', dict(symbol=self.symbol, side=side, type='MARKET',
                                               quantity=f"{qty:.6f}"))

    # 3. 每日 09:30 选币 + 09:25 平仓
    def daily(self):
        now = dt.datetime.utcnow()
        if now.hour==6 and now.minute==25:   # utc 06:25 = 14:25 北京时间
            if self.entry:
                post('/fapi/v1/order', dict(symbol=self.symbol, side='SELL', type='MARKET',
                                            quantity=f"{self.qty:.6f}"))
                logging.info('Day close')
            self.symbol = self.entry = self.qty = None
            return
        if now.hour==1 and now.minute==30:   # utc 01:30 = 09:30 北京时间
            self.symbol = self.pick()
            if self.symbol:
                logging.info(f'Pick {self.symbol}')
            self.day = now.date()

    # 4. 3m 突破
    def run(self):
        logging.info('LightNewCoinBreakout start')
        while True:
            try:
                self.daily()
                if not self.symbol: 
                    time.sleep(30)
                    continue
                kl = get('/fapi/v1/klines', {'symbol': self.symbol, 'interval': '3m', 'limit': 21})
                if len(kl) < 21: 
                    time.sleep(30); continue
                hh = max(float(k[2]) for k in kl[-21:-1])
                close = float(kl[-1][4])
                if close > hh and self.entry is None:
                    # 开多
                    lev = post('/fapi/v1/leverage', dict(symbol=self.symbol, leverage=3))
                    bal = float(get('/fapi/v2/balance')[0]['balance'])
                    qty = bal * 0.99 / close
                    self.order('BUY', qty)
                    self.entry = close
                    self.qty = qty
                    # 止损 3%
                    post('/fapi/v1/order', dict(symbol=self.symbol, side='SELL', type='STOP_MARKET',
                                                quantity=f"{qty:.6f}", stopPrice=f"{close*0.97:.6f}"))
                    logging.info(f'Entry {self.symbol} {qty} @ {close}')
                time.sleep(30)
            except KeyboardInterrupt:
                break
            except Exception as e:
                logging.error(e)
                time.sleep(60)

if __name__ == '__main__':
    LightNewCoinBreakout().run()
"""
性能监控与反馈系统
Performance Monitoring and Feedback System for Universal Trading

功能：
1. 实时监控各币种交易表现
2. 自动分析和优化交易参数
3. 提供性能报告和趋势分析
4. 实现自动化反馈优化循环
"""

import time
import json
import logging
import statistics
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque

from ..classifier.symbol_classifier import SymbolTier, SymbolClassifier
from ..config.parameter_matrix import ParameterMatrix, TradingParameters
from ..risk.adaptive_risk_manager import AdaptiveRiskManager, RiskLevel

class PerformanceGrade(Enum):
    """性能评级"""
    EXCELLENT = "优秀"    # A级
    GOOD = "良好"         # B级
    AVERAGE = "一般"      # C级
    POOR = "较差"         # D级
    TERRIBLE = "极差"     # F级

@dataclass
class TradeRecord:
    """交易记录"""
    symbol: str
    side: str
    quantity: float
    entry_price: float
    exit_price: float = 0.0
    pnl: float = 0.0
    commission: float = 0.0
    entry_time: float = 0.0
    exit_time: float = 0.0
    hold_duration: float = 0.0
    is_closed: bool = False
    error_code: str = ""
    error_message: str = ""

@dataclass
class SymbolPerformance:
    """币种性能指标"""
    symbol: str
    tier: SymbolTier
    
    # 基础统计
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    
    # 收益指标
    total_pnl: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    net_profit: float = 0.0
    
    # 比率指标
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    avg_trade: float = 0.0
    
    # 风险指标
    max_drawdown: float = 0.0
    max_consecutive_wins: int = 0
    max_consecutive_losses: int = 0
    current_streak: int = 0
    
    # 时间指标
    avg_hold_time: float = 0.0
    total_hold_time: float = 0.0
    
    # 效率指标
    trades_per_day: float = 0.0
    success_rate_trend: List[float] = field(default_factory=list)
    pnl_trend: List[float] = field(default_factory=list)
    
    # 错误统计
    error_count: int = 0
    error_rate: float = 0.0
    
    # 更新时间
    last_update: float = field(default_factory=time.time)
    
    def calculate_grade(self) -> PerformanceGrade:
        """计算性能评级"""
        score = 0
        
        # 胜率评分 (0-30分)
        if self.win_rate >= 0.7:
            score += 30
        elif self.win_rate >= 0.6:
            score += 25
        elif self.win_rate >= 0.5:
            score += 20
        elif self.win_rate >= 0.4:
            score += 15
        else:
            score += 10
        
        # 盈利因子评分 (0-25分)
        if self.profit_factor >= 2.0:
            score += 25
        elif self.profit_factor >= 1.5:
            score += 20
        elif self.profit_factor >= 1.2:
            score += 15
        elif self.profit_factor >= 1.0:
            score += 10
        else:
            score += 5
        
        # 平均收益评分 (0-20分)
        if self.avg_trade > 0:
            if self.avg_trade >= 5.0:
                score += 20
            elif self.avg_trade >= 2.0:
                score += 15
            elif self.avg_trade >= 1.0:
                score += 10
            else:
                score += 5
        
        # 回撤评分 (0-15分)
        if self.max_drawdown <= 0.02:
            score += 15
        elif self.max_drawdown <= 0.05:
            score += 12
        elif self.max_drawdown <= 0.1:
            score += 8
        elif self.max_drawdown <= 0.15:
            score += 5
        else:
            score += 2
        
        # 错误率评分 (0-10分)
        if self.error_rate <= 0.01:
            score += 10
        elif self.error_rate <= 0.05:
            score += 8
        elif self.error_rate <= 0.1:
            score += 5
        else:
            score += 2
        
        # 根据总分确定评级
        if score >= 85:
            return PerformanceGrade.EXCELLENT
        elif score >= 70:
            return PerformanceGrade.GOOD
        elif score >= 55:
            return PerformanceGrade.AVERAGE
        elif score >= 40:
            return PerformanceGrade.POOR
        else:
            return PerformanceGrade.TERRIBLE

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, classifier: SymbolClassifier = None,
                 parameter_matrix: ParameterMatrix = None,
                 risk_manager: AdaptiveRiskManager = None):
        self.classifier = classifier or SymbolClassifier()
        self.parameter_matrix = parameter_matrix or ParameterMatrix()
        self.risk_manager = risk_manager or AdaptiveRiskManager()
        self.logger = logging.getLogger(__name__)
        
        # 性能数据存储
        self.symbol_performances: Dict[str, SymbolPerformance] = {}
        self.trade_records: Dict[str, List[TradeRecord]] = defaultdict(list)
        
        # 实时数据队列（用于趋势分析）
        self.real_time_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 优化历史
        self.optimization_history: List[Dict] = []
        
        # 监控配置
        self.monitor_config = {
            'min_trades_for_analysis': 10,    # 最少交易次数才进行分析
            'trend_window': 20,               # 趋势分析窗口
            'optimization_interval': 3600,    # 优化间隔（秒）
            'performance_threshold': 0.3,     # 性能阈值
            'auto_optimization': True         # 自动优化开关
        }
        
        self.last_optimization_time = 0
    
    def record_trade(self, trade: TradeRecord):
        """记录交易"""
        symbol = trade.symbol
        
        # 添加到交易记录
        self.trade_records[symbol].append(trade)
        
        # 更新性能指标
        self._update_symbol_performance(symbol, trade)
        
        # 添加到实时数据队列
        self.real_time_data[symbol].append({
            'timestamp': time.time(),
            'pnl': trade.pnl,
            'is_win': trade.pnl > 0,
            'hold_duration': trade.hold_duration
        })
        
        self.logger.info(f"交易记录: {symbol} {trade.side} PnL: {trade.pnl:.2f}")
        
        # 检查是否需要优化
        if self.monitor_config['auto_optimization']:
            self._check_optimization_trigger(symbol)
    
    def _update_symbol_performance(self, symbol: str, trade: TradeRecord):
        """更新币种性能指标"""
        if symbol not in self.symbol_performances:
            tier = self.classifier.get_symbol_tier(symbol)
            self.symbol_performances[symbol] = SymbolPerformance(symbol=symbol, tier=tier)
        
        perf = self.symbol_performances[symbol]
        
        # 更新基础统计
        perf.total_trades += 1
        
        if trade.pnl > 0:
            perf.winning_trades += 1
            perf.gross_profit += trade.pnl
            perf.current_streak = perf.current_streak + 1 if perf.current_streak >= 0 else 1
            perf.max_consecutive_wins = max(perf.max_consecutive_wins, perf.current_streak)
        else:
            perf.losing_trades += 1
            perf.gross_loss += abs(trade.pnl)
            perf.current_streak = perf.current_streak - 1 if perf.current_streak <= 0 else -1
            perf.max_consecutive_losses = max(perf.max_consecutive_losses, abs(perf.current_streak))
        
        # 更新收益指标
        perf.total_pnl += trade.pnl
        perf.net_profit = perf.gross_profit - perf.gross_loss
        
        # 更新比率指标
        perf.win_rate = perf.winning_trades / perf.total_trades
        perf.profit_factor = perf.gross_profit / perf.gross_loss if perf.gross_loss > 0 else float('inf')
        perf.avg_win = perf.gross_profit / perf.winning_trades if perf.winning_trades > 0 else 0
        perf.avg_loss = perf.gross_loss / perf.losing_trades if perf.losing_trades > 0 else 0
        perf.avg_trade = perf.total_pnl / perf.total_trades
        
        # 更新时间指标
        if trade.hold_duration > 0:
            perf.total_hold_time += trade.hold_duration
            perf.avg_hold_time = perf.total_hold_time / perf.total_trades
        
        # 更新错误统计
        if trade.error_code:
            perf.error_count += 1
            perf.error_rate = perf.error_count / perf.total_trades
        
        # 更新趋势数据
        perf.success_rate_trend.append(perf.win_rate)
        perf.pnl_trend.append(perf.total_pnl)
        
        # 保持趋势数据长度
        max_trend_length = self.monitor_config['trend_window']
        if len(perf.success_rate_trend) > max_trend_length:
            perf.success_rate_trend = perf.success_rate_trend[-max_trend_length:]
        if len(perf.pnl_trend) > max_trend_length:
            perf.pnl_trend = perf.pnl_trend[-max_trend_length:]
        
        # 计算回撤
        if len(perf.pnl_trend) > 1:
            peak_pnl = max(perf.pnl_trend)
            current_drawdown = (peak_pnl - perf.total_pnl) / peak_pnl if peak_pnl > 0 else 0
            perf.max_drawdown = max(perf.max_drawdown, current_drawdown)
        
        perf.last_update = time.time()
    
    def _check_optimization_trigger(self, symbol: str):
        """检查是否触发优化"""
        current_time = time.time()
        
        # 检查优化间隔
        if current_time - self.last_optimization_time < self.monitor_config['optimization_interval']:
            return
        
        perf = self.symbol_performances.get(symbol)
        if not perf or perf.total_trades < self.monitor_config['min_trades_for_analysis']:
            return
        
        # 检查性能是否需要优化
        grade = perf.calculate_grade()
        if grade in [PerformanceGrade.POOR, PerformanceGrade.TERRIBLE]:
            self.logger.info(f"触发优化: {symbol} 性能评级: {grade.value}")
            self._optimize_symbol_parameters(symbol)
            self.last_optimization_time = current_time
    
    def _optimize_symbol_parameters(self, symbol: str):
        """优化币种参数"""
        perf = self.symbol_performances.get(symbol)
        if not perf:
            return
        
        try:
            # 准备性能数据
            performance_data = {
                'success_rate': perf.win_rate,
                'avg_profit': perf.avg_trade,
                'max_drawdown': perf.max_drawdown,
                'volatility': self._calculate_volatility(symbol),
                'profit_factor': perf.profit_factor,
                'error_rate': perf.error_rate
            }
            
            # 调用参数矩阵的优化方法
            old_params = self.parameter_matrix.get_parameters(symbol, perf.tier)
            optimized_params = self.parameter_matrix.optimize_parameters(
                symbol, perf.tier, performance_data
            )
            
            # 记录优化历史
            optimization_record = {
                'timestamp': time.time(),
                'symbol': symbol,
                'tier': perf.tier.value,
                'performance_grade': perf.calculate_grade().value,
                'performance_data': performance_data,
                'old_parameters': asdict(old_params),
                'new_parameters': asdict(optimized_params),
                'optimization_reason': self._get_optimization_reason(perf)
            }
            
            self.optimization_history.append(optimization_record)
            
            # 保持历史记录长度
            if len(self.optimization_history) > 100:
                self.optimization_history = self.optimization_history[-100:]
            
            self.logger.info(f"参数优化完成: {symbol}")
            
        except Exception as e:
            self.logger.error(f"参数优化失败 {symbol}: {e}")
    
    def _calculate_volatility(self, symbol: str) -> float:
        """计算波动率"""
        if symbol not in self.real_time_data or len(self.real_time_data[symbol]) < 2:
            return 0.0
        
        pnl_values = [data['pnl'] for data in self.real_time_data[symbol]]
        if len(pnl_values) < 2:
            return 0.0
        
        return statistics.stdev(pnl_values) / statistics.mean(pnl_values) if statistics.mean(pnl_values) != 0 else 0.0
    
    def _get_optimization_reason(self, perf: SymbolPerformance) -> str:
        """获取优化原因"""
        reasons = []
        
        if perf.win_rate < 0.4:
            reasons.append("胜率过低")
        if perf.profit_factor < 1.0:
            reasons.append("盈利因子小于1")
        if perf.max_drawdown > 0.1:
            reasons.append("回撤过大")
        if perf.error_rate > 0.1:
            reasons.append("错误率过高")
        if perf.avg_trade < 0:
            reasons.append("平均收益为负")
        
        return "; ".join(reasons) if reasons else "综合性能不佳"
    
    def get_symbol_performance(self, symbol: str) -> Optional[SymbolPerformance]:
        """获取币种性能"""
        return self.symbol_performances.get(symbol)
    
    def get_performance_ranking(self, limit: int = 10) -> List[Tuple[str, SymbolPerformance, PerformanceGrade]]:
        """获取性能排名"""
        rankings = []
        
        for symbol, perf in self.symbol_performances.items():
            if perf.total_trades >= self.monitor_config['min_trades_for_analysis']:
                grade = perf.calculate_grade()
                rankings.append((symbol, perf, grade))
        
        # 按总收益排序
        rankings.sort(key=lambda x: x[1].total_pnl, reverse=True)
        
        return rankings[:limit]
    
    def get_tier_performance_summary(self) -> Dict[str, Dict]:
        """获取分类性能摘要"""
        tier_stats = defaultdict(lambda: {
            'count': 0,
            'total_pnl': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'avg_win_rate': 0.0,
            'grades': defaultdict(int)
        })
        
        for symbol, perf in self.symbol_performances.items():
            if perf.total_trades < self.monitor_config['min_trades_for_analysis']:
                continue
            
            tier_name = perf.tier.value
            stats = tier_stats[tier_name]
            
            stats['count'] += 1
            stats['total_pnl'] += perf.total_pnl
            stats['total_trades'] += perf.total_trades
            stats['winning_trades'] += perf.winning_trades
            
            grade = perf.calculate_grade()
            stats['grades'][grade.value] += 1
        
        # 计算平均胜率
        for tier_name, stats in tier_stats.items():
            if stats['total_trades'] > 0:
                stats['avg_win_rate'] = stats['winning_trades'] / stats['total_trades']
        
        return dict(tier_stats)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        if not self.optimization_history:
            return {'message': '暂无优化记录'}
        
        recent_optimizations = [opt for opt in self.optimization_history 
                              if time.time() - opt['timestamp'] < 86400]  # 最近24小时
        
        optimization_reasons = defaultdict(int)
        optimized_symbols = set()
        
        for opt in recent_optimizations:
            optimization_reasons[opt['optimization_reason']] += 1
            optimized_symbols.add(opt['symbol'])
        
        return {
            'total_optimizations': len(self.optimization_history),
            'recent_optimizations': len(recent_optimizations),
            'optimized_symbols_count': len(optimized_symbols),
            'optimization_reasons': dict(optimization_reasons),
            'latest_optimization': self.optimization_history[-1] if self.optimization_history else None
        }
    
    def generate_performance_report(self, symbol: str = None) -> Dict[str, Any]:
        """生成性能报告"""
        if symbol:
            # 单个币种报告
            perf = self.symbol_performances.get(symbol)
            if not perf:
                return {'error': f'未找到币种 {symbol} 的性能数据'}
            
            grade = perf.calculate_grade()
            
            return {
                'symbol': symbol,
                'tier': perf.tier.value,
                'grade': grade.value,
                'performance': asdict(perf),
                'recent_trades': len([t for t in self.trade_records[symbol] 
                                    if time.time() - t.entry_time < 86400]),
                'optimization_count': len([opt for opt in self.optimization_history 
                                         if opt['symbol'] == symbol])
            }
        else:
            # 全局报告
            total_symbols = len(self.symbol_performances)
            active_symbols = len([p for p in self.symbol_performances.values() 
                                if p.total_trades >= self.monitor_config['min_trades_for_analysis']])
            
            total_pnl = sum(p.total_pnl for p in self.symbol_performances.values())
            total_trades = sum(p.total_trades for p in self.symbol_performances.values())
            
            grade_distribution = defaultdict(int)
            for perf in self.symbol_performances.values():
                if perf.total_trades >= self.monitor_config['min_trades_for_analysis']:
                    grade = perf.calculate_grade()
                    grade_distribution[grade.value] += 1
            
            return {
                'summary': {
                    'total_symbols': total_symbols,
                    'active_symbols': active_symbols,
                    'total_pnl': total_pnl,
                    'total_trades': total_trades,
                    'avg_pnl_per_symbol': total_pnl / active_symbols if active_symbols > 0 else 0
                },
                'grade_distribution': dict(grade_distribution),
                'tier_performance': self.get_tier_performance_summary(),
                'top_performers': [(symbol, perf.total_pnl, perf.calculate_grade().value) 
                                 for symbol, perf, grade in self.get_performance_ranking(5)],
                'optimization_summary': self.get_optimization_report()
            }
    
    def export_performance_data(self, filepath: str):
        """导出性能数据"""
        export_data = {
            'export_time': time.time(),
            'symbol_performances': {
                symbol: asdict(perf) for symbol, perf in self.symbol_performances.items()
            },
            'optimization_history': self.optimization_history,
            'monitor_config': self.monitor_config,
            'summary_report': self.generate_performance_report()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"性能数据已导出到: {filepath}")

# 使用示例
if __name__ == "__main__":
    # 初始化监控器
    monitor = PerformanceMonitor()
    
    # 模拟交易记录
    trade1 = TradeRecord(
        symbol='BTCUSDT',
        side='BUY',
        quantity=0.001,
        entry_price=50000,
        exit_price=50500,
        pnl=0.5,
        entry_time=time.time() - 3600,
        exit_time=time.time(),
        hold_duration=3600,
        is_closed=True
    )
    
    monitor.record_trade(trade1)
    
    # 获取性能报告
    report = monitor.generate_performance_report('BTCUSDT')
    print(f"性能报告: {report}")
    
    # 获取排名
    rankings = monitor.get_performance_ranking(5)
    for symbol, perf, grade in rankings:
        print(f"{symbol}: {grade.value}, PnL: {perf.total_pnl:.2f}")
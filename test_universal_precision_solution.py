#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普遍性精度解决方案验证测试
测试各种 step_size 场景，确保新的精度处理方法能够正确工作
"""

import sys
import os
from decimal import Decimal, getcontext
import logging

# 添加策略模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy'))

# 设置高精度计算
getcontext().prec = 50

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_precision_solution.log')
    ]
)

class MockLogger:
    """模拟日志记录器"""
    def debug(self, msg):
        print(f"DEBUG: {msg}")
    
    def info(self, msg):
        print(f"INFO: {msg}")
    
    def warning(self, msg):
        print(f"WARNING: {msg}")
    
    def error(self, msg):
        print(f"ERROR: {msg}")

class PrecisionTester:
    """精度处理测试器"""
    
    def __init__(self):
        self.log = MockLogger()
        
    def safe_format_to_precision(self, value, step_size):
        """
        基于 step_size 的智能精度控制方法（从 maker_channel_enhanced.py 复制）
        """
        try:
            if not value or not step_size:
                self.log.warning(f"[精度格式化] 无效输入: value={value}, step_size={step_size}")
                return "0"
            
            # 转换为 Decimal 进行高精度计算
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(step_size))
            
            # 基于 step_size 智能确定精度
            if decimal_step >= Decimal('1.0'):
                # step_size >= 1.0，使用整数精度
                precision = 0
                self.log.debug(f"[智能精度控制] step_size={step_size} >= 1.0，使用整数精度")
            elif decimal_step >= Decimal('0.001'):
                # 0.001 <= step_size < 1.0，使用3位小数
                precision = 3
                self.log.debug(f"[智能精度控制] step_size={step_size} >= 0.001，使用3位小数精度")
            else:
                # step_size < 0.001，使用8位小数
                precision = 8
                self.log.debug(f"[智能精度控制] step_size={step_size} < 0.001，使用8位小数精度")
            
            # 8位小数安全检查
            if precision > 8:
                self.log.warning(f"[精度安全检查] 精度 {precision} 超过8位小数限制，强制设为8位")
                precision = 8
            
            # 格式化数值
            if precision == 0:
                # 整数格式
                result = str(int(decimal_value))
            else:
                # 小数格式
                format_str = f"{{:.{precision}f}}"
                result = format_str.format(float(decimal_value))
            
            self.log.debug(f"[精度格式化完成] value={value} -> {result} (precision={precision})")
            return result
            
        except Exception as e:
            self.log.error(f"[精度格式化失败] value={value}, step_size={step_size}, error={e}")
            return str(value)
    
    def _format_order_params_with_skyusdt_fix(self, symbol, price, quantity, tick_size, step_size):
        """
        优化的订单参数格式化方法（从 maker_channel_enhanced.py 复制）
        """
        try:
            self.log.debug(f"[订单参数格式化] {symbol} 开始格式化: price={price}, quantity={quantity}")
            
            # 使用新的智能精度控制格式化价格
            price_str = self.safe_format_to_precision(price, tick_size)
            
            # 使用新的智能精度控制格式化数量
            qty_str = self.safe_format_to_precision(quantity, step_size)
            
            # 数量调整（确保符合最小交易规则）
            try:
                qty_decimal = Decimal(qty_str)
                step_decimal = Decimal(str(step_size))
                
                # 如果数量小于 step_size，调整为 step_size
                if qty_decimal < step_decimal:
                    qty_str = self.safe_format_to_precision(step_size, step_size)
                    self.log.debug(f"[数量调整] {symbol} 数量小于step_size，调整为: {qty_str}")
                
            except Exception as e:
                self.log.warning(f"[数量调整失败] {symbol} error={e}")
            
            # 计算名义价值用于验证
            try:
                notional = Decimal(price_str) * Decimal(qty_str)
                self.log.debug(f"[名义价值计算] {symbol} notional={notional}")
            except Exception as e:
                self.log.warning(f"[名义价值计算失败] {symbol} error={e}")
            
            self.log.debug(f"[订单参数格式化完成] {symbol} price_str={price_str}, qty_str={qty_str}")
            return price_str, qty_str
            
        except Exception as e:
            self.log.error(f"[订单参数格式化异常] {symbol} error={e}")
            return str(price), str(quantity)

def test_step_size_scenarios():
    """测试各种 step_size 场景"""
    
    print("=" * 80)
    print("开始测试普遍性精度解决方案")
    print("=" * 80)
    
    tester = PrecisionTester()
    
    # 测试场景定义
    test_cases = [
        # 场景1: 大 step_size (>= 1.0) - 整数精度
        {
            "name": "大step_size场景 (整数精度)",
            "symbol": "BTCUSDT",
            "price": 45000.123456,
            "quantity": 1.567890,
            "tick_size": 0.01,
            "step_size": 1.0,
            "expected_precision": 0
        },
        
        # 场景2: 中等 step_size (0.001 <= step_size < 1.0) - 3位小数
        {
            "name": "中等step_size场景 (3位小数)",
            "symbol": "ETHUSDT", 
            "price": 3000.123456,
            "quantity": 0.567890,
            "tick_size": 0.01,
            "step_size": 0.001,
            "expected_precision": 3
        },
        
        # 场景3: 小 step_size (< 0.001) - 8位小数
        {
            "name": "小step_size场景 (8位小数)",
            "symbol": "ADAUSDT",
            "price": 0.123456789,
            "quantity": 100.123456789,
            "tick_size": 0.000001,
            "step_size": 0.0001,
            "expected_precision": 8
        },
        
        # 场景4: 极小 step_size - 8位小数限制
        {
            "name": "极小step_size场景 (8位小数限制)",
            "symbol": "DOGEUSDT",
            "price": 0.000123456789,
            "quantity": 1000.123456789,
            "tick_size": 0.00000001,
            "step_size": 0.00000001,
            "expected_precision": 8
        },
        
        # 场景5: 边界值测试
        {
            "name": "边界值测试",
            "symbol": "BNBUSDT",
            "price": 500.0,
            "quantity": 0.001,
            "tick_size": 0.1,
            "step_size": 0.001,
            "expected_precision": 3
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试场景 {i}: {case['name']}")
        print(f"{'='*60}")
        
        try:
            # 测试 safe_format_to_precision
            print(f"\n1. 测试 safe_format_to_precision:")
            print(f"   输入: value={case['quantity']}, step_size={case['step_size']}")
            
            formatted_qty = tester.safe_format_to_precision(case['quantity'], case['step_size'])
            print(f"   输出: {formatted_qty}")
            
            # 测试 _format_order_params_with_skyusdt_fix
            print(f"\n2. 测试 _format_order_params_with_skyusdt_fix:")
            print(f"   输入: symbol={case['symbol']}, price={case['price']}, quantity={case['quantity']}")
            print(f"         tick_size={case['tick_size']}, step_size={case['step_size']}")
            
            price_str, qty_str = tester._format_order_params_with_skyusdt_fix(
                case['symbol'], case['price'], case['quantity'], 
                case['tick_size'], case['step_size']
            )
            
            print(f"   输出: price_str={price_str}, qty_str={qty_str}")
            
            # 验证结果
            print(f"\n3. 结果验证:")
            
            # 检查数量格式是否符合预期精度
            if '.' in qty_str:
                actual_precision = len(qty_str.split('.')[1])
            else:
                actual_precision = 0
            
            expected_precision = case['expected_precision']
            
            if actual_precision <= expected_precision:
                print(f"   ✓ 精度控制正确: 实际精度={actual_precision}, 预期精度<={expected_precision}")
                success_count += 1
            else:
                print(f"   ✗ 精度控制错误: 实际精度={actual_precision}, 预期精度<={expected_precision}")
            
            # 检查数量是否符合 step_size 规则
            try:
                qty_decimal = Decimal(qty_str)
                step_decimal = Decimal(str(case['step_size']))
                
                if qty_decimal >= step_decimal:
                    print(f"   ✓ 数量规则正确: {qty_str} >= {case['step_size']}")
                else:
                    print(f"   ✗ 数量规则错误: {qty_str} < {case['step_size']}")
                    
            except Exception as e:
                print(f"   ✗ 数量验证失败: {e}")
            
        except Exception as e:
            print(f"   ✗ 测试执行失败: {e}")
    
    # 测试总结
    print(f"\n{'='*80}")
    print(f"测试总结")
    print(f"{'='*80}")
    print(f"总测试场景: {total_count}")
    print(f"成功场景: {success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试场景通过！普遍性精度解决方案验证成功！")
        return True
    else:
        print("❌ 部分测试场景失败，需要进一步优化")
        return False

def test_edge_cases():
    """测试边界情况"""
    
    print(f"\n{'='*80}")
    print("边界情况测试")
    print(f"{'='*80}")
    
    tester = PrecisionTester()
    
    edge_cases = [
        {"name": "零值测试", "value": 0, "step_size": 0.001},
        {"name": "空值测试", "value": None, "step_size": 0.001},
        {"name": "负值测试", "value": -1.23456, "step_size": 0.01},
        {"name": "极大值测试", "value": 999999999.123456789, "step_size": 0.00000001},
        {"name": "极小值测试", "value": 0.00000001, "step_size": 0.00000001},
    ]
    
    for case in edge_cases:
        print(f"\n测试: {case['name']}")
        print(f"输入: value={case['value']}, step_size={case['step_size']}")
        
        try:
            result = tester.safe_format_to_precision(case['value'], case['step_size'])
            print(f"输出: {result}")
            print("✓ 边界情况处理正常")
        except Exception as e:
            print(f"✗ 边界情况处理异常: {e}")

if __name__ == "__main__":
    # 执行主要测试
    main_success = test_step_size_scenarios()
    
    # 执行边界情况测试
    test_edge_cases()
    
    # 最终结果
    print(f"\n{'='*80}")
    if main_success:
        print("🎉 普遍性精度解决方案验证完成！所有核心功能正常工作！")
    else:
        print("❌ 普遍性精度解决方案需要进一步优化")
    print(f"{'='*80}")
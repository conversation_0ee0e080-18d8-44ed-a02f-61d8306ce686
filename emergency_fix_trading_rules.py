#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复：交易规则获取失败问题
解决 tick_size=None, step_size=None 导致的开仓失败
"""

import sys
import os
import time
import math
import logging
from decimal import Decimal
from typing import Dict, Optional, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class EmergencyTradingRulesFix:
    """紧急交易规则修复器"""
    
    def __init__(self, http_client):
        self.http = http_client
        self.rules_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.last_update = {}
        self.logger = logging.getLogger(__name__)
        
        # 已知交易规则备份（从实际API获取的准确值）
        self.known_rules = {
            'PLAYUSDT': {
                'tick_size': 0.00001,
                'step_size': 1.0,
                'min_qty': 1.0,
                'min_notional': 5.0
            },
            'PROVEUSDT': {
                'tick_size': 0.0001,
                'step_size': 1.0,
                'min_qty': 1.0,
                'min_notional': 5.0
            },
            'PIPPINUSDT': {
                'tick_size': 0.00001,
                'step_size': 0.001,
                'min_qty': 0.001,
                'min_notional': 5.0
            },
            'BTCUSDT': {
                'tick_size': 0.01,
                'step_size': 0.001,
                'min_qty': 0.001,
                'min_notional': 5.0
            },
            'ETHUSDT': {
                'tick_size': 0.01,
                'step_size': 0.001,
                'min_qty': 0.001,
                'min_notional': 5.0
            },
            'ADAUSDT': {
                'tick_size': 0.0001,
                'step_size': 1.0,
                'min_qty': 1.0,
                'min_notional': 5.0
            },
            'DOTUSDT': {
                'tick_size': 0.001,
                'step_size': 0.1,
                'min_qty': 0.1,
                'min_notional': 5.0
            }
        }
    
    def get_trading_rules_enhanced(self, symbol: str) -> Optional[Dict[str, Any]]:
        """增强的交易规则获取方法"""
        
        # 方法1: 检查缓存
        if symbol in self.rules_cache:
            cache_time = self.last_update.get(symbol, 0)
            if time.time() - cache_time < self.cache_ttl:
                rules = self.rules_cache[symbol]
                if self._validate_rules(symbol, rules):
                    self.logger.info(f"✅ {symbol} 使用缓存交易规则")
                    return rules
        
        # 方法2: 实时API获取
        try:
            self.logger.info(f"🔄 {symbol} 实时获取交易规则...")
            exchange_info = self.http.get('/fapi/v1/exchangeInfo', {'symbol': symbol})
            
            if exchange_info and 'symbols' in exchange_info and exchange_info['symbols']:
                symbol_info = exchange_info['symbols'][0]
                rules = self._extract_rules(symbol_info)
                
                if self._validate_rules(symbol, rules):
                    self.rules_cache[symbol] = rules
                    self.last_update[symbol] = time.time()
                    self.logger.info(f"✅ {symbol} 实时获取成功: {rules}")
                    return rules
                    
        except Exception as e:
            self.logger.error(f"❌ {symbol} 实时获取失败: {e}")
        
        # 方法3: 使用已知规则
        if symbol in self.known_rules:
            rules = self.known_rules[symbol].copy()
            self.logger.warning(f"⚠️ {symbol} 使用已知规则: {rules}")
            
            # 缓存已知规则
            self.rules_cache[symbol] = rules
            self.last_update[symbol] = time.time()
            return rules
        
        # 方法4: 基于价格推测默认规则
        default_rules = self._get_default_rules_by_price(symbol)
        if default_rules:
            self.logger.warning(f"⚠️ {symbol} 使用推测规则: {default_rules}")
            return default_rules
        
        self.logger.error(f"❌ {symbol} 所有方法都失败，无法获取交易规则")
        return None
    
    def _extract_rules(self, symbol_info: Dict) -> Dict[str, Any]:
        """从交易所信息中提取规则"""
        
        rules = {
            'tick_size': None,
            'step_size': None,
            'min_qty': None,
            'min_notional': None,
        }
        
        # 提取过滤器信息
        for filter_info in symbol_info.get('filters', []):
            filter_type = filter_info.get('filterType')
            
            if filter_type == 'PRICE_FILTER':
                rules['tick_size'] = float(filter_info.get('tickSize', 0))
            elif filter_type == 'LOT_SIZE':
                rules['step_size'] = float(filter_info.get('stepSize', 0))
                rules['min_qty'] = float(filter_info.get('minQty', 0))
            elif filter_type == 'MIN_NOTIONAL':
                rules['min_notional'] = float(filter_info.get('notional', 5.0))
        
        return rules
    
    def _validate_rules(self, symbol: str, rules: Dict) -> bool:
        """验证交易规则的有效性"""
        
        if not rules:
            self.logger.error(f"❌ {symbol} 规则为空")
            return False
        
        # 检查关键字段
        required_fields = ['tick_size', 'step_size', 'min_qty']
        for field in required_fields:
            value = rules.get(field)
            if value is None or value == 0:
                self.logger.error(f"❌ {symbol} {field} 无效: {value}")
                return False
        
        # 检查数值有效性
        try:
            step_size = Decimal(str(rules['step_size']))
            tick_size = Decimal(str(rules['tick_size']))
            min_qty = Decimal(str(rules['min_qty']))
            
            if step_size <= 0 or tick_size <= 0 or min_qty <= 0:
                self.logger.error(f"❌ {symbol} 规则数值无效")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 规则格式错误: {e}")
            return False
        
        self.logger.info(f"✅ {symbol} 规则验证通过")
        return True
    
    def _get_default_rules_by_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """基于价格推测默认规则"""
        
        try:
            # 获取当前价格来推测规则
            ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
            if not ticker or 'price' not in ticker:
                return None
                
            price = float(ticker['price'])
            
            if price >= 100:  # 高价币
                return {
                    'tick_size': 0.01,
                    'step_size': 0.001,
                    'min_qty': 0.001,
                    'min_notional': 5.0,
                }
            elif price >= 1:  # 中价币
                return {
                    'tick_size': 0.0001,
                    'step_size': 0.01,
                    'min_qty': 0.01,
                    'min_notional': 5.0,
                }
            elif price >= 0.01:  # 低价币
                return {
                    'tick_size': 0.00001,
                    'step_size': 1.0,
                    'min_qty': 1.0,
                    'min_notional': 5.0,
                }
            else:  # 极低价币
                return {
                    'tick_size': 0.000001,
                    'step_size': 10.0,
                    'min_qty': 10.0,
                    'min_notional': 5.0,
                }
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 无法获取价格推测规则: {e}")
        
        return None
    
    def smart_retry_mechanism(self, symbol: str, error_code: int, attempt: int) -> str:
        """智能重试机制"""
        
        if error_code == -1111:  # 精度错误
            if attempt == 1:
                # 第一次精度错误：刷新交易规则
                self.logger.warning(f"{symbol} 精度错误，刷新交易规则")
                if symbol in self.rules_cache:
                    del self.rules_cache[symbol]
                if symbol in self.last_update:
                    del self.last_update[symbol]
                return 'refresh_rules'
            else:
                # 重复精度错误：跳过此次交易
                self.logger.error(f"{symbol} 重复精度错误，跳过交易")
                return 'skip'
        
        elif error_code == -1022:  # 签名错误
            # 签名错误：刷新时间戳
            self.logger.warning(f"{symbol} 签名错误，刷新时间戳")
            return 'refresh_timestamp'
        
        else:
            # 其他错误：正常重试
            return 'normal_retry'
    
    def verify_precision_fix(self, symbol: str, original_qty: float, fixed_qty: float, step_size: float) -> bool:
        """验证精度修复效果"""
        
        if not step_size or step_size == 0:
            self.logger.error(f"{symbol} step_size无效: {step_size}")
            return False
        
        try:
            remainder = Decimal(str(fixed_qty)) % Decimal(str(step_size))
            is_compliant = abs(remainder) < Decimal('1e-15')
            
            self.logger.info(f"{symbol} 精度验证: {fixed_qty} % {step_size} = {remainder}, 合规: {is_compliant}")
            return is_compliant
            
        except Exception as e:
            self.logger.error(f"{symbol} 精度验证失败: {e}")
            return False
    
    def _round_price(self, price: float, tick_size: float) -> float:
        """价格精度处理"""
        try:
            if tick_size <= 0:
                return price
            
            # 计算小数位数
            decimal_places = max(0, -int(math.floor(math.log10(tick_size))))
            
            # 向下舍入到最近的tick_size倍数
            rounded_price = math.floor(price / tick_size) * tick_size
            
            # 格式化到正确的小数位数
            return round(rounded_price, decimal_places)
        
        except Exception as e:
            self.logger.error(f"价格精度处理失败: {e}")
            return price
    
    def _round_qty(self, qty: float, step_size: float) -> float:
        """数量精度处理"""
        try:
            if step_size <= 0:
                return qty
            
            # 计算小数位数
            decimal_places = max(0, -int(math.floor(math.log10(step_size))))
            
            # 向下舍入到最近的step_size倍数
            rounded_qty = math.floor(qty / step_size) * step_size
            
            # 格式化到正确的小数位数
            return round(rounded_qty, decimal_places)
        
        except Exception as e:
            self.logger.error(f"数量精度处理失败: {e}")
            return qty

    def smart_nominal_value_adjustment(self, symbol: str, price: float, qty: float, min_notional: float) -> float:
        """智能名义价值调整"""
        try:
            current_notional = price * qty
            
            if current_notional < min_notional:
                # 调整数量以满足最小名义价值要求
                adjusted_qty = min_notional / price
                
                # 确保调整后的数量符合步长要求
                rules = self.get_trading_rules_enhanced(symbol)
                if rules and 'step_size' in rules:
                    step_size = rules['step_size']
                    adjusted_qty = self._round_qty(adjusted_qty, step_size)
                
                self.logger.info(f"📊 {symbol} 名义价值调整: {qty} -> {adjusted_qty} (价格: {price}, 最小名义价值: {min_notional})")
                return adjusted_qty
            
            return qty
        
        except Exception as e:
            self.logger.error(f"❌ {symbol} 名义价值调整失败: {e}")
            return qty


def test_emergency_fix():
    """测试紧急修复功能"""
    
    # 模拟HTTP客户端
    class MockHttpClient:
        def get(self, endpoint, params=None):
            if 'exchangeInfo' in endpoint:
                return {
                    'symbols': [{
                        'symbol': params.get('symbol', 'PLAYUSDT'),
                        'filters': [
                            {'filterType': 'PRICE_FILTER', 'tickSize': '0.00001'},
                            {'filterType': 'LOT_SIZE', 'stepSize': '1.0', 'minQty': '1.0'},
                            {'filterType': 'MIN_NOTIONAL', 'notional': '5.0'}
                        ]
                    }]
                }
            elif 'ticker/price' in endpoint:
                return {'price': '0.001234'}
            return None
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
    
    # 测试修复器
    mock_client = MockHttpClient()
    fixer = EmergencyTradingRulesFix(mock_client)
    
    # 测试获取交易规则
    rules = fixer.get_trading_rules_enhanced('PLAYUSDT')
    print(f"获取到的规则: {rules}")
    
    # 测试精度验证
    is_valid = fixer.verify_precision_fix('PLAYUSDT', 123.456, 123.0, 1.0)
    print(f"精度验证结果: {is_valid}")
    
    # 测试重试机制
    retry_action = fixer.smart_retry_mechanism('PLAYUSDT', -1111, 1)
    print(f"重试策略: {retry_action}")


if __name__ == '__main__':
    test_emergency_fix()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试XANUSDT的K线数据获取问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy.maker_channel_enhanced import MakerChannelEnhanced
from http_client import HttpClient
import yaml
import pandas as pd

def debug_xan_klines():
    print("=== 调试XANUSDT K线数据获取 ===\n")
    
    # 初始化策略
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    http = HttpClient(
        config['api_key'], 
        config['api_secret'], 
        config.get('base_url', 'https://fapi.binance.com'), 
        config.get('verify_ssl', True)
    )
    strategy = MakerChannelEnhanced(http, config)
    
    symbol = 'XANUSDT'
    
    print(f"1. 检查{symbol}基本信息:")
    try:
        strategy.load_symbols_with_cache()
        info = strategy.symbols_info.get(symbol, {})
        print(f"  {symbol}信息: {info}")
        print(f"  上线天数: {info.get('age_days', 'N/A')}天")
    except Exception as e:
        print(f"  获取基本信息失败: {e}")
    
    print(f"\n2. 尝试获取{symbol}的K线数据:")
    
    # 测试不同时间周期和数量的K线获取
    test_cases = [
        ('1m', 10),
        ('1m', 50),
        ('5m', 10),
        ('5m', 50),
        ('15m', 10),
        ('15m', 50),
        ('1h', 10),
        ('1h', 50),
        ('1d', 10),
    ]
    
    for interval, limit in test_cases:
        try:
            print(f"\n  尝试获取 {interval} 周期，{limit} 根K线:")
            df = strategy.get_klines(symbol, interval, limit)
            if df is not None and len(df) > 0:
                print(f"    ✓ 成功获取 {len(df)} 根K线")
                print(f"    时间范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"    最新价格: {df['c'].iloc[-1]}")
                print(f"    数据样本:")
                print(df.tail(3).to_string())
            else:
                print(f"    ✗ 获取失败或数据为空")
        except Exception as e:
            print(f"    ✗ 获取异常: {e}")
    
    print(f"\n3. 测试缓存机制:")
    try:
        # 测试缓存获取
        cache_key = f"kl15_{symbol}_50"
        df_cached = strategy.cache_get(cache_key, strategy._raw_klines, symbol, '15m', 50)
        if df_cached is not None:
            print(f"  ✓ 缓存获取成功，{len(df_cached)} 根K线")
        else:
            print(f"  ✗ 缓存获取失败")
    except Exception as e:
        print(f"  ✗ 缓存测试异常: {e}")
    
    print(f"\n4. 测试原始API调用:")
    try:
        # 直接调用API
        params = {
            'symbol': symbol,
            'interval': '15m',
            'limit': 50
        }
        raw_data = strategy.http.get('/fapi/v1/klines', params)
        if raw_data:
            print(f"  ✓ 原始API调用成功，获取 {len(raw_data)} 条数据")
            if len(raw_data) > 0:
                print(f"  最新数据: {raw_data[-1]}")
        else:
            print(f"  ✗ 原始API调用失败")
    except Exception as e:
        print(f"  ✗ 原始API调用异常: {e}")
    
    print(f"\n5. 检查币种是否存在于交易所:")
    try:
        # 获取24小时行情
        ticker = strategy.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
        if ticker:
            print(f"  ✓ {symbol}存在于交易所")
            print(f"  24h涨幅: {ticker.get('priceChangePercent', 'N/A')}%")
            print(f"  24h成交额: {ticker.get('quoteVolume', 'N/A')} USDT")
            print(f"  当前价格: {ticker.get('lastPrice', 'N/A')}")
        else:
            print(f"  ✗ {symbol}不存在于交易所或获取失败")
    except Exception as e:
        print(f"  ✗ 检查币种异常: {e}")

if __name__ == "__main__":
    debug_xan_klines()
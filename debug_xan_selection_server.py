#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from strategy.maker_channel_enhanced import MakerChannelEnhanced
from http_client import HttpClient
import yaml
import pandas as pd

def debug_xan_selection_server():
    print("=== 服务器环境XANUSDT筛选调试 ===\n")
    
    # 初始化策略
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    http = HttpClient(
        config['api_key'], 
        config['api_secret'], 
        config.get('base_url', 'https://fapi.binance.com'), 
        config.get('verify_ssl', True)
    )
    strategy = MakerChannelEnhanced(http, config)
    
    # 1. 检查XANUSDT基本信息
    print("1. 检查XANUSDT基本信息:")
    strategy.load_symbols_with_cache()
    symbols_info = strategy.symbols_info
    if 'XANUSDT' in symbols_info:
        xan_info = symbols_info['XANUSDT']
        print(f"  XANUSDT信息: {xan_info}")
        print(f"  上线天数: {xan_info.get('age_days', 'N/A')}天")
        print(f"  24h涨幅: {xan_info.get('change_24h', 'N/A')}%")
        print(f"  成交额: {xan_info.get('volume_usdt_24h', 'N/A')} USDT")
        print(f"  0.1%深度: {xan_info.get('depth_0_1_percent', 'N/A')} USDT")
    else:
        print("  ✗ XANUSDT不在币种信息中")
        return
    
    # 2. 检查策略配置
    print(f"\n2. 检查策略配置:")
    print(f"  策略模式: {strategy.strategy_mode}")
    print(f"  新币最大天数: {strategy.new_coin_max_age_days}")
    print(f"  新币动量范围: {strategy.new_coin_momentum_range}")
    print(f"  新币深度最小值: {strategy.new_coin_depth_min}")
    
    # 3. 测试新币突破筛选
    print(f"\n3. 测试新币突破筛选:")
    try:
        newcoin_pool = strategy.get_newcoin_breakthrough()
        print(f"  新币突破池数量: {len(newcoin_pool)}")
        if 'XANUSDT' in newcoin_pool:
            print(f"  ✓ XANUSDT在新币突破池中")
        else:
            print(f"  ✗ XANUSDT不在新币突破池中")
            print(f"  新币突破池: {list(newcoin_pool.keys())[:10]}")
    except Exception as e:
        print(f"  ✗ 新币突破筛选失败: {e}")
    
    # 4. 测试龙头池筛选
    print(f"\n4. 测试龙头池筛选:")
    try:
        leader_pool = strategy.get_top50_market_leaders()
        print(f"  龙头池数量: {len(leader_pool)}")
        if 'XANUSDT' in leader_pool:
            print(f"  ✓ XANUSDT在龙头池中")
        else:
            print(f"  ✗ XANUSDT不在龙头池中")
            print(f"  龙头池前10: {list(leader_pool.keys())[:10]}")
    except Exception as e:
        print(f"  ✗ 龙头池筛选失败: {e}")
    
    # 5. 详细分析XANUSDT筛选条件
    print(f"\n5. 详细分析XANUSDT筛选条件:")
    
    # 检查币龄条件
    age_days = xan_info.get('age_days', 0)
    print(f"  币龄检查: {age_days}天 <= {strategy.new_coin_max_age_days}天 -> {'✓' if age_days <= strategy.new_coin_max_age_days else '✗'}")
    
    # 检查涨幅条件
    change_24h = xan_info.get('change_24h', 0)
    momentum_min, momentum_max = strategy.new_coin_momentum_range
    print(f"  涨幅检查: {momentum_min}% <= {change_24h}% <= {momentum_max}% -> {'✓' if momentum_min <= change_24h <= momentum_max else '✗'}")
    
    # 检查深度条件
    depth = xan_info.get('depth_0_1_percent', 0)
    print(f"  深度检查: {depth} USDT >= {strategy.new_coin_depth_min} USDT -> {'✓' if depth >= strategy.new_coin_depth_min else '✗'}")
    
    # 检查K线数据
    try:
        klines = strategy.get_klines('XANUSDT', '5m', 50)
        if klines is not None and len(klines) >= 20:
            print(f"  K线数据: 获取到{len(klines)}根K线 >= 20根 -> ✓")
            
            # 检查通道突破条件
            try:
                df = pd.DataFrame(klines)
                df.columns = ['timestamp', 'o', 'h', 'l', 'c', 'v', 'ct', 'q', 'n', 'V', 'qV', 'ignore']
                df = df.astype(float)
                
                # 计算通道
                period = strategy.channel_period
                multiplier = strategy.channel_multiplier
                
                df['sma'] = df['c'].rolling(window=period).mean()
                df['std'] = df['c'].rolling(window=period).std()
                df['upper'] = df['sma'] + multiplier * df['std']
                df['lower'] = df['sma'] - multiplier * df['std']
                
                latest = df.iloc[-1]
                close_price = latest['c']
                high_price = latest['h']
                upper_band = latest['upper']
                
                print(f"  通道分析:")
                print(f"    最新收盘价: {close_price}")
                print(f"    最新最高价: {high_price}")
                print(f"    通道上轨: {upper_band}")
                print(f"    收盘价突破上轨: {close_price >= upper_band * 0.995} -> {'✓' if close_price >= upper_band * 0.995 else '✗'}")
                print(f"    最高价突破上轨: {high_price > upper_band} -> {'✓' if high_price > upper_band else '✗'}")
                
                # 检查价格在通道中上部
                sma = latest['sma']
                in_upper_half = close_price > sma
                print(f"    价格在通道中上部: {close_price} > {sma} -> {'✓' if in_upper_half else '✗'}")
                
            except Exception as e:
                print(f"  ✗ 通道分析失败: {e}")
                
        else:
            print(f"  K线数据: 获取到{len(klines) if klines else 0}根K线 < 20根 -> ✗")
    except Exception as e:
        print(f"  ✗ K线数据获取失败: {e}")
    
    # 6. 最终筛选结果
    print(f"\n6. 最终筛选结果:")
    try:
        final_pool = strategy.refresh_top1()
        print(f"  最终池子数量: {len(final_pool)}")
        if 'XANUSDT' in final_pool:
            print(f"  ✓ XANUSDT在最终池子中")
            score = final_pool['XANUSDT'].get('total_score', 0)
            print(f"  XANUSDT总分: {score}")
        else:
            print(f"  ✗ XANUSDT不在最终池子中")
            print(f"  最终池子前10: {list(final_pool.keys())[:10]}")
    except Exception as e:
        print(f"  ✗ 最终筛选失败: {e}")

if __name__ == "__main__":
    debug_xan_selection_server()
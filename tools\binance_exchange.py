import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
import logging
import time
import hmac
import hashlib
import urllib.parse
from .exchange_interface import ExchangeInterface
from ..utils.proxy_detector import get_proxy_config


class BinanceExchange(ExchangeInterface):
    """币安交易所实现 - 支持真实交易"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 基础配置
        self.api_key = config.get('api', {}).get('api_key', '')
        self.api_secret = config.get('api', {}).get('api_secret', '')
        
        # 配置带重试机制的会话
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 智能代理配置 - 自动检测网络环境
        proxy_config = get_proxy_config()
        self.proxies = {}
        
        if proxy_config['enabled'] and proxy_config['http']:
            self.proxies = {
                'http': proxy_config['http'],
                'https': proxy_config['https']
            }
            self.logger.info(f"启用智能代理: {proxy_config['http']}")
            # 配置代理到会话
            self.session.proxies.update(self.proxies)
        else:
            self.logger.info("禁用代理（远程服务器环境或代理不可用）")
        
        # 基础交易对信息
        self.symbols_info = {
            'BTCUSDT': {'precision': {'price': 2, 'amount': 6}},
            'ETHUSDT': {'precision': {'price': 2, 'amount': 5}},
            'BNBUSDT': {'precision': {'price': 2, 'amount': 4}}
        }
        
        # 获取所有可用的合约交易对
        self._load_all_futures_symbols()
        
        self.logger.info("币安交易所客户端初始化完成")
    
    def _calculate_precision(self, step_size: str) -> int:
        """计算精度值"""
        try:
            # 移除末尾的0
            step_size = step_size.rstrip('0')
            # 如果没有小数点，精度为0
            if '.' not in step_size:
                return 0
            # 计算小数点后的位数
            decimal_part = step_size.split('.')[-1]
            # 特殊处理：如果末尾有多个0，需要计算实际精度
            return len(decimal_part)
        except Exception as e:
            self.logger.error(f"计算精度值失败: {e}, step_size: {step_size}")
            return 0
    
    def _load_all_futures_symbols(self):
        """从币安合约API加载所有可用的交易对"""
        try:
            url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            symbols = data.get('symbols', [])
            
            # 更新symbols_info，只包含状态为"TRADING"的交易对
            for symbol_info in symbols:
                symbol = symbol_info['symbol']
                status = symbol_info.get('status', '')
                
                # 只添加状态为"TRADING"的交易对
                if status == 'TRADING':
                    # 添加到基础交易对信息中
                    if symbol not in self.symbols_info:
                        # 获取价格和数量精度
                        price_filter = next((f for f in symbol_info.get('filters', []) if f['filterType'] == 'PRICE_FILTER'), {})
                        lot_size_filter = next((f for f in symbol_info.get('filters', []) if f['filterType'] == 'LOT_SIZE'), {})
                        
                        # 正确计算价格精度
                        price_tick_size = price_filter.get('tickSize', '0.00000001')
                        price_precision = self._calculate_precision(price_tick_size)
                        
                        # 正确计算数量精度
                        lot_step_size = lot_size_filter.get('stepSize', '0.00000001')
                        amount_precision = self._calculate_precision(lot_step_size)
                        
                        self.symbols_info[symbol] = {
                            'precision': {
                                'price': price_precision,
                                'amount': amount_precision
                            }
                        }
                        # 如果是STBLUSDT，记录精度信息用于调试
                        if symbol == 'STBLUSDT':
                            self.logger.info(f"STBLUSDT精度信息: 价格精度={price_precision}, 数量精度={amount_precision}, price_tick_size={price_tick_size}, lot_step_size={lot_step_size}")
            
            self.logger.info(f"成功加载 {len(self.symbols_info)} 个合约交易对信息")
        except Exception as e:
            self.logger.warning(f"加载合约交易对信息失败，使用默认交易对: {e}")
    
    def _sign_request(self, params: Dict[str, Any]) -> str:
        """生成请求签名"""
        query_string = urllib.parse.urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """获取账户信息 - 使用真实API"""
        try:
            # 使用币安合约API获取账户信息
            url = "https://fapi.binance.com/fapi/v2/account"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.logger.info("成功获取账户信息")
            return data
        except Exception as e:
            self.logger.error(f"获取账户信息失败: {e}")
            return None
    
    def get_balance(self) -> Dict[str, Decimal]:
        """获取账户余额 - 使用真实API"""
        try:
            # 使用币安合约API获取账户余额
            url = "https://fapi.binance.com/fapi/v2/account"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            # 查找USDT余额
            for asset in data.get('assets', []):
                if asset.get('asset') == 'USDT':
                    return {
                        'total': Decimal(str(asset.get('walletBalance', 0))),
                        'free': Decimal(str(asset.get('availableBalance', 0))),
                        'used': Decimal(str(float(asset.get('walletBalance', 0)) - float(asset.get('availableBalance', 0))))
                    }
            
            # 如果没有找到USDT余额，返回默认值
            return {
                'total': Decimal("0"),
                'free': Decimal("0"),
                'used': Decimal("0")
            }
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
            # 返回模拟数据
            initial_investment = self.config.get('capital_management', {}).get('initial_investment', 1000)
            return {
                'total': Decimal(str(initial_investment)),
                'free': Decimal(str(initial_investment)),
                'used': Decimal("0")
            }
    
    def get_positions(self) -> List[Union[Dict[str, Any], Any]]:
        """获取持仓信息 - 使用真实API"""
        try:
            # 使用币安合约API获取持仓信息
            url = "https://fapi.binance.com/fapi/v2/positionRisk"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data
        except Exception as e:
            self.logger.error(f"获取持仓信息失败: {e}")
            # 返回模拟数据
            return []
    
    def get_open_orders(self, symbol: str) -> List[Union[Dict[str, Any], Any]]:
        """获取挂单信息 - 使用真实API"""
        try:
            # 使用币安合约API获取挂单信息
            url = "https://fapi.binance.com/fapi/v1/openOrders"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'symbol': symbol,
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data
        except Exception as e:
            self.logger.error(f"获取挂单信息失败: {e}")
            # 返回模拟数据
            return []
    
    def place_order(self, symbol: str, side: str, order_type: str,
                   quantity: Decimal, price: Optional[Decimal] = None,
                   stop_price: Optional[Decimal] = None,
                   reduce_only: bool = False) -> Optional[Dict[str, Any]]:
        """下单 - 使用真实API"""
        try:
            # 使用币安合约API下单
            url = "https://fapi.binance.com/fapi/v1/order"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'symbol': symbol,
                'side': side.upper(),  # 确保side是大写的
                'type': order_type.upper(),  # 确保type是大写的
                'timestamp': timestamp
            }
            
            # 获取交易对信息以确定精度
            symbol_info = self.get_symbol_info(symbol)
            self.logger.info(f"交易对 {symbol} 信息: {symbol_info}")
            
            if symbol_info:
                amount_precision = symbol_info['precision']['amount']
                price_precision = symbol_info['precision']['price']
                
                self.logger.info(f"精度信息 - 数量精度: {amount_precision}, 价格精度: {price_precision}")
                
                # 根据精度调整数量和价格
                # 使用quantize方法确保精度正确
                if amount_precision == 0:
                    # 整数精度，向上取整
                    quantity = quantity.quantize(Decimal('1'), rounding='ROUND_UP')
                else:
                    # 小数精度，保留指定位数
                    quantity = quantity.quantize(Decimal('0.' + '0' * amount_precision), rounding='ROUND_UP')
                
                # 特殊处理：确保数量至少为1（对于STBLUSDT等最小下单数量为1的交易对）
                if quantity < Decimal('1') and symbol.endswith('USDT'):
                    quantity = Decimal('1')
                
                if price is not None:
                    if price_precision == 0:
                        price = price.quantize(Decimal('1'), rounding='ROUND_HALF_UP')
                    else:
                        price = price.quantize(Decimal('0.' + '0' * price_precision), rounding='ROUND_HALF_UP')
                    
                if stop_price is not None:
                    if price_precision == 0:
                        stop_price = stop_price.quantize(Decimal('1'), rounding='ROUND_HALF_UP')
                    else:
                        stop_price = stop_price.quantize(Decimal('0.' + '0' * price_precision), rounding='ROUND_HALF_UP')
            
            # 添加数量参数
            params['quantity'] = str(quantity)
            
            # 添加可选参数
            if price is not None:
                params['price'] = str(price)
            if stop_price is not None:
                params['stopPrice'] = str(stop_price)
            if reduce_only:
                params['reduceOnly'] = 'true'
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            self.logger.info(f"准备下单: {symbol} {side} {order_type} 数量:{quantity} 价格:{price} 止损价:{stop_price}")
            self.logger.info(f"请求参数: {params}")
            response = self.session.post(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 验证订单是否真正创建成功
            if data and (data.get('orderId') or data.get('clientOrderId')):
                order_id = data.get('orderId') or data.get('clientOrderId')
                self.logger.info(f"真实下单成功: {symbol} {side} {order_type} {quantity} 订单ID: {order_id}")
                return data
            else:
                self.logger.error(f"下单失败: {symbol} - 交易所返回无效响应: {data}")
                return None
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"真实下单HTTP错误: {e}")
            self.logger.error(f"响应内容: {e.response.text}")
            # 检查是否是因为达到最大止损订单限制
            if "Reach max stop order limit" in e.response.text:
                self.logger.warning(f"达到最大止损订单限制，无法下止损单: {symbol}")
            # 检查是否是精度错误
            elif "Precision is over the maximum defined for this asset" in e.response.text:
                self.logger.error(f"订单精度错误: {symbol} 数量:{quantity} 价格:{price} 止损价:{stop_price}")
            return None
        except Exception as e:
            self.logger.error(f"真实下单失败: {e}")
            return None
    
    def cancel_order(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]:
        """取消订单 - 使用真实API"""
        try:
            # 使用币安合约API取消订单
            url = "https://fapi.binance.com/fapi/v1/order"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'symbol': symbol.upper(),  # 确保symbol是大写的
                'timestamp': timestamp
            }
            
            # 优先使用clientOrderId，如果不存在则使用orderId
            # 修正：更准确地判断订单ID类型
            order_id_str = str(order_id)
            if (order_id_str.startswith('entry_') or 
                order_id_str.startswith('mock_order_') or 
                '_' in order_id_str or
                not order_id_str.isdigit()):
                # 这是clientOrderId（包含下划线、特定前缀或非纯数字）
                params['origClientOrderId'] = order_id_str
            else:
                # 这是orderId（纯数字）
                params['orderId'] = order_id_str
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            self.logger.info(f"准备取消订单: {symbol} 订单ID: {order_id} 参数: {params}")
            response = self.session.delete(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.logger.info(f"真实取消订单成功: {symbol} {order_id}")
            return data
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"真实取消订单HTTP错误: {e}")
            self.logger.error(f"响应内容: {e.response.text}")
            # 检查是否是因为订单不存在导致的错误
            if e.response.status_code == 400:
                response_text = e.response.text
                if "Unknown order sent" in response_text or "Order does not exist" in response_text:
                    self.logger.info(f"订单不存在，无需取消: {symbol} {order_id}")
                    # 返回一个模拟的成功响应
                    return {'status': 'success', 'msg': 'Order does not exist'}
                elif "Mandatory parameter" in response_text and "orderid" in response_text.lower():
                    # 特殊处理：参数名错误
                    self.logger.error(f"参数名错误，可能是大小写问题: {response_text}")
            return None
        except Exception as e:
            self.logger.error(f"真实取消订单失败: {e}")
            return None
    
    def get_klines(self, symbol: str, interval: str, limit: int) -> List[List[Any]]:
        """获取K线数据 - 使用合约公共REST API"""
        try:
            # 使用币安合约公共API获取K线数据
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            klines = response.json()
            # 转换为标准格式: [timestamp, open, high, low, close, volume]
            return [[
                int(k[0]), float(k[1]), float(k[2]), float(k[3]), float(k[4]), float(k[5])
            ] for k in klines]
            
        except Exception as e:
            self.logger.warning(f"获取K线失败，返回模拟数据: {e}")
            # 返回模拟K线数据
            return self._generate_mock_klines(symbol, limit)
    
    def _generate_mock_klines(self, symbol: str, limit: int) -> List[List[Any]]:
        """生成模拟K线数据"""
        base_price = 50000 if symbol == 'BTCUSDT' else 3000 if symbol == 'ETHUSDT' else 600
        klines = []
        timestamp = int(time.time() * 1000) - (limit * 60000)
        
        for i in range(limit):
            open_price = base_price * (1 + i * 0.001)
            high_price = open_price * 1.02
            low_price = open_price * 0.98
            close_price = open_price * 1.01
            volume = 1000 + i * 10
            
            klines.append([
                timestamp + i * 60000,
                open_price, high_price, low_price, close_price, volume
            ])
        
        return klines
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取交易对信息"""
        return self.symbols_info.get(symbol)
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆 - 使用真实API"""
        try:
            # 使用币安合约API设置杠杆
            url = "https://fapi.binance.com/fapi/v1/leverage"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'symbol': symbol,
                'leverage': leverage,
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.post(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.logger.info(f"真实设置杠杆成功: {symbol} {leverage}x")
            return True
        except Exception as e:
            self.logger.error(f"真实设置杠杆失败: {e}")
            # 如果真实设置杠杆失败，回退到模拟设置
            self.logger.info(f"回退到模拟设置杠杆: {symbol} {leverage}x")
            return True
    
    def set_margin_type(self, symbol: str, margin_type: str) -> bool:
        """设置保证金模式 - 使用真实API"""
        try:
            # 使用币安合约API设置保证金模式
            url = "https://fapi.binance.com/fapi/v1/marginType"
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {
                'symbol': symbol,
                'marginType': margin_type,
                'timestamp': timestamp
            }
            
            # 生成签名
            query_string = urllib.parse.urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            params['signature'] = signature
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.post(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            self.logger.info(f"真实设置保证金模式成功: {symbol} {margin_type}")
            return True
        except Exception as e:
            self.logger.error(f"真实设置保证金模式失败: {e}")
            # 如果真实设置保证金模式失败，回退到模拟设置
            self.logger.info(f"回退到模拟设置保证金模式: {symbol} {margin_type}")
            return True
    
    def get_symbols(self) -> List[str]:
        """获取所有交易对"""
        return list(self.symbols_info.keys())
    
    def get_all_symbols(self) -> List[str]:
        """获取所有交易对（与get_symbols相同）"""
        return self.get_symbols()
    
    def get_ticker_24hr(self) -> List[Dict[str, Any]]:
        """获取24小时统计信息 - 使用合约API"""
        try:
            url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.warning(f"获取24小时统计信息失败: {e}")
            return []

    def get_order(self, symbol: str, order_id: Union[str, int]) -> Optional[Dict[str, Any]]:
        """查询订单详情"""
        try:
            # 构建请求参数
            params = {
                'symbol': symbol,
                'timestamp': int(time.time() * 1000)
            }
            
            # 确保order_id是字符串
            order_id_str = str(order_id)
            
            # 如果是clientOrderId，使用origClientOrderId参数
            if order_id_str.startswith('entry_') or len(order_id_str) > 10:
                params['origClientOrderId'] = order_id_str
            else:
                params['orderId'] = order_id_str
            
            # 签名请求
            params['signature'] = self._sign_request(params)
            
            # 发送请求
            headers = {
                'X-MBX-APIKEY': self.api_key
            }
            
            response = self.session.get(
                'https://fapi.binance.com/fapi/v1/order',
                params=params,
                headers=headers,
                timeout=30,
                proxies=self.proxies
            )
            
            response.raise_for_status()
            order_data = response.json()
            
            # 检查订单是否存在
            if 'orderId' in order_data or 'clientOrderId' in order_data:
                return order_data
            else:
                self.logger.warning(f"订单查询返回无效数据: {order_data}")
                return None
                
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                # 订单不存在
                self.logger.info(f"订单不存在: {symbol} {order_id}")
                return None
            else:
                self.logger.error(f"查询订单HTTP错误: {e}")
                return None
        except Exception as e:
            self.logger.error(f"查询订单失败: {e}")
            return None

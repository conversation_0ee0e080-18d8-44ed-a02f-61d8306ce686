#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地测试ORDERUSDT的交易规则和精度处理
基于常见新币的交易规则进行模拟测试
"""

import decimal as dec
from decimal import Decimal

def test_orderusdt_precision():
    """测试ORDERUSDT的精度处理"""
    print("=== ORDERUSDT精度处理测试 ===")
    
    # 基于常见新币的交易规则，ORDERUSDT可能的规则
    possible_rules = [
        {
            "name": "新币标准规则1",
            "tick_size": 0.00001,    # 5位小数
            "step_size": 0.01,       # 2位小数
            "min_qty": 0.01,
            "min_notional": 5.0
        },
        {
            "name": "新币标准规则2", 
            "tick_size": 0.000001,   # 6位小数
            "step_size": 0.001,      # 3位小数
            "min_qty": 0.001,
            "min_notional": 5.0
        },
        {
            "name": "新币标准规则3",
            "tick_size": 0.0001,     # 4位小数
            "step_size": 0.1,        # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0
        }
    ]
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际下单参数:")
    print(f"  数量: {actual_qty}")
    print(f"  价格: {actual_price}")
    print(f"  名义价值: {actual_qty * actual_price:.6f} USDT")
    
    for rules in possible_rules:
        print(f"\n--- {rules['name']} ---")
        test_with_rules(actual_qty, actual_price, rules)

def test_with_rules(qty, price, rules):
    """使用特定规则测试精度处理"""
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules['min_notional']
    
    print(f"交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 使用策略中的精度处理函数
    rounded_price = _round_price(price, tick_size)
    rounded_qty = _round_qty(qty, step_size, min_qty, rounded_price, min_notional)
    
    print(f"精度处理后:")
    print(f"  价格: {rounded_price}")
    print(f"  数量: {rounded_qty}")
    print(f"  名义价值: {rounded_qty * rounded_price:.6f} USDT")
    
    # 测试降档重试逻辑
    print(f"降档重试测试:")
    retry_scales = [1.0, 0.75, 0.5, 0.25]
    
    for scale in retry_scales:
        q_try = rounded_qty * scale
        q_try = _round_qty(q_try, step_size, min_qty, rounded_price, min_notional)
        
        # 模拟下单参数格式化
        price_str, qty_str = format_order_params(rounded_price, q_try, tick_size, step_size, min_qty)
        
        # 检查精度
        price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
        qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
        
        # 计算允许的最大小数位数
        tick_decimals = get_decimal_places(tick_size)
        step_decimals = get_decimal_places(step_size)
        
        precision_ok = price_decimals <= tick_decimals and qty_decimals <= step_decimals
        notional_ok = q_try * rounded_price >= min_notional
        
        status = "✅" if precision_ok and notional_ok else "❌"
        print(f"  {int(scale*100)}%: 数量={qty_str}, 价格={price_str}, 名义价值={q_try * rounded_price:.6f} {status}")
        
        if not precision_ok:
            print(f"    精度问题: 价格{price_decimals}位(≤{tick_decimals}), 数量{qty_decimals}位(≤{step_decimals})")
        if not notional_ok:
            print(f"    名义价值不足: {q_try * rounded_price:.6f} < {min_notional}")

def format_order_params(price, qty, tick_size, step_size, min_qty):
    """格式化下单参数（复制自策略代码的逻辑）"""
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (Decimal(str(price)) / Decimal(str(tick_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(tick_size))
        price_str = format(price_dec.normalize(), 'f')
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (Decimal(str(qty)) / Decimal(str(step_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(step_size))
        qty_dec = max(qty_dec, Decimal(str(min_qty or 0)))
        qty_str = format(qty_dec.normalize(), 'f')
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def get_decimal_places(value):
    """计算小数位数"""
    if value == 0:
        return 0
    str_val = f"{value:.20f}".rstrip('0')
    if '.' in str_val:
        return len(str_val.split('.')[-1])
    return 0

def _round_price(price, tick_size):
    """价格精度处理（复制自策略代码）"""
    if price <= 0 or tick_size <= 0:
        return price
    dec.getcontext().prec = 18
    tick_d = Decimal(str(tick_size))
    price_d = Decimal(str(price))
    rounded = float((price_d / tick_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
    return max(rounded, tick_size)

def _round_qty(q, step, min_q, price=None, min_notional=None):
    """数量精度处理（复制自策略代码）"""
    if q <= 0 or step <= 0:
        return min_q
    dec.getcontext().prec = 18
    step_d = Decimal(str(step))
    
    # 先按步长向下取整
    rounded_down = float((Decimal(str(q)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
    rounded_down = max(rounded_down, min_q)
    
    # 如果提供了价格和最小名义价值，检查是否满足要求
    if price and min_notional:
        notional = rounded_down * price
        if notional < min_notional:
            # 向上调整到满足最小名义价值的数量
            required_qty = min_notional / price
            rounded_up = float((Decimal(str(required_qty)) / step_d).quantize(Decimal('1'), rounding=dec.ROUND_UP) * step_d)
            return max(rounded_up, min_q)
    
    return rounded_down

def analyze_format_issue():
    """分析格式化问题"""
    print("\n=== 格式化问题分析 ===")
    
    # 模拟可能导致问题的场景
    test_cases = [
        {"price": 0.4, "tick_size": 0.00001, "desc": "价格0.4, tick_size=0.00001"},
        {"price": 0.400000, "tick_size": 0.00001, "desc": "价格0.400000, tick_size=0.00001"},
        {"qty": 236.417, "step_size": 0.001, "desc": "数量236.417, step_size=0.001"},
        {"qty": 236.417, "step_size": 0.01, "desc": "数量236.417, step_size=0.01"},
    ]
    
    for case in test_cases:
        print(f"\n{case['desc']}:")
        
        if 'price' in case:
            price = case['price']
            tick_size = case['tick_size']
            
            # 当前格式化方法
            dec.getcontext().prec = 18
            price_dec = (Decimal(str(price)) / Decimal(str(tick_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(tick_size))
            price_str = format(price_dec.normalize(), 'f')
            
            print(f"  输入: {price}")
            print(f"  Decimal计算: {price_dec}")
            print(f"  格式化结果: '{price_str}'")
            print(f"  小数位数: {len(price_str.split('.')[-1]) if '.' in price_str else 0}")
            
        if 'qty' in case:
            qty = case['qty']
            step_size = case['step_size']
            
            # 当前格式化方法
            dec.getcontext().prec = 18
            qty_dec = (Decimal(str(qty)) / Decimal(str(step_size))).quantize(Decimal('1'), rounding=dec.ROUND_DOWN) * Decimal(str(step_size))
            qty_str = format(qty_dec.normalize(), 'f')
            
            print(f"  输入: {qty}")
            print(f"  Decimal计算: {qty_dec}")
            print(f"  格式化结果: '{qty_str}'")
            print(f"  小数位数: {len(qty_str.split('.')[-1]) if '.' in qty_str else 0}")

if __name__ == "__main__":
    test_orderusdt_precision()
    analyze_format_issue()
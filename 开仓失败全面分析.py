#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开仓失败全面分析脚本
基于日志文件 logs/strategy_enhanced_20251001_220201.log 进行深度分析
"""

import re
import json
from collections import defaultdict, Counter
from datetime import datetime

def analyze_trading_failures():
    """分析开仓失败的全面情况"""
    
    log_file = "logs\\strategy_enhanced_20251001_220201.log"
    
    # 数据收集容器
    failure_stats = {
        'precision_errors': [],  # -1111 精度错误
        'signature_errors': [],  # -1022 签名错误
        'nominal_value_insufficient': [],  # 名义价值不足
        'trading_attempts': [],  # 所有开仓尝试
        'debug_info': [],  # 调试信息
    }
    
    # 错误模式
    patterns = {
        'precision_error': r'{"code":-1111,"msg":"Precision is over the maximum defined for this asset."}',
        'signature_error': r'{"code":-1022,"msg":"Signature for this request is not valid."}',
        'nominal_insufficient': r'名义价值不足: ([\d.]+) < 5\.0',
        'trading_attempt': r'\[DEBUG\] (\w+) 开始增强格式化: price=([\d.]+), qty=([\d.]+)',
        'final_params': r'(\w+) 最终订单参数: price=\'([\d.]+)\', qty=\'([\d.]+)\'',
        'nominal_value': r'(\w+) 名义价值: ([\d.]+) USDT',
        'trading_rules': r'(\w+) 参数: tick_size=(\w+), step_size=(\w+), min_qty=(\w+)',
    }
    
    print("🔍 开始分析日志文件...")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"❌ 日志文件未找到: {log_file}")
        return
    
    current_attempt = {}
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        
        # 提取时间戳
        timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
        timestamp = timestamp_match.group(1) if timestamp_match else None
        
        # 检测开仓尝试开始
        attempt_match = re.search(patterns['trading_attempt'], line)
        if attempt_match:
            if current_attempt:  # 保存上一个尝试
                failure_stats['trading_attempts'].append(current_attempt)
            
            current_attempt = {
                'symbol': attempt_match.group(1),
                'timestamp': timestamp,
                'line_num': line_num,
                'initial_price': float(attempt_match.group(2)),
                'initial_qty': float(attempt_match.group(3)),
                'errors': [],
                'final_params': {},
                'nominal_value': None,
                'trading_rules': {},
                'has_precision_error': False,
                'has_signature_error': False,
                'has_nominal_insufficient': False,
            }
        
        # 收集当前尝试的信息
        if current_attempt:
            # 交易规则信息
            rules_match = re.search(patterns['trading_rules'], line)
            if rules_match and rules_match.group(1) == current_attempt['symbol']:
                current_attempt['trading_rules'] = {
                    'tick_size': rules_match.group(2),
                    'step_size': rules_match.group(3),
                    'min_qty': rules_match.group(4),
                }
            
            # 最终订单参数
            final_match = re.search(patterns['final_params'], line)
            if final_match and final_match.group(1) == current_attempt['symbol']:
                current_attempt['final_params'] = {
                    'price': final_match.group(2),
                    'qty': final_match.group(3),
                }
            
            # 名义价值
            nominal_match = re.search(patterns['nominal_value'], line)
            if nominal_match and nominal_match.group(1) == current_attempt['symbol']:
                current_attempt['nominal_value'] = float(nominal_match.group(2))
            
            # 名义价值不足
            insufficient_match = re.search(patterns['nominal_insufficient'], line)
            if insufficient_match:
                current_attempt['has_nominal_insufficient'] = True
                current_attempt['insufficient_value'] = float(insufficient_match.group(1))
            
            # 精度错误
            if re.search(patterns['precision_error'], line):
                current_attempt['has_precision_error'] = True
                current_attempt['errors'].append('precision_error')
            
            # 签名错误
            if re.search(patterns['signature_error'], line):
                current_attempt['has_signature_error'] = True
                current_attempt['errors'].append('signature_error')
    
    # 保存最后一个尝试
    if current_attempt:
        failure_stats['trading_attempts'].append(current_attempt)
    
    # 统计分析
    print(f"\n📊 **开仓失败统计分析报告**")
    print(f"=" * 60)
    
    total_attempts = len(failure_stats['trading_attempts'])
    print(f"📈 **总开仓尝试次数**: {total_attempts}")
    
    if total_attempts == 0:
        print("❌ 未发现开仓尝试记录")
        return
    
    # 1. 失败原因统计
    print(f"\n🔍 **1. 失败原因统计分析**")
    print(f"-" * 40)
    
    precision_errors = sum(1 for attempt in failure_stats['trading_attempts'] if attempt['has_precision_error'])
    signature_errors = sum(1 for attempt in failure_stats['trading_attempts'] if attempt['has_signature_error'])
    nominal_insufficient = sum(1 for attempt in failure_stats['trading_attempts'] if attempt['has_nominal_insufficient'])
    
    print(f"🚨 **精度错误 (-1111)**: {precision_errors} 次 ({precision_errors/total_attempts*100:.1f}%)")
    print(f"🔐 **签名错误 (-1022)**: {signature_errors} 次 ({signature_errors/total_attempts*100:.1f}%)")
    print(f"💰 **名义价值不足**: {nominal_insufficient} 次 ({nominal_insufficient/total_attempts*100:.1f}%)")
    
    # 2. 交易对分析
    print(f"\n📋 **2. 交易对失败分析**")
    print(f"-" * 40)
    
    symbol_stats = defaultdict(lambda: {
        'total': 0, 'precision': 0, 'signature': 0, 'nominal': 0
    })
    
    for attempt in failure_stats['trading_attempts']:
        symbol = attempt['symbol']
        symbol_stats[symbol]['total'] += 1
        if attempt['has_precision_error']:
            symbol_stats[symbol]['precision'] += 1
        if attempt['has_signature_error']:
            symbol_stats[symbol]['signature'] += 1
        if attempt['has_nominal_insufficient']:
            symbol_stats[symbol]['nominal'] += 1
    
    for symbol, stats in symbol_stats.items():
        print(f"🎯 **{symbol}**: 总计{stats['total']}次 | "
              f"精度{stats['precision']}次 | 签名{stats['signature']}次 | 名义{stats['nominal']}次")
    
    # 3. 精度修复效果分析
    print(f"\n🔧 **3. 精度修复效果分析**")
    print(f"-" * 40)
    
    # 检查交易规则获取情况
    rules_missing = sum(1 for attempt in failure_stats['trading_attempts'] 
                       if attempt['trading_rules'].get('step_size') == 'None')
    
    print(f"⚠️  **交易规则缺失**: {rules_missing}/{total_attempts} 次 ({rules_missing/total_attempts*100:.1f}%)")
    print(f"📝 **说明**: step_size=None 表示无法获取交易规则，这是精度错误的根本原因")
    
    # 4. 名义价值分析
    print(f"\n💰 **4. 名义价值分析**")
    print(f"-" * 40)
    
    nominal_values = [attempt['nominal_value'] for attempt in failure_stats['trading_attempts'] 
                     if attempt['nominal_value'] is not None]
    
    if nominal_values:
        print(f"📊 **名义价值范围**: {min(nominal_values):.2f} - {max(nominal_values):.2f} USDT")
        print(f"📊 **平均名义价值**: {sum(nominal_values)/len(nominal_values):.2f} USDT")
        
        # 名义价值分布
        ranges = [(0, 1), (1, 5), (5, 25), (25, 100), (100, float('inf'))]
        for min_val, max_val in ranges:
            count = sum(1 for v in nominal_values if min_val <= v < max_val)
            range_str = f"{min_val}-{max_val if max_val != float('inf') else '∞'}"
            print(f"   📈 {range_str} USDT: {count} 次 ({count/len(nominal_values)*100:.1f}%)")
    
    # 5. 时间分析
    print(f"\n⏰ **5. 时间分布分析**")
    print(f"-" * 40)
    
    time_hours = defaultdict(int)
    for attempt in failure_stats['trading_attempts']:
        if attempt['timestamp']:
            hour = datetime.strptime(attempt['timestamp'], '%Y-%m-%d %H:%M:%S').hour
            time_hours[hour] += 1
    
    for hour in sorted(time_hours.keys()):
        count = time_hours[hour]
        print(f"🕐 **{hour:02d}:00-{hour:02d}:59**: {count} 次")
    
    # 6. 具体问题诊断
    print(f"\n🔍 **6. 根本原因诊断**")
    print(f"-" * 40)
    
    print(f"❌ **主要问题**: 精度修复未生效")
    print(f"   - 所有尝试的 step_size 都是 None")
    print(f"   - 无法获取交易规则导致精度控制失效")
    print(f"   - 增强格式化方法被调用但无法正常工作")
    
    print(f"\n🔧 **次要问题**: 签名错误")
    print(f"   - 在精度错误后的重试中出现")
    print(f"   - 可能是时间戳或参数变化导致")
    
    print(f"\n💡 **名义价值问题**: 降档机制导致")
    print(f"   - 100% → 75% → 50% → 25% 降档")
    print(f"   - 最终降到 < 5 USDT 无法开仓")
    
    # 7. 解决方案建议
    print(f"\n🚀 **7. 智能解决方案建议**")
    print(f"-" * 40)
    
    print(f"🎯 **立即修复 (优先级1)**:")
    print(f"   1. 修复交易规则获取逻辑")
    print(f"   2. 确保 step_size 正确获取")
    print(f"   3. 验证精度修复生效")
    
    print(f"\n🔧 **智能优化 (优先级2)**:")
    print(f"   1. 名义价值不足时智能调整到 6 USDT")
    print(f"   2. 检查账户余额充足性")
    print(f"   3. 优化重试机制避免签名错误")
    
    print(f"\n📊 **监控改进 (优先级3)**:")
    print(f"   1. 增加交易规则获取成功率监控")
    print(f"   2. 添加精度修复效果验证")
    print(f"   3. 实时错误统计和告警")
    
    # 8. 详细失败案例
    print(f"\n📝 **8. 典型失败案例分析**")
    print(f"-" * 40)
    
    # 选择几个典型案例
    for i, attempt in enumerate(failure_stats['trading_attempts'][:3]):
        print(f"\n🔍 **案例 {i+1}**: {attempt['symbol']} @ {attempt['timestamp']}")
        print(f"   📊 初始参数: price={attempt['initial_price']}, qty={attempt['initial_qty']}")
        print(f"   📋 交易规则: {attempt['trading_rules']}")
        print(f"   📄 最终参数: {attempt['final_params']}")
        print(f"   💰 名义价值: {attempt['nominal_value']} USDT")
        print(f"   ❌ 错误类型: {', '.join(attempt['errors'])}")
    
    print(f"\n" + "=" * 60)
    print(f"📋 **分析完成** - 发现 {total_attempts} 次开仓尝试，全部失败")
    print(f"🎯 **核心问题**: 交易规则获取失败导致精度修复无效")
    print(f"💡 **解决方向**: 修复交易规则获取 + 智能名义价值调整")

if __name__ == "__main__":
    analyze_trading_failures()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试并修复API签名错误的脚本
主要解决 -1022 "Signature for this request is not valid" 错误
"""

import sys
import os
import json
import time
import hmac
import hashlib
import urllib.parse
import yaml
from decimal import Decimal, getcontext

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient

def load_config():
    """加载配置"""
    try:
        # 加载YAML配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
        
        # 加载JSON配置（可选）
        json_config = {}
        try:
            with open('config/config.json', 'r', encoding='utf-8') as f:
                json_config = json.load(f)
        except Exception:
            pass
        
        # 合并配置
        config = {**yaml_config, **json_config}
        
        print(f"✓ 配置加载成功")
        print(f"  - API Key: {config['api_key'][:10]}...")
        print(f"  - Base URL: {config['base_url']}")
        return config
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return None

def test_signature_generation():
    """测试签名生成逻辑"""
    print("\n=== 测试签名生成逻辑 ===")
    
    config = load_config()
    if not config:
        return False
    
    # 创建HttpClient实例
    client = HttpClient(config['api_key'], config['api_secret'], config['base_url'])
    
    # 测试参数（模拟PUMPBTCUSDT下单参数）
    test_params = {
        'symbol': 'PUMPBTCUSDT',
        'side': 'BUY',
        'type': 'MARKET',
        'quantity': '1268.352',
        'timestamp': int(time.time() * 1000),
        'recvWindow': 60000
    }
    
    print(f"测试参数: {test_params}")
    
    # 生成签名
    try:
        signature = client._sign_request(test_params)
        print(f"✓ 签名生成成功: {signature[:20]}...")
        
        # 验证签名格式
        if len(signature) == 64 and all(c in '0123456789abcdef' for c in signature):
            print("✓ 签名格式正确 (64位十六进制)")
        else:
            print("✗ 签名格式异常")
            
        return True
    except Exception as e:
        print(f"✗ 签名生成失败: {e}")
        return False

def test_parameter_encoding():
    """测试参数编码问题"""
    print("\n=== 测试参数编码问题 ===")
    
    # 测试不同的参数值
    test_cases = [
        {'quantity': '1268.352'},
        {'quantity': '1268.35'},
        {'quantity': '1268'},
        {'price': '0.100000'},
        {'price': '0.1'},
        {'symbol': 'PUMPBTCUSDT'},
    ]
    
    for case in test_cases:
        query_string = urllib.parse.urlencode(case)
        print(f"参数: {case} -> 编码: {query_string}")
        
        # 检查是否有特殊字符
        if any(c in query_string for c in ['%', '+', '&']):
            print(f"  ⚠️  包含特殊字符，可能影响签名")
        else:
            print(f"  ✓ 编码正常")

def test_timestamp_issue():
    """测试时间戳相关问题"""
    print("\n=== 测试时间戳问题 ===")
    
    config = load_config()
    if not config:
        return False
    
    client = HttpClient(config['api_key'], config['api_secret'], config['base_url'])
    
    # 测试服务器时间
    try:
        response = client.get('/fapi/v1/time')
        if response:
            server_time = response.get('serverTime', 0)
            local_time = int(time.time() * 1000)
            time_diff = abs(server_time - local_time)
            
            print(f"服务器时间: {server_time}")
            print(f"本地时间: {local_time}")
            print(f"时间差: {time_diff}ms")
            
            if time_diff > 5000:  # 5秒
                print("⚠️  时间差过大，可能导致签名失败")
                return False
            else:
                print("✓ 时间同步正常")
                return True
        else:
            print("✗ 无法获取服务器时间")
            return False
    except Exception as e:
        print(f"✗ 时间同步测试失败: {e}")
        return False

def test_pumpbtcusdt_order_params():
    """测试PUMPBTCUSDT的具体下单参数"""
    print("\n=== 测试PUMPBTCUSDT下单参数 ===")
    
    config = load_config()
    if not config:
        return False
    
    client = HttpClient(config['api_key'], config['api_secret'], config['base_url'])
    
    # 获取交易规则
    try:
        exchange_info = client.get('/fapi/v1/exchangeInfo')
        if not exchange_info:
            print("✗ 无法获取交易规则")
            return False
        
        # 查找PUMPBTCUSDT的规则
        symbol_info = None
        for symbol in exchange_info.get('symbols', []):
            if symbol['symbol'] == 'PUMPBTCUSDT':
                symbol_info = symbol
                break
        
        if not symbol_info:
            print("✗ 未找到PUMPBTCUSDT交易规则")
            return False
        
        print(f"✓ 找到PUMPBTCUSDT交易规则")
        
        # 解析过滤器
        filters = {f['filterType']: f for f in symbol_info['filters']}
        
        lot_size = filters.get('LOT_SIZE', {})
        price_filter = filters.get('PRICE_FILTER', {})
        
        step_size = float(lot_size.get('stepSize', 0))
        tick_size = float(price_filter.get('tickSize', 0))
        min_qty = float(lot_size.get('minQty', 0))
        
        print(f"  - stepSize: {step_size}")
        print(f"  - tickSize: {tick_size}")
        print(f"  - minQty: {min_qty}")
        
        # 测试格式化函数
        from maker_channel_enhanced import MakerChannelEnhanced
        strategy = MakerChannelEnhanced()
        
        # 测试原始参数
        original_qty = 1268.352
        original_price = 0.100000
        
        formatted_price, formatted_qty = strategy._format_order_params(
            original_price, original_qty, tick_size, step_size, min_qty
        )
        
        print(f"原始数量: {original_qty} -> 格式化: {formatted_qty}")
        print(f"原始价格: {original_price} -> 格式化: {formatted_price}")
        
        # 验证格式化结果
        try:
            float(formatted_qty)
            float(formatted_price)
            print("✓ 格式化参数可以转换为浮点数")
        except ValueError as e:
            print(f"✗ 格式化参数无法转换为浮点数: {e}")
            return False
        
        # 测试下单参数
        order_params = {
            'symbol': 'PUMPBTCUSDT',
            'side': 'BUY',
            'type': 'MARKET',
            'quantity': formatted_qty,
            'timestamp': client._get_corrected_timestamp(),
            'recvWindow': 60000
        }
        
        print(f"下单参数: {order_params}")
        
        # 生成签名
        signature = client._sign_request(order_params)
        print(f"✓ 签名生成: {signature[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_real_order_simulation():
    """模拟真实下单请求（不实际下单）"""
    print("\n=== 模拟真实下单请求 ===")
    
    config = load_config()
    if not config:
        return False
    
    client = HttpClient(config['api_key'], config['api_secret'], config['base_url'])
    
    try:
        # 获取账户信息，验证API连接
        account_info = client.get('/fapi/v2/account')
        if account_info:
            print("✓ API连接正常，账户信息获取成功")
        else:
            print("✗ API连接失败")
            return False
        
        # 获取PUMPBTCUSDT当前价格
        ticker = client.get('/fapi/v1/ticker/24hr', {'symbol': 'PUMPBTCUSDT'})
        if not ticker:
            print("✗ 无法获取PUMPBTCUSDT价格")
            return False
        
        current_price = float(ticker['lastPrice'])
        print(f"✓ PUMPBTCUSDT当前价格: {current_price}")
        
        # 获取交易规则
        exchange_info = client.get('/fapi/v1/exchangeInfo')
        symbol_info = None
        for symbol in exchange_info.get('symbols', []):
            if symbol['symbol'] == 'PUMPBTCUSDT':
                symbol_info = symbol
                break
        
        if not symbol_info:
            print("✗ 未找到交易规则")
            return False
        
        # 解析过滤器
        filters = {f['filterType']: f for f in symbol_info['filters']}
        lot_size = filters.get('LOT_SIZE', {})
        
        step_size = float(lot_size.get('stepSize', 0))
        min_qty = float(lot_size.get('minQty', 0))
        
        # 计算合理的下单数量（使用最小数量的10倍）
        test_qty = max(min_qty * 10, 100)  # 至少100个单位
        
        # 格式化参数
        from maker_channel_enhanced import MakerChannelEnhanced
        strategy = MakerChannelEnhanced()
        
        _, formatted_qty = strategy._format_order_params(
            current_price, test_qty, 0, step_size, min_qty
        )
        
        print(f"测试数量: {test_qty} -> 格式化: {formatted_qty}")
        
        # 构建下单参数
        order_params = {
            'symbol': 'PUMPBTCUSDT',
            'side': 'BUY',
            'type': 'MARKET',
            'quantity': formatted_qty,
            'timestamp': client._get_corrected_timestamp(),
            'recvWindow': 60000
        }
        
        print(f"模拟下单参数: {order_params}")
        
        # 生成签名
        signature = client._sign_request(order_params)
        order_params['signature'] = signature
        
        print(f"✓ 签名生成完成")
        print(f"完整参数: {order_params}")
        
        # 注意：这里不实际发送下单请求，只是验证参数格式
        print("✓ 参数格式验证通过（未实际下单）")
        
        return True
        
    except Exception as e:
        print(f"✗ 模拟测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== PUMPBTCUSDT签名错误调试脚本 ===")
    
    # 执行各项测试
    tests = [
        ("签名生成逻辑", test_signature_generation),
        ("参数编码问题", test_parameter_encoding),
        ("时间戳问题", test_timestamp_issue),
        ("PUMPBTCUSDT参数", test_pumpbtcusdt_order_params),
        ("真实下单模拟", test_real_order_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} - 通过")
            else:
                print(f"✗ {test_name} - 失败")
        except Exception as e:
            print(f"✗ {test_name} - 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！签名问题应已解决")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
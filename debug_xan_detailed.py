#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'strategy'))

# 修复相对导入问题
import importlib.util
spec = importlib.util.spec_from_file_location("maker_channel_enhanced", "e:/allmac/strategy/maker_channel_enhanced.py")
maker_module = importlib.util.module_from_spec(spec)
sys.modules["maker_channel_enhanced"] = maker_module

# 手动设置依赖模块
from strategy.rate_limiter import ApiRateLimiter
from strategy.binance_http import BinanceHttp
from strategy.cache_manager import CacheManager
from strategy.symbol_loader import SymbolLoader

# 现在导入主类
spec.loader.exec_module(maker_module)
MakerChannelEnhanced = maker_module.MakerChannelEnhanced

import json

def main():
    # 初始化策略
    strategy = MakerChannelEnhanced()
    strategy.load_symbols_with_cache()

    # 检查XANUSDT基本信息
    xan_info = strategy.symbols_info.get('XANUSDT', {})
    print(f'XANUSDT基本信息: {json.dumps(xan_info, indent=2)}')

    # 检查是否在新币突破池中
    print(f'\n检查新币突破池:')
    newcoin_pool = strategy.get_newcoin_breakthrough()
    print(f'新币突破池数量: {len(newcoin_pool)}')
    print(f'XANUSDT在新币突破池中: {"XANUSDT" in newcoin_pool}')
    if newcoin_pool:
        print(f'新币突破池前10: {newcoin_pool[:10]}')

    # 检查是否在龙头池中
    print(f'\n检查龙头池:')
    leader_pool = strategy.get_top50_market_leaders()
    print(f'龙头池数量: {len(leader_pool)}')
    print(f'XANUSDT在龙头池中: {"XANUSDT" in leader_pool}')
    if leader_pool:
        print(f'龙头池前10: {leader_pool[:10]}')

    # 检查筛选条件
    age_days = xan_info.get('age_days', 999)
    print(f'\n筛选条件检查:')
    print(f'币龄: {age_days}天 (限制: {strategy.new_coin_max_age_days}天)')
    print(f'币龄符合: {age_days <= strategy.new_coin_max_age_days}')

    # 检查深度
    try:
        depth = strategy.get_depth01pct('XANUSDT')
        print(f'深度: {depth} USDT (最低要求: {strategy.new_coin_depth_min} USDT)')
        print(f'深度符合: {depth >= strategy.new_coin_depth_min}')
    except Exception as e:
        print(f'深度检查失败: {e}')

    # 详细检查新币突破筛选条件
    print(f'\n详细检查XANUSDT新币突破筛选:')
    try:
        df = strategy.cache_get(f"kl15_XANUSDT_50", strategy._raw_klines, 'XANUSDT', '15m', 50)
        if df is None or len(df) < 30:
            print(f'✗ K线数据不足: {0 if df is None else len(df)} < 30')
        else:
            print(f'✓ K线数据充足: {len(df)}根')
            
            # 计算通道上下轨
            upper = df['h'].rolling(20).max().iloc[-1]
            lower = df['l'].rolling(20).min().iloc[-1]
            close = df['c'].iloc[-1]
            high = df['h'].iloc[-1]
            
            print(f'通道上轨: {upper:.6f}')
            print(f'通道下轨: {lower:.6f}')
            print(f'当前收盘: {close:.6f}')
            print(f'当前最高: {high:.6f}')
            
            # 检测突破条件
            breakthrough_close = close >= upper * 0.995
            breakthrough_high = high >= upper
            momentum_check = close / df['c'].iloc[-25] > 1.02
            
            print(f'收盘突破(99.5%): {breakthrough_close} ({close:.6f} >= {upper * 0.995:.6f})')
            print(f'最高突破: {breakthrough_high} ({high:.6f} >= {upper:.6f})')
            print(f'动量检查(2%): {momentum_check} ({close / df["c"].iloc[-25]:.4f} > 1.02)')
            
            # 通道位置
            channel_position = (close - lower) / (upper - lower + 1e-8)
            in_upper_channel = channel_position >= 0.5
            print(f'通道位置: {channel_position:.2%} (需要>=50%: {in_upper_channel})')
            
            # 成交量验证
            recent_vol = df['v'].iloc[-5:].mean()
            base_vol = df['v'].iloc[-25:-5].mean()
            volume_surge = recent_vol > base_vol * 1.1
            print(f'成交量放大: {volume_surge} ({recent_vol:.0f} > {base_vol * 1.1:.0f})')
            
            # 最终判断
            condition1 = (breakthrough_close or breakthrough_high or in_upper_channel) and momentum_check
            condition2 = volume_surge or (breakthrough_close and breakthrough_high)
            final_pass = condition1 and condition2
            
            print(f'条件1(突破+动量): {condition1}')
            print(f'条件2(成交量): {condition2}')
            print(f'最终通过: {final_pass}')
            
    except Exception as e:
        print(f'详细检查失败: {e}')

    # 检查策略模式
    print(f'\n策略模式检查:')
    print(f'当前策略模式: {strategy.strategy_mode}')
    print(f'新币爆发模式: {strategy.strategy_mode == "new_coin_burst"}')

if __name__ == '__main__':
    main()
import time
import logging
from typing import Optional, Callable, Dict, Any, List, Tuple
from decimal import Decimal
import random
import requests
from ..portfolio.position_manager import PositionState


class OrderManager:
    """订单管理器 - 解决下单混乱和幂等性问题"""
    
    def __init__(self, exchange, position_manager):
        self.exchange = exchange
        self.position_manager = position_manager
        self.logger = logging.getLogger(__name__)
    
    def execute_with_retry(self, func: Callable[[], bool], max_retries: int = 3, 
                          delay: float = 1.0) -> bool:
        """带重试机制的订单执行 - 优化网络错误处理"""
        for attempt in range(max_retries):
            delay_multiplier = 1.0  # 默认延迟倍数
            try:
                result = func()
                if result:
                    return True
                self.logger.warning(f"第{attempt + 1}次尝试失败")
            except requests.exceptions.RequestException as e:
                # 网络相关错误，增加重试延迟
                self.logger.warning(f"第{attempt + 1}次尝试网络异常: {e}")
                delay_multiplier = 2.0  # 网络错误时增加延迟
            except Exception as e:
                self.logger.warning(f"第{attempt + 1}次尝试异常: {e}")
                delay_multiplier = 1.0
            
            if attempt < max_retries - 1:
                jitter = random.uniform(0.5, 1.5)
                time.sleep(delay * delay_multiplier * jitter)
        
        return False

    def ensure_clean_state_before_open(self, symbol: str) -> bool:
        """开仓前确保交易对状态干净 - 实现原则2的开仓前检查"""
        try:
            self.logger.info(f"开仓前检查: {symbol}")
            
            # 1. 查询该symbol的所有挂单
            open_orders = self.exchange.get_open_orders(symbol)
            
            # 2. 如果存在任何遗留挂单 → 立即撤销
            if open_orders:
                self.logger.info(f"发现遗留挂单: {symbol} 数量: {len(open_orders)}，开始清理")
                
                # 撤销所有挂单
                success = self.cancel_all_orders(symbol)
                
                if not success:
                    self.logger.error(f"清理遗留挂单失败: {symbol}")
                    return False
                
                self.logger.info(f"遗留挂单清理完成: {symbol}")
            
            # 3. 检查仓位状态，确保没有有效仓位
            position = self.position_manager.get_position(symbol)
            if position and position.state == PositionState.OPEN and position.size > 0:
                self.logger.warning(f"开仓前检查失败: {symbol} 已有有效仓位")
                return False
                
            # 4. 确认撤销成功后，状态干净，允许开仓
            self.logger.info(f"开仓前检查通过: {symbol} 状态干净")
            return True
            
        except Exception as e:
            self.logger.error(f"开仓前检查异常: {symbol} - {e}")
            return False
            
            if attempt < max_retries - 1:
                jitter = random.uniform(0.5, 1.5)
                time.sleep(delay * delay_multiplier * jitter)
        
        return False
    
    def cancel_order_with_retry(self, symbol: str, order_id: str, 
                               max_retries: int = 3) -> bool:
        """带重试机制的订单取消"""
        def _cancel():
            result = self.exchange.cancel_order(symbol, order_id)
            # 如果订单不存在，也认为是成功的
            if result is None:
                # 检查订单是否真的不存在
                try:
                    open_orders = self.exchange.get_open_orders(symbol)
                    order_exists = any(order.get('clientOrderId') == order_id or str(order.get('orderId')) == str(order_id) 
                                      for order in open_orders)
                    if not order_exists:
                        self.logger.info(f"订单不存在，无需取消: {symbol} {order_id}")
                        return True
                except Exception as e:
                    self.logger.warning(f"检查订单是否存在时出错: {e}")
                    # 网络错误时，我们假设订单可能已被取消
                    if "timed out" in str(e) or "EOF occurred" in str(e):
                        self.logger.info(f"网络错误，假设订单 {symbol} {order_id} 可能已被取消")
                        return True
            return result is not None
        
        return self.execute_with_retry(_cancel, max_retries)
    
    def place_market_order(self, symbol: str, side: str, quantity: Decimal,
                          reduce_only: bool = False) -> Optional[Dict[str, Any]]:
        """下市价单 - 增加订单验证"""
        try:
            response = self.exchange.place_order(
                symbol=symbol,
                side=side,
                order_type='MARKET',
                quantity=quantity,
                reduce_only=reduce_only
            )
            
            # 验证订单是否真正创建成功
            if response and (response.get('orderId') or response.get('clientOrderId')):
                order_id = response.get('orderId') or response.get('clientOrderId')
                self.logger.info(f"市价单创建成功: {symbol} {side} 订单ID: {order_id}")
                return response
            else:
                self.logger.error(f"市价单创建失败: {symbol} {side} - 交易所返回无效响应: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"下市价单失败: {e}")
            return None
    
    def place_stop_loss_order(self, symbol: str, stop_price: Decimal, 
                             quantity: Decimal) -> Optional[Dict[str, Any]]:
        """下止损单 - 增强订单验证"""
        try:
            response = self.exchange.place_order(
                symbol=symbol,
                side='SELL',  # 默认为多单止损
                order_type='STOP_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            # 验证订单是否真正创建成功
            if response and (response.get('orderId') or response.get('clientOrderId')):
                order_id = response.get('orderId') or response.get('clientOrderId')
                
                # 进一步验证订单是否真正存在于交易所
                if self._verify_order_exists(symbol, order_id):
                    self.logger.info(f"止损单创建成功并验证通过: {symbol} 订单ID: {order_id}")
                    return response
                else:
                    self.logger.error(f"止损单创建失败: {symbol} - 订单ID {order_id} 在交易所不存在")
                    return None
            else:
                self.logger.error(f"止损单创建失败: {symbol} - 交易所返回无效响应: {response}")
                return None
                
        except Exception as e:
            # 检查是否是因为达到最大止损订单限制
            if "Reach max stop order limit" in str(e):
                self.logger.warning(f"达到最大止损订单限制，无法下止损单: {symbol}")
            else:
                self.logger.error(f"下止损单失败: {e}")
            return None
    
    def place_take_profit_order(self, symbol: str, stop_price: Decimal,
                               quantity: Decimal) -> Optional[Dict[str, Any]]:
        """下止盈单 - 增加订单验证"""
        try:
            response = self.exchange.place_order(
                symbol=symbol,
                side='SELL',  # 默认为多单止盈
                order_type='TAKE_PROFIT_MARKET',
                quantity=quantity,
                stop_price=stop_price,
                reduce_only=True
            )
            
            # 验证订单是否真正创建成功
            if response and (response.get('orderId') or response.get('clientOrderId')):
                order_id = response.get('orderId') or response.get('clientOrderId')
                self.logger.info(f"止盈单创建成功: {symbol} 订单ID: {order_id}")
                return response
            else:
                self.logger.error(f"止盈单创建失败: {symbol} - 交易所返回无效响应: {response}")
                return None
                
        except Exception as e:
            # 检查是否是因为达到最大止损订单限制
            if "Reach max stop order limit" in str(e):
                self.logger.warning(f"达到最大止盈订单限制，无法下止盈单: {symbol}")
            else:
                self.logger.error(f"下止盈单失败: {e}")
            return None
    
    def sync_order_status(self, symbol: str) -> None:
        """同步订单状态 - 修复订单ID类型不匹配问题"""
        try:
            open_orders = self.exchange.get_open_orders(symbol)
            position = self.position_manager.get_position(symbol)
            
            if not position:
                return
            
            # 统一转换为字符串进行比较，避免类型不匹配
            exchange_order_ids = set()
            for order in open_orders:
                order_id = order.get('clientOrderId') or order.get('orderId')
                if order_id:
                    exchange_order_ids.add(str(order_id))
            
            # 检查本地记录的订单是否在交易所存在
            if position.stop_loss_order_id:
                sl_order_id_str = str(position.stop_loss_order_id)
                if sl_order_id_str not in exchange_order_ids:
                    self.logger.warning(f"止损订单不存在: {sl_order_id_str}")
                    position.stop_loss_order_id = None
                else:
                    self.logger.debug(f"止损订单存在: {sl_order_id_str}")
            
            if position.take_profit_order_id:
                tp_order_id_str = str(position.take_profit_order_id)
                if tp_order_id_str not in exchange_order_ids:
                    self.logger.warning(f"止盈订单不存在: {tp_order_id_str}")
                    position.take_profit_order_id = None
                else:
                    self.logger.debug(f"止盈订单存在: {tp_order_id_str}")
                
        except Exception as e:
            # 网络超时等错误，不重置订单ID，等待下次同步
            if "timed out" in str(e) or "timeout" in str(e) or "network" in str(e).lower():
                self.logger.warning(f"同步订单状态网络超时，跳过本次同步: {e}")
            else:
                self.logger.error(f"同步订单状态失败: {e}")
    
    def cleanup_orphan_orders(self, symbol: str) -> None:
        """清理孤儿订单"""
        try:
            open_orders = self.exchange.get_open_orders(symbol)
            position = self.position_manager.get_position(symbol)
            
            # 如果没有仓位但有挂单，清理所有订单
            if not position or position.state == PositionState.NONE:
                for order in open_orders:
                    order_id = order.get('clientOrderId') or order.get('orderId')
                    if order_id:
                        if not self.cancel_order_with_retry(symbol, str(order_id)):
                            self.logger.warning(f"取消订单失败: {symbol} {order_id}")
                        else:
                            self.logger.info(f"成功取消孤儿订单: {symbol} {order_id}")
        
        except Exception as e:
            self.logger.error(f"清理孤儿订单失败: {e}")
    
    def cancel_all_orders(self, symbol: str) -> bool:
        """撤销所有挂单"""
        try:
            open_orders = self.exchange.get_open_orders(symbol)
            success = True
            cancelled_count = 0
            total_orders = len(open_orders)
            
            for order in open_orders:
                # 优先使用clientOrderId，如果不存在则使用orderId
                order_id = order.get('clientOrderId') or order.get('orderId')
                if order_id:
                    if not self.cancel_order_with_retry(symbol, str(order_id)):
                        # 记录失败但继续处理其他订单
                        self.logger.warning(f"取消订单失败，但继续处理其他订单: {symbol} {order_id}")
                        success = False
                    else:
                        self.logger.info(f"成功取消订单: {symbol} {order_id}")
                        cancelled_count += 1
            
            self.logger.info(f"撤销订单完成: {symbol} 总订单数={total_orders} 成功取消={cancelled_count} 是否完全成功={success}")
            return True  # 总是返回True，因为我们已经尽力取消了所有订单
        except Exception as e:
            self.logger.error(f"撤销所有订单失败: {e}")
            return False
    
    def update_stop_orders(self, symbol: str, stop_loss_price: Optional[Decimal], 
                          take_profit_price: Optional[Decimal], quantity: Decimal) -> bool:
        """更新止损止盈单 - 撤销旧单并创建新单"""
        try:
            position = self.position_manager.get_position(symbol)
            if not position:
                return False
            
            # 撤销现有的止损止盈单
            success = True
            if position.stop_loss_order_id:
                # 确保订单ID是字符串类型
                order_id_str = str(position.stop_loss_order_id)
                if not self.cancel_order_with_retry(symbol, order_id_str):
                    self.logger.warning(f"撤销旧止损单失败: {symbol} {order_id_str}")
                    # 即使撤销失败，也继续执行，因为可能订单已经不存在
                position.stop_loss_order_id = None
            
            if position.take_profit_order_id:
                # 确保订单ID是字符串类型
                order_id_str = str(position.take_profit_order_id)
                if not self.cancel_order_with_retry(symbol, order_id_str):
                    self.logger.warning(f"撤销旧止盈单失败: {symbol} {order_id_str}")
                    # 即使撤销失败，也继续执行，因为可能订单已经不存在
                position.take_profit_order_id = None
            
            # 创建新的止损单
            if stop_loss_price is not None and quantity > 0:
                sl_order = self.place_stop_loss_order(symbol, stop_loss_price, quantity)
                if sl_order:
                    position.stop_loss_order_id = sl_order.get('clientOrderId') or sl_order.get('orderId')
                    position.stop_loss_price = stop_loss_price
                else:
                    self.logger.error(f"创建新止损单失败: {symbol}")
                    success = False
            
            # 创建新的止盈单
            if take_profit_price is not None and quantity > 0:
                tp_order = self.place_take_profit_order(symbol, take_profit_price, quantity)
                if tp_order:
                    position.take_profit_order_id = tp_order.get('clientOrderId') or tp_order.get('orderId')
                    position.take_profit_price = take_profit_price
                else:
                    self.logger.error(f"创建新止盈单失败: {symbol}")
                    success = False
            
            return success
        except Exception as e:
            self.logger.error(f"更新止损止盈单失败: {e}")
            return False
    
    def update_trailing_stop(self, symbol: str, new_stop_price: Decimal) -> bool:
        """更新移动止损 - 只允许向有利方向移动"""
        try:
            position = self.position_manager.get_position(symbol)
            if not position or position.state != PositionState.OPEN:
                return False
            
            # 检查是否需要更新移动止损
            should_update = False
            
            # 通过仓位大小判断方向：正数为多单，负数为空单
            is_long_position = position.size > 0
            
            if position.stop_loss_price is not None:
                current_stop_price = float(position.stop_loss_price)
                new_stop_price_float = float(new_stop_price)
                
                if is_long_position:
                    # 多单：新的止损价应该高于当前止损价（保护更多利润）
                    if new_stop_price_float > current_stop_price:
                        should_update = True
                        self.logger.info(f"多单移动止损条件满足：新止损价 {new_stop_price} > 当前止损价 {position.stop_loss_price}")
                    else:
                        self.logger.info(f"多单移动止损条件不满足：新止损价 {new_stop_price} <= 当前止损价 {position.stop_loss_price}")
                else:
                    # 空单：新的止损价应该低于当前止损价（保护更多利润）
                    if new_stop_price_float < current_stop_price:
                        should_update = True
                        self.logger.info(f"空单移动止损条件满足：新止损价 {new_stop_price} < 当前止损价 {position.stop_loss_price}")
                    else:
                        self.logger.info(f"空单移动止损条件不满足：新止损价 {new_stop_price} >= 当前止损价 {position.stop_loss_price}")
            else:
                # 如果当前没有止损价，直接设置
                should_update = True
                self.logger.info(f"当前没有止损价，直接设置移动止损")
                        
            
            if should_update:
                # 保存旧的止损单信息，以便在创建新订单失败时可以恢复
                old_stop_loss_order_id = position.stop_loss_order_id
                old_stop_loss_price = position.stop_loss_price
                
                # 撤销旧的止损单
                if position.stop_loss_order_id:
                    self.logger.info(f"撤销旧移动止损单: {symbol} {position.stop_loss_order_id}")
                    if self.cancel_order_with_retry(symbol, str(position.stop_loss_order_id)):
                        position.stop_loss_order_id = None
                        self.logger.info(f"成功撤销旧移动止损单")
                    else:
                        self.logger.error(f"撤销旧移动止损单失败: {symbol}")
                        return False
                
                # 创建新的移动止损单
                if new_stop_price is not None and position.size > 0:
                    self.logger.info(f"创建新移动止损单: {symbol} 价格={new_stop_price} 数量={position.size}")
                    sl_order = self.place_stop_loss_order(symbol, new_stop_price, position.size)
                    if sl_order:
                        position.stop_loss_order_id = sl_order.get('clientOrderId') or sl_order.get('orderId')
                        position.stop_loss_price = new_stop_price
                        self.logger.info(f"移动止损更新成功: {symbol} 新止损价={new_stop_price} 订单ID={position.stop_loss_order_id}")
                        return True
                    else:
                        self.logger.error(f"创建新移动止损单失败: {symbol}")
                        # 回滚：尝试恢复旧的止损单
                        if old_stop_loss_order_id and old_stop_loss_price:
                            self.logger.info(f"尝试恢复旧的止损单: {symbol}")
                            old_order = self.place_stop_loss_order(symbol, old_stop_loss_price, position.size)
                            if old_order:
                                position.stop_loss_order_id = old_order.get('clientOrderId') or old_order.get('orderId')
                                position.stop_loss_price = old_stop_loss_price
                                self.logger.info(f"成功恢复旧的止损单: {symbol}")
                                return True
                            else:
                                self.logger.error(f"恢复旧的止损单失败: {symbol}")
                        return False
                else:
                    self.logger.error(f"无法创建移动止损单: 价格或数量无效")
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"更新移动止损失败: {e}")
            return False
    
    def cleanup_expired_stop_orders(self) -> None:
        """清理过期的止损止盈订单（功能已停用）"""
        try:
            self.logger.info("开始清理过期的止损止盈订单（功能已停用，仅记录日志）")
            
            # 获取所有交易对
            all_symbols = self.exchange.get_all_symbols()
            
            # 只记录日志，不执行任何清理操作
            for symbol in all_symbols:
                try:
                    # 获取该交易对的所有挂单（仅用于日志记录）
                    open_orders = self.exchange.get_open_orders(symbol)
                    
                    if open_orders:
                        self.logger.info(f"交易对 {symbol} 有 {len(open_orders)} 个挂单（清理功能已停用）")
                    else:
                        self.logger.debug(f"交易对 {symbol} 没有挂单")
                        
                except Exception as e:
                    self.logger.warning(f"检查交易对 {symbol} 的订单时出错: {e}")
                    
            self.logger.info("过期止损止盈订单检查完成（清理功能已停用）")
        except Exception as e:
            self.logger.error(f"检查过期止损止盈订单失败: {e}")
    
    def _cancel_all_orders_for_symbol(self, symbol: str, open_orders: List[Dict[str, Any]]) -> bool:
        """取消指定交易对的所有订单（内部辅助方法）"""
        try:
            # 获取所有订单ID并逐一取消
            order_ids_to_cancel = []
            for order in open_orders:
                order_id = order.get('clientOrderId') or order.get('orderId')
                if order_id:
                    order_ids_to_cancel.append((str(order_id), order.get('type', 'UNKNOWN')))
            
            # 逐一取消订单
            success_count = 0
            for order_id, order_type in order_ids_to_cancel:
                self.logger.info(f"清理订单 {symbol}: {order_id} (类型: {order_type})")
                if self.cancel_order_with_retry(symbol, order_id):
                    success_count += 1
                else:
                    self.logger.warning(f"取消订单失败: {symbol} {order_id}")
            
            self.logger.info(f"订单清理完成: {symbol} 总订单数={len(order_ids_to_cancel)} 成功取消={success_count}")
            return success_count == len(order_ids_to_cancel)
        except Exception as e:
            self.logger.error(f"取消订单失败: {e}")
            return False

    def _cancel_specific_orders(self, symbol: str, orders_to_cancel: List[Tuple[str, str]]) -> bool:
        """取消特定订单（内部辅助方法）"""
        try:
            success_count = 0
            for order_id, order_type in orders_to_cancel:
                self.logger.info(f"清理不匹配订单 {symbol}: {order_id} (类型: {order_type})")
                if self.cancel_order_with_retry(symbol, order_id):
                    success_count += 1
                else:
                    self.logger.warning(f"取消订单失败: {symbol} {order_id}")
            
            self.logger.info(f"特定订单清理完成: {symbol} 总订单数={len(orders_to_cancel)} 成功取消={success_count}")
            return success_count == len(orders_to_cancel)
        except Exception as e:
            self.logger.error(f"取消特定订单失败: {e}")
            return False

    def cleanup_all_orders_for_symbol(self, symbol: str) -> bool:
        """清理指定交易对的所有订单（简化版本）"""
        try:
            self.logger.info(f"开始清理交易对 {symbol} 的所有订单")
            
            # 获取当前所有挂单
            open_orders = self.exchange.get_open_orders(symbol)
            
            if open_orders:
                self.logger.info(f"发现挂单 {symbol}，数量: {len(open_orders)}，开始清理所有订单")
                return self._cancel_all_orders_for_symbol(symbol, open_orders)
            else:
                self.logger.info(f"交易对 {symbol} 没有挂单需要清理")
                return True
                
        except Exception as e:
            self.logger.error(f"清理交易对 {symbol} 的订单失败: {e}")
            return False

    def _verify_order_exists(self, symbol: str, order_id: str) -> bool:
        """验证订单是否真正存在于交易所"""
        try:
            # 查询交易所获取订单详情
            order_info = self.exchange.get_order(symbol, order_id)
            
            if order_info:
                # 检查订单状态是否有效
                status = order_info.get('status')
                if status in ['NEW', 'PARTIALLY_FILLED']:
                    self.logger.debug(f"订单验证成功: {symbol} {order_id} 状态: {status}")
                    return True
                else:
                    self.logger.warning(f"订单状态无效: {symbol} {order_id} 状态: {status}")
                    return False
            else:
                self.logger.warning(f"订单不存在: {symbol} {order_id}")
                return False
                
        except Exception as e:
            # 网络错误或API限制，保守返回True（假设订单存在）
            if "timed out" in str(e) or "network" in str(e).lower() or "rate limit" in str(e).lower():
                self.logger.warning(f"订单验证网络错误，假设订单存在: {symbol} {order_id} - {e}")
                return True
            else:
                self.logger.error(f"订单验证失败: {symbol} {order_id} - {e}")
                return False

    def cancel_all_stop_take_profit_orders(self, symbol: str) -> bool:
        """平仓后取消指定交易对的所有止损止盈订单"""
        try:
            self.logger.info(f"平仓后开始清理 {symbol} 的所有止损止盈订单")
            
            # 获取当前所有挂单
            open_orders = self.exchange.get_open_orders(symbol)
            
            if not open_orders:
                self.logger.info(f"交易对 {symbol} 没有挂单需要清理")
                return True
            
            # 只清理止损止盈订单
            orders_to_cancel = []
            for order in open_orders:
                order_id = order.get('clientOrderId') or order.get('orderId')
                order_type = order.get('type')
                
                # 只清理止损和止盈订单
                if order_type in ['STOP_MARKET', 'STOP', 'TAKE_PROFIT_MARKET', 'TAKE_PROFIT']:
                    order_id_str = str(order_id) if order_id else None
                    if order_id_str:
                        orders_to_cancel.append((order_id_str, order_type))
                        self.logger.info(f"发现止损止盈订单需要清理：{order_type} {order_id_str}")
            
            if orders_to_cancel:
                self.logger.info(f"发现 {len(orders_to_cancel)} 个止损止盈订单需要清理")
                return self._cancel_specific_orders(symbol, orders_to_cancel)
            else:
                self.logger.info(f"交易对 {symbol} 没有止损止盈订单需要清理")
                return True
                
        except Exception as e:
            self.logger.error(f"清理止损止盈订单失败: {symbol} - {e}")
            return False

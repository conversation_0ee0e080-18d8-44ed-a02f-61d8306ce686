#!/usr/bin/env python3
"""
PIPPINUSDT 精度问题修复测试脚本
分析并修复下单精度错误(-1111)
"""

import sys
import os
import logging
import json
import requests
from decimal import Decimal, ROUND_DOWN

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
log = logging.getLogger(__name__)

def get_pippinusdt_rules():
    """获取PIPPINUSDT的交易规则"""
    try:
        # 使用国内可访问的镜像
        urls = [
            'https://fapi.binance.com/fapi/v1/exchangeInfo?symbol=PIPPINUSDT',
            'https://binance.com/fapi/v1/exchangeInfo?symbol=PIPPINUSDT',
            'https://api.binance.com/fapi/v1/exchangeInfo?symbol=PIPPINUSDT'
        ]
        
        for url in urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('symbols'):
                        symbol_info = data['symbols'][0]
                        filters = symbol_info.get('filters', [])
                        
                        result = {}
                        for f in filters:
                            if f.get('filterType') == 'PRICE_FILTER':
                                result['tick_size'] = float(f.get('tickSize', 0))
                                result['max_price'] = float(f.get('maxPrice', 0))
                                result['min_price'] = float(f.get('minPrice', 0))
                            elif f.get('filterType') == 'LOT_SIZE':
                                result['step_size'] = float(f.get('stepSize', 0))
                                result['max_qty'] = float(f.get('maxQty', 0))
                                result['min_qty'] = float(f.get('minQty', 0))
                            elif f.get('filterType') == 'MIN_NOTIONAL':
                                result['min_notional'] = float(f.get('notional', 0))
                        
                        log.info(f"PIPPINUSDT 交易规则: {json.dumps(result, indent=2)}")
                        return result
            except Exception as e:
                log.warning(f"从 {url} 获取数据失败: {e}")
                continue
                
        log.error("无法从任何URL获取PIPPINUSDT交易规则")
        return None
        
    except Exception as e:
        log.error(f"获取交易规则失败: {e}")
        return None

def format_order_params_fixed(price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化函数"""
    try:
        from decimal import Decimal, ROUND_DOWN
        
        def format_to_precision(value, precision_step):
            """根据精度步长格式化数值"""
            if not precision_step or precision_step <= 0:
                return str(value)
            
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出 - 修复精度问题
            if decimal_places > 0:
                # 使用格式化字符串，确保不丢失精度
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 去除末尾的0，但保留必要的小数位
                formatted = formatted.rstrip('0').rstrip('.') if '.' in formatted else formatted
                return formatted if formatted else format_str.format(float(rounded))
            else:
                return str(int(float(rounded)))
        
        # 格式化价格
        price_str = format_to_precision(price, tick_size)
        
        # 格式化数量，确保不小于最小数量
        if min_qty and qty < min_qty:
            qty = min_qty
        qty_str = format_to_precision(qty, step_size)
        
        # 最终检查，确保不为空
        if not price_str or price_str in ['', '.', '0.']:
            price_str = str(price)
        if not qty_str or qty_str in ['', '.', '0.']:
            qty_str = str(qty)
        
        return price_str, qty_str
        
    except Exception as e:
        log.error(f"格式化参数失败: {e}")
        return str(price), str(qty)

def test_precision_formatting():
    """测试精度格式化"""
    log.info("=== 测试PIPPINUSDT精度格式化 ===")
    
    # 获取交易规则
    rules = get_pippinusdt_rules()
    if not rules:
        log.error("无法获取交易规则，使用默认值")
        rules = {
            'tick_size': 0.0001,  # 价格精度
            'step_size': 1.0,     # 数量精度（整数）
            'min_qty': 1.0,       # 最小数量
            'min_notional': 5.0   # 最小名义价值
        }
    
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules.get('min_notional', 5.0)
    
    log.info(f"交易规则: tick_size={tick_size}, step_size={step_size}, min_qty={min_qty}, min_notional={min_notional}")
    
    # 测试不同的价格和数量组合
    test_cases = [
        (0.12345, 100.0),   # 正常情况
        (0.123456, 50.5),   # 价格精度超限
        (0.1234, 0.5),      # 数量小于最小值
        (0.123456789, 75.25), # 高精度价格
    ]
    
    for price, qty in test_cases:
        log.info(f"\n测试输入: price={price}, qty={qty}")
        
        # 原始格式化函数
        price_str_orig, qty_str_orig = format_order_params_fixed(price, qty, tick_size, step_size, min_qty)
        log.info(f"原始函数结果: price_str='{price_str_orig}', qty_str='{qty_str_orig}'")
        
        # 计算名义价值
        notional = float(price_str_orig) * float(qty_str_orig)
        log.info(f"名义价值: {notional:.4f} USDT")
        
        # 检查是否满足最小名义价值
        if notional < min_notional:
            log.warning(f"名义价值 {notional:.4f} 小于最小要求 {min_notional}")
        
        # 检查精度是否符合要求
        price_precision = len(price_str_orig.split('.')[1]) if '.' in price_str_orig else 0
        tick_precision = len(str(tick_size).split('.')[1]) if '.' in str(tick_size) else 0
        
        if price_precision > tick_precision:
            log.error(f"价格精度超限: 实际精度{price_precision} > 要求精度{tick_precision}")
        else:
            log.info(f"价格精度符合: 实际精度{price_precision} <= 要求精度{tick_precision}")

def create_fixed_format_function():
    """创建修复后的格式化函数代码"""
    
    fixed_code = '''def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化，使用Decimal确保精度"""
    from decimal import Decimal, ROUND_DOWN
    
    def format_to_precision(value, precision_step):
        """根据精度步长格式化数值"""
        if not precision_step or precision_step <= 0:
            return str(value)
        
        try:
            # 使用Decimal确保精度
            decimal_value = Decimal(str(value))
            decimal_step = Decimal(str(precision_step))
            
            # 向下取整到最近的步长倍数
            rounded = (decimal_value / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_step
            
            # 计算小数位数
            step_str = str(precision_step)
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
            
            # 格式化输出 - 修复精度问题
            if decimal_places > 0:
                # 使用格式化字符串，确保不丢失精度
                format_str = f"{{:.{decimal_places}f}}"
                formatted = format_str.format(float(rounded))
                # 去除末尾的0，但保留必要的小数位
                if '.' in formatted:
                    # 分割整数和小数部分
                    int_part, dec_part = formatted.split('.')
                    # 去除小数部分末尾的0
                    dec_part = dec_part.rstrip('0')
                    if dec_part:
                        formatted = f"{int_part}.{dec_part}"
                    else:
                        formatted = int_part
                return formatted
            else:
                return str(int(float(rounded)))
                
        except Exception as e:
            # 如果Decimal处理失败，回退到简单字符串格式化
            return str(value)
    
    try:
        # 格式化价格
        price_str = format_to_precision(price, tick_size)
        
        # 格式化数量，确保不小于最小数量
        if min_qty and float(qty) < float(min_qty):
            qty = min_qty
        qty_str = format_to_precision(qty, step_size)
        
        # 最终验证和清理
        if not price_str or price_str.strip() in ['', '.', '0.']:
            price_str = str(float(price))  # 确保有效的浮点字符串
        
        if not qty_str or qty_str.strip() in ['', '.', '0.']:
            qty_str = str(float(qty))  # 确保有效的浮点字符串
        
        # 最终精度检查
        if tick_size and '.' in str(tick_size):
            max_decimals = len(str(tick_size).split('.')[1])
            if '.' in price_str:
                actual_decimals = len(price_str.split('.')[1])
                if actual_decimals > max_decimals:
                    # 重新格式化以符合精度要求
                    price_str = f"{float(price_str):.{max_decimals}f}".rstrip('0').rstrip('.')
        
        return price_str, qty_str
        
    except Exception as e:
        # 最后的回退方案
        return str(float(price)), str(float(qty))'''
    
    return fixed_code

if __name__ == '__main__':
    log.info("=== PIPPINUSDT 精度问题修复测试 ===")
    
    # 测试精度格式化
    test_precision_formatting()
    
    # 生成修复后的代码
    fixed_code = create_fixed_format_function()
    
    # 保存修复后的代码
    with open('pippinusdt_precision_fix.py', 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('# PIPPINUSDT 精度问题修复代码\n\n')
        f.write(fixed_code)
    
    log.info("修复后的代码已保存到 pippinusdt_precision_fix.py")
    log.info("建议将此函数替换 strategy/maker_channel_enhanced.py 中的 _format_order_params 方法")
"""
动态参数配置矩阵
Dynamic Parameter Configuration Matrix for Universal Trading

功能：
1. 为不同分类币种提供差异化交易参数
2. 支持参数动态调整和热更新
3. 提供参数查询和优化接口
"""

import json
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

from ..classifier.symbol_classifier import SymbolTier

@dataclass
class TradingParameters:
    """交易参数配置"""
    # 价格偏差容忍度
    price_deviation_tolerance: float = 0.02    # 2%
    
    # 仓位限制
    min_position_usdt: float = 5.0             # 最小仓位5U
    max_position_usdt: float = 1000.0          # 最大仓位1000U
    max_position_ratio: float = 0.1            # 最大仓位比例10%
    
    # 止盈止损
    take_profit_ratio: float = 0.015           # 止盈1.5%
    stop_loss_ratio: float = 0.01              # 止损1%
    
    # 保证金缓冲
    margin_buffer_ratio: float = 0.2           # 保证金缓冲20%
    
    # 重试机制
    max_retry_count: int = 3                   # 最大重试3次
    retry_delay_seconds: float = 1.0           # 重试间隔1秒
    quantity_scale_factors: List[float] = None # 数量缩放因子
    
    # 风控参数
    max_daily_trades: int = 50                 # 每日最大交易次数
    max_consecutive_losses: int = 5            # 最大连续亏损次数
    volatility_threshold: float = 0.1          # 波动率阈值10%
    
    # 流动性要求
    min_volume_24h: float = 1_000_000         # 最小24h交易量100万U
    min_depth_1pct: float = 10_000            # 最小1%深度1万U
    
    # 时间限制
    order_ttl_minutes: int = 30               # 订单有效期30分钟
    position_hold_max_hours: int = 24         # 最大持仓时间24小时
    
    def __post_init__(self):
        if self.quantity_scale_factors is None:
            self.quantity_scale_factors = [1.0, 0.8, 0.6, 0.4, 0.25]

class ParameterMatrix:
    """参数配置矩阵管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        
        # 参数矩阵：每个分类对应一套参数
        self.tier_parameters: Dict[SymbolTier, TradingParameters] = {}
        
        # 个性化参数：特定币种的定制参数
        self.symbol_parameters: Dict[str, TradingParameters] = {}
        
        # 参数更新历史
        self.parameter_history: List[Dict] = []
        
        # 初始化默认参数
        self._init_default_parameters()
        
        # 加载配置文件
        if config_file:
            self.load_config(config_file)
    
    def _init_default_parameters(self):
        """初始化默认参数配置"""
        
        # 蓝筹币参数（保守策略）
        self.tier_parameters[SymbolTier.TIER_1] = TradingParameters(
            price_deviation_tolerance=0.01,      # 1%偏差容忍
            min_position_usdt=10.0,
            max_position_usdt=5000.0,
            max_position_ratio=0.2,              # 20%最大仓位
            take_profit_ratio=0.01,              # 1%止盈
            stop_loss_ratio=0.008,               # 0.8%止损
            margin_buffer_ratio=0.15,            # 15%保证金缓冲
            max_retry_count=5,
            retry_delay_seconds=0.5,
            max_daily_trades=30,
            max_consecutive_losses=3,
            volatility_threshold=0.05,           # 5%波动率阈值
            min_volume_24h=50_000_000,          # 5000万U最小交易量
            min_depth_1pct=100_000,             # 10万U最小深度
            order_ttl_minutes=60,               # 60分钟订单有效期
            position_hold_max_hours=48          # 48小时最大持仓
        )
        
        # 主流币参数（平衡策略）
        self.tier_parameters[SymbolTier.TIER_2] = TradingParameters(
            price_deviation_tolerance=0.015,     # 1.5%偏差容忍
            min_position_usdt=8.0,
            max_position_usdt=3000.0,
            max_position_ratio=0.15,             # 15%最大仓位
            take_profit_ratio=0.012,             # 1.2%止盈
            stop_loss_ratio=0.01,                # 1%止损
            margin_buffer_ratio=0.18,            # 18%保证金缓冲
            max_retry_count=4,
            retry_delay_seconds=0.8,
            max_daily_trades=40,
            max_consecutive_losses=4,
            volatility_threshold=0.08,           # 8%波动率阈值
            min_volume_24h=10_000_000,          # 1000万U最小交易量
            min_depth_1pct=50_000,              # 5万U最小深度
            order_ttl_minutes=45,
            position_hold_max_hours=36
        )
        
        # 新兴币参数（积极策略）
        self.tier_parameters[SymbolTier.TIER_3] = TradingParameters(
            price_deviation_tolerance=0.02,      # 2%偏差容忍
            min_position_usdt=6.0,
            max_position_usdt=2000.0,
            max_position_ratio=0.12,             # 12%最大仓位
            take_profit_ratio=0.015,             # 1.5%止盈
            stop_loss_ratio=0.012,               # 1.2%止损
            margin_buffer_ratio=0.22,            # 22%保证金缓冲
            max_retry_count=3,
            retry_delay_seconds=1.0,
            max_daily_trades=50,
            max_consecutive_losses=5,
            volatility_threshold=0.12,           # 12%波动率阈值
            min_volume_24h=5_000_000,           # 500万U最小交易量
            min_depth_1pct=20_000,              # 2万U最小深度
            order_ttl_minutes=30,
            position_hold_max_hours=24
        )
        
        # 小币种参数（谨慎策略）
        self.tier_parameters[SymbolTier.TIER_4] = TradingParameters(
            price_deviation_tolerance=0.025,     # 2.5%偏差容忍
            min_position_usdt=5.0,
            max_position_usdt=1000.0,
            max_position_ratio=0.08,             # 8%最大仓位
            take_profit_ratio=0.02,              # 2%止盈
            stop_loss_ratio=0.015,               # 1.5%止损
            margin_buffer_ratio=0.25,            # 25%保证金缓冲
            max_retry_count=2,
            retry_delay_seconds=1.5,
            max_daily_trades=30,
            max_consecutive_losses=3,
            volatility_threshold=0.15,           # 15%波动率阈值
            min_volume_24h=1_000_000,           # 100万U最小交易量
            min_depth_1pct=10_000,              # 1万U最小深度
            order_ttl_minutes=20,
            position_hold_max_hours=12
        )
        
        # 新币种参数（超谨慎策略）
        self.tier_parameters[SymbolTier.TIER_5] = TradingParameters(
            price_deviation_tolerance=0.03,      # 3%偏差容忍
            min_position_usdt=5.0,
            max_position_usdt=500.0,
            max_position_ratio=0.05,             # 5%最大仓位
            take_profit_ratio=0.025,             # 2.5%止盈
            stop_loss_ratio=0.02,                # 2%止损
            margin_buffer_ratio=0.3,             # 30%保证金缓冲
            max_retry_count=1,
            retry_delay_seconds=2.0,
            max_daily_trades=20,
            max_consecutive_losses=2,
            volatility_threshold=0.2,            # 20%波动率阈值
            min_volume_24h=500_000,             # 50万U最小交易量
            min_depth_1pct=5_000,               # 5000U最小深度
            order_ttl_minutes=15,
            position_hold_max_hours=6
        )
    
    def get_parameters(self, symbol: str, tier: SymbolTier) -> TradingParameters:
        """获取指定币种的交易参数"""
        # 优先返回个性化参数
        if symbol in self.symbol_parameters:
            self.logger.debug(f"使用个性化参数: {symbol}")
            return self.symbol_parameters[symbol]
        
        # 返回分类参数
        if tier in self.tier_parameters:
            self.logger.debug(f"使用分类参数: {symbol} -> {tier.value}")
            return self.tier_parameters[tier]
        
        # 默认返回小币种参数
        self.logger.warning(f"未找到参数配置，使用默认: {symbol}")
        return self.tier_parameters[SymbolTier.TIER_4]
    
    def set_symbol_parameters(self, symbol: str, parameters: TradingParameters):
        """设置币种个性化参数"""
        self.symbol_parameters[symbol] = parameters
        
        # 记录参数更新历史
        self.parameter_history.append({
            'timestamp': time.time(),
            'action': 'set_symbol_parameters',
            'symbol': symbol,
            'parameters': asdict(parameters)
        })
        
        self.logger.info(f"设置个性化参数: {symbol}")
    
    def update_tier_parameters(self, tier: SymbolTier, parameters: TradingParameters):
        """更新分类参数"""
        old_params = self.tier_parameters.get(tier)
        self.tier_parameters[tier] = parameters
        
        # 记录参数更新历史
        self.parameter_history.append({
            'timestamp': time.time(),
            'action': 'update_tier_parameters',
            'tier': tier.value,
            'old_parameters': asdict(old_params) if old_params else None,
            'new_parameters': asdict(parameters)
        })
        
        self.logger.info(f"更新分类参数: {tier.value}")
    
    def optimize_parameters(self, symbol: str, tier: SymbolTier, 
                          performance_data: Dict[str, Any]) -> TradingParameters:
        """基于历史表现优化参数"""
        current_params = self.get_parameters(symbol, tier)
        optimized_params = TradingParameters(**asdict(current_params))
        
        try:
            # 获取性能指标
            success_rate = performance_data.get('success_rate', 0.5)
            avg_profit = performance_data.get('avg_profit', 0.0)
            max_drawdown = performance_data.get('max_drawdown', 0.0)
            volatility = performance_data.get('volatility', 0.1)
            
            # 基于成功率调整参数
            if success_rate < 0.3:  # 成功率过低
                # 放宽价格偏差容忍度
                optimized_params.price_deviation_tolerance *= 1.2
                # 减少最大仓位
                optimized_params.max_position_ratio *= 0.8
                # 增加保证金缓冲
                optimized_params.margin_buffer_ratio *= 1.1
                
            elif success_rate > 0.7:  # 成功率较高
                # 适当收紧参数
                optimized_params.price_deviation_tolerance *= 0.9
                # 可以增加仓位
                optimized_params.max_position_ratio *= 1.1
                
            # 基于平均收益调整止盈止损
            if avg_profit < 0:  # 平均亏损
                # 收紧止损
                optimized_params.stop_loss_ratio *= 0.8
                # 放宽止盈
                optimized_params.take_profit_ratio *= 1.2
                
            # 基于最大回撤调整风控
            if max_drawdown > 0.05:  # 回撤过大
                # 减少连续亏损容忍
                optimized_params.max_consecutive_losses = max(1, 
                    optimized_params.max_consecutive_losses - 1)
                # 增加保证金缓冲
                optimized_params.margin_buffer_ratio *= 1.15
                
            # 基于波动率调整
            if volatility > optimized_params.volatility_threshold * 1.5:
                # 高波动率：减少仓位，增加缓冲
                optimized_params.max_position_ratio *= 0.7
                optimized_params.margin_buffer_ratio *= 1.2
                
            # 设置优化后的参数
            self.set_symbol_parameters(symbol, optimized_params)
            
            self.logger.info(f"参数优化完成: {symbol}, 成功率: {success_rate:.2%}")
            return optimized_params
            
        except Exception as e:
            self.logger.error(f"参数优化失败 {symbol}: {e}")
            return current_params
    
    def get_parameter_comparison(self, symbol: str, tier: SymbolTier) -> Dict[str, Any]:
        """获取参数对比信息"""
        current_params = self.get_parameters(symbol, tier)
        tier_params = self.tier_parameters.get(tier)
        
        comparison = {
            'symbol': symbol,
            'tier': tier.value,
            'using_custom': symbol in self.symbol_parameters,
            'current_parameters': asdict(current_params),
            'tier_parameters': asdict(tier_params) if tier_params else None,
            'differences': {}
        }
        
        # 计算差异
        if tier_params and symbol in self.symbol_parameters:
            current_dict = asdict(current_params)
            tier_dict = asdict(tier_params)
            
            for key in current_dict:
                if current_dict[key] != tier_dict.get(key):
                    comparison['differences'][key] = {
                        'custom': current_dict[key],
                        'tier_default': tier_dict.get(key)
                    }
        
        return comparison
    
    def save_config(self, filepath: str):
        """保存参数配置到文件"""
        config_data = {
            'tier_parameters': {
                tier.value: asdict(params) 
                for tier, params in self.tier_parameters.items()
            },
            'symbol_parameters': {
                symbol: asdict(params) 
                for symbol, params in self.symbol_parameters.items()
            },
            'parameter_history': self.parameter_history[-100:],  # 保留最近100条历史
            'save_time': time.time()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"参数配置已保存到: {filepath}")
    
    def load_config(self, filepath: str):
        """从文件加载参数配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载分类参数
            if 'tier_parameters' in config_data:
                for tier_name, params_dict in config_data['tier_parameters'].items():
                    tier = SymbolTier(tier_name)
                    self.tier_parameters[tier] = TradingParameters(**params_dict)
            
            # 加载个性化参数
            if 'symbol_parameters' in config_data:
                for symbol, params_dict in config_data['symbol_parameters'].items():
                    self.symbol_parameters[symbol] = TradingParameters(**params_dict)
            
            # 加载历史记录
            if 'parameter_history' in config_data:
                self.parameter_history = config_data['parameter_history']
            
            self.logger.info(f"参数配置已从文件加载: {filepath}")
            
        except Exception as e:
            self.logger.error(f"加载参数配置失败: {e}")
    
    def get_all_parameters_summary(self) -> Dict[str, Any]:
        """获取所有参数配置摘要"""
        return {
            'tier_count': len(self.tier_parameters),
            'custom_symbol_count': len(self.symbol_parameters),
            'history_count': len(self.parameter_history),
            'tier_parameters': {
                tier.value: {
                    'min_position': params.min_position_usdt,
                    'max_position': params.max_position_usdt,
                    'take_profit': params.take_profit_ratio,
                    'stop_loss': params.stop_loss_ratio,
                    'max_daily_trades': params.max_daily_trades
                }
                for tier, params in self.tier_parameters.items()
            },
            'custom_symbols': list(self.symbol_parameters.keys())
        }

# 使用示例
if __name__ == "__main__":
    # 初始化参数矩阵
    matrix = ParameterMatrix()
    
    # 获取不同分类的参数
    from ..classifier.symbol_classifier import SymbolTier
    
    btc_params = matrix.get_parameters('BTCUSDT', SymbolTier.TIER_1)
    print(f"BTC参数 - 止盈: {btc_params.take_profit_ratio:.1%}, 止损: {btc_params.stop_loss_ratio:.1%}")
    
    new_coin_params = matrix.get_parameters('NEWCOINUSDT', SymbolTier.TIER_5)
    print(f"新币参数 - 止盈: {new_coin_params.take_profit_ratio:.1%}, 止损: {new_coin_params.stop_loss_ratio:.1%}")
    
    # 获取参数摘要
    summary = matrix.get_all_parameters_summary()
    print(f"\n参数配置摘要: {summary}")
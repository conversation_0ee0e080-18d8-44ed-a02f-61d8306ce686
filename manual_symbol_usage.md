# 手动干预接口使用说明

## 功能说明
手动干预接口允许交易员在策略运行过程中手动指定交易币种，无需等待自动选币时间窗口。

## 使用方法

### 1. 创建干预文件
在策略运行目录下创建文件：`manual_symbol.txt`

### 2. 写入币种代码
在文件中写入要交易的币种代码（必须是USDT交易对），例如：
```
BTCUSDT
```

### 3. 保存文件
保存文件后，策略会在下一个循环周期（约30秒内）检测到该文件并执行以下操作：

1. **验证币种格式**：确保是有效的USDT交易对
2. **平仓现有持仓**：如果当前有持仓，会先平仓
3. **切换币种**：将策略的交易币种切换为指定币种
4. **删除干预文件**：处理完成后自动删除文件，避免重复执行
5. **记录日志**：详细记录干预过程

## 注意事项

1. **币种格式**：必须是大写的USDT交易对，如 `BTCUSDT`、`ETHUSDT`
2. **文件编码**：建议使用UTF-8编码
3. **单次使用**：文件处理后会自动删除，每次干预需要重新创建
4. **安全性**：如果当前有持仓，会先平仓再切换币种
5. **响应时间**：通常在30秒内生效

## 示例

### 切换到比特币
```bash
echo BTCUSDT > manual_symbol.txt
```

### 切换到以太坊
```bash
echo ETHUSDT > manual_symbol.txt
```

### 切换到其他币种
```bash
echo ADAUSDT > manual_symbol.txt
```

## 日志监控
干预操作会产生以下日志：
- `🎯 MANUAL_INTERVENTION: Symbol changed from XXX to YYY`
- `📤 MANUAL_INTERVENTION_CLOSE: Closing position for manual symbol change`
- `🗑️ MANUAL_FILE_REMOVED: manual_symbol.txt deleted after processing`

通过监控这些日志可以确认干预操作是否成功执行。
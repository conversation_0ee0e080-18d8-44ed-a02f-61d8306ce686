#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
import json
import logging
import pandas as pd
from http_client import HttpClient
from strategy.maker_channel_enhanced import MakerChannelEnhanced

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def debug_xan_selection():
    """调试XANUSDT为什么没有被选出"""
    try:
        # 加载配置
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        with open('config/config.json', 'r', encoding='utf-8') as f:
            json_config = json.load(f)
        
        # 合并配置
        config.update(json_config)
        
        # 初始化HttpClient和策略
        http = HttpClient(
            config['api_key'], 
            config['api_secret'], 
            config.get('base_url', 'https://fapi.binance.com'),
            config.get('verify_ssl', True)
        )
        
        strategy = MakerChannelEnhanced(http, config)
        
        print("=== 调试XANUSDT选择过程 ===")
        
        # 1. 检查XANUSDT基本信息
        print("\n1. 检查XANUSDT基本信息:")
        strategy.load_symbols_with_cache()
        xan_info = strategy.symbols_info.get('XANUSDT')
        if xan_info:
            print(f"  XANUSDT信息: {xan_info}")
            print(f"  上线天数: {xan_info['age_days']:.1f}天")
        else:
            print("  XANUSDT不在币种列表中!")
            return
        
        # 2. 检查24小时行情数据
        print("\n2. 检查24小时行情数据:")
        ticker_data = http.get('/fapi/v1/ticker/24hr', {'symbol': 'XANUSDT'})
        if ticker_data:
            print(f"  24h涨幅: {ticker_data.get('priceChangePercent', 'N/A')}%")
            print(f"  24h成交额: {float(ticker_data.get('quoteVolume', 0)):,.0f} USDT")
            print(f"  当前价格: {ticker_data.get('lastPrice', 'N/A')}")
        else:
            print("  获取24h行情数据失败!")
        
        # 3. 检查深度
        print("\n3. 检查深度:")
        depth = strategy.get_depth01pct('XANUSDT')
        print(f"  0.1%深度: {depth:,.0f} USDT")
        
        # 4. 检查K线数据和技术指标
        print("\n4. 检查K线数据和技术指标:")
        df15 = strategy.get_klines('XANUSDT', '15m', 200)
        if df15 is not None and len(df15) > 50:
            print(f"  K线数据长度: {len(df15)}")
            
            # 计算技术指标
            ema = df15['c'].ewm(span=strategy.entry_mac_params[0]).mean().iloc[-1]
            atr = strategy._calculate_atr(df15['h'], df15['l'], df15['c'], length=strategy.entry_mac_params[1]).iloc[-1]
            upper = ema + atr * strategy.entry_mac_params[2]
            lower = ema - atr * strategy.entry_mac_params[3]
            close = df15['c'].iloc[-1]
            pos = (close - lower) / (upper - lower + 1e-8)
            
            print(f"  当前价格: {close:.6f}")
            print(f"  EMA({strategy.entry_mac_params[0]}): {ema:.6f}")
            print(f"  ATR({strategy.entry_mac_params[1]}): {atr:.6f}")
            print(f"  上轨: {upper:.6f}")
            print(f"  下轨: {lower:.6f}")
            print(f"  通道位置: {pos:.3f} (需要>=0.6或价格>=上轨)")
            
            # 评分
            df15.name = 'XANUSDT'
            card = strategy.score_symbol(df15, xan_info['age_days'])
            print(f"  总评分: {card.total_score:.2f} (需要>={strategy.SCORE_GATE})")
            print(f"  评分详情: 动量={card.momentum_score:.1f}, 趋势={card.trend_score:.1f}, 深度={card.depth_score:.1f}, 新币={card.newcoin_score:.1f}")
        else:
            print("  获取K线数据失败或数据不足!")
        
        # 5. 检查策略模式和筛选条件
        print(f"\n5. 策略模式: {strategy.strategy_mode}")
        print(f"  新币最大天数: {strategy.new_coin_max_age_days}")
        print(f"  新币动量范围: {strategy.new_coin_momentum_range}")
        print(f"  新币深度最小值: {strategy.new_coin_depth_min}")
        
        # 6. 模拟龙头池筛选
        print("\n6. 模拟龙头池筛选:")
        try:
            if strategy.strategy_mode == 'new_coin_burst':
                symbols = strategy.get_newcoin_breakthrough(strategy.new_coin_max_age_days)
            else:
                symbols = list(set(strategy.get_top50_market_leaders() + strategy.get_newcoin_breakthrough()))
            
            if 'XANUSDT' in symbols:
                print("  ✓ XANUSDT在龙头池中")
                print(f"  龙头池总数: {len(symbols)}")
                print(f"  前10个币种: {symbols[:10]}")
            else:
                print("  ✗ XANUSDT不在龙头池中")
                print(f"  龙头池总数: {len(symbols)}")
                print(f"  前10个币种: {symbols[:10]}")
        except Exception as e:
            print(f"  龙头池筛选失败: {e}")
        
    except Exception as e:
        print(f"调试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_xan_selection()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复ORDERUSDT精度问题的补丁脚本
"""

import os
import shutil
from datetime import datetime

def backup_original_file():
    """备份原始文件"""
    original_file = "e:/allmac/strategy/maker_channel_enhanced.py"
    backup_file = f"e:/allmac/strategy/maker_channel_enhanced.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已备份原始文件到: {backup_file}")
        return True
    else:
        print(f"❌ 原始文件不存在: {original_file}")
        return False

def create_fixed_format_function():
    """创建修复后的格式化函数"""
    return '''
    def _format_order_params(self, price, qty, tick_size, step_size, min_qty):
        """安全的下单参数格式化，避免精度错误"""
        import decimal as dec
        dec.getcontext().prec = 18
        
        # 计算允许的最大小数位数
        def get_decimal_places(value):
            if value == 0:
                return 0
            str_val = f"{value:.20f}".rstrip('0')
            if '.' in str_val:
                return len(str_val.split('.')[-1])
            return 0
        
        # 价格格式化
        if tick_size:
            price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
            tick_decimals = get_decimal_places(tick_size)
            # 限制小数位数，避免精度错误
            price_str = f"{float(price_dec):.{tick_decimals}f}".rstrip('0').rstrip('.')
            if not price_str or price_str == '':
                price_str = f"{float(price_dec):.{max(1, tick_decimals)}f}"
        else:
            price_str = str(price)
        
        # 数量格式化
        if step_size:
            qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
            qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
            step_decimals = get_decimal_places(step_size)
            # 限制小数位数，避免精度错误
            qty_str = f"{float(qty_dec):.{step_decimals}f}".rstrip('0').rstrip('.')
            if not qty_str or qty_str == '':
                qty_str = f"{float(qty_dec):.{max(1, step_decimals)}f}"
        else:
            qty_str = str(qty)
        
        return price_str, qty_str
'''

def get_fixed_place_maker_order():
    """获取修复后的place_maker_order函数的关键部分"""
    return '''                    # 使用安全的格式化方法
                    price_str, qty_str = self._format_order_params(r_price, q_try, tick_size, step_size, min_qty)'''

def apply_precision_fix():
    """应用精度修复"""
    print("=== 应用ORDERUSDT精度修复 ===")
    
    # 备份原始文件
    if not backup_original_file():
        return False
    
    strategy_file = "e:/allmac/strategy/maker_channel_enhanced.py"
    
    try:
        # 读取原始文件
        with open(strategy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经应用过修复
        if '_format_order_params' in content:
            print("⚠️  精度修复已经应用过，跳过修复")
            return True
        
        # 找到类定义的位置，添加新的格式化函数
        class_def_pos = content.find('class MakerChannelEnhanced:')
        if class_def_pos == -1:
            print("❌ 未找到MakerChannelEnhanced类定义")
            return False
        
        # 找到第一个方法定义的位置
        first_method_pos = content.find('def ', class_def_pos)
        if first_method_pos == -1:
            print("❌ 未找到类方法定义")
            return False
        
        # 在第一个方法前插入新的格式化函数
        new_function = create_fixed_format_function()
        content = content[:first_method_pos] + new_function + '\n\n    ' + content[first_method_pos:]
        
        # 替换place_maker_order中的格式化逻辑
        old_format_pattern1 = '''                    # 用Decimal量化生成字符串，避免浮点误差
                    import decimal as dec
                    dec.getcontext().prec = 18
                    if tick_size:
                        price_dec = (dec.Decimal(str(r_price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
                        price_str = format(price_dec.normalize(), 'f')
                    else:
                        price_str = str(r_price)
                    if step_size:
                        qty_dec = (dec.Decimal(str(q_try)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
                        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
                        qty_str = format(qty_dec.normalize(), 'f')
                    else:
                        qty_str = str(q_try)'''
        
        new_format_code = get_fixed_place_maker_order()
        
        if old_format_pattern1 in content:
            content = content.replace(old_format_pattern1, new_format_code)
            print("✅ 已替换place_maker_order中的格式化逻辑")
        else:
            print("⚠️  未找到预期的格式化代码模式，可能需要手动修复")
        
        # 写入修复后的文件
        with open(strategy_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 精度修复已应用")
        return True
        
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return False

def test_fix():
    """测试修复效果"""
    print("\n=== 测试修复效果 ===")
    
    # 模拟测试数据
    test_cases = [
        {
            "price": 0.400000,
            "qty": 236.417,
            "tick_size": 0.00001,
            "step_size": 0.001,
            "min_qty": 0.001,
            "desc": "ORDERUSDT可能的规则"
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['desc']}:")
        print(f"  输入: 价格={case['price']}, 数量={case['qty']}")
        print(f"  规则: tick_size={case['tick_size']}, step_size={case['step_size']}")
        
        # 模拟修复后的格式化逻辑
        price_str, qty_str = format_order_params_fixed(
            case['price'], case['qty'], 
            case['tick_size'], case['step_size'], case['min_qty']
        )
        
        print(f"  修复后: 价格='{price_str}', 数量='{qty_str}'")
        
        # 检查精度
        price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
        qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
        
        def get_decimal_places(value):
            if value == 0:
                return 0
            str_val = f"{value:.20f}".rstrip('0')
            if '.' in str_val:
                return len(str_val.split('.')[-1])
            return 0
        
        tick_decimals = get_decimal_places(case['tick_size'])
        step_decimals = get_decimal_places(case['step_size'])
        
        print(f"  精度检查: 价格{price_decimals}位(≤{tick_decimals}), 数量{qty_decimals}位(≤{step_decimals})")
        
        if price_decimals <= tick_decimals and qty_decimals <= step_decimals:
            print("  ✅ 精度检查通过")
        else:
            print("  ❌ 精度检查失败")

def format_order_params_fixed(price, qty, tick_size, step_size, min_qty):
    """修复后的格式化函数（用于测试）"""
    import decimal as dec
    dec.getcontext().prec = 18
    
    def get_decimal_places(value):
        if value == 0:
            return 0
        str_val = f"{value:.20f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[-1])
        return 0
    
    # 价格格式化
    if tick_size:
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        tick_decimals = get_decimal_places(tick_size)
        price_str = f"{float(price_dec):.{tick_decimals}f}".rstrip('0').rstrip('.')
        if not price_str or price_str == '':
            price_str = f"{float(price_dec):.{max(1, tick_decimals)}f}"
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
        step_decimals = get_decimal_places(step_size)
        qty_str = f"{float(qty_dec):.{step_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str or qty_str == '':
            qty_str = f"{float(qty_dec):.{max(1, step_decimals)}f}"
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

if __name__ == "__main__":
    # 先测试修复效果
    test_fix()
    
    # 询问是否应用修复
    print("\n" + "="*50)
    response = input("是否应用精度修复到策略文件？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        if apply_precision_fix():
            print("\n✅ 精度修复已成功应用！")
            print("建议重新运行策略测试ORDERUSDT的下单功能。")
        else:
            print("\n❌ 精度修复应用失败！")
    else:
        print("\n取消应用修复。")
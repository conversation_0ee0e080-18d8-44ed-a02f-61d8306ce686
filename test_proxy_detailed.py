#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理连接详细测试脚本
测试代理服务器连接状况和API访问能力
"""

import requests
import json
import time
import logging
from urllib.parse import urljoin
import socket
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('proxy_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class ProxyTester:
    def __init__(self):
        self.proxy_url = "http://127.0.0.1:7897"
        self.proxies = {
            'http': self.proxy_url,
            'https': self.proxy_url
        }
        self.binance_base_url = "https://fapi.binance.com"
        self.test_results = {}
        
    def test_proxy_basic_connection(self):
        """测试代理服务器基本连接"""
        logger.info("=" * 60)
        logger.info("1. 测试代理服务器基本连接")
        logger.info("=" * 60)
        
        try:
            # 测试代理端口是否开放
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', 7897))
            sock.close()
            
            if result == 0:
                logger.info("✓ 代理端口 7897 可访问")
                self.test_results['proxy_port'] = True
            else:
                logger.error("✗ 代理端口 7897 不可访问")
                self.test_results['proxy_port'] = False
                return False
                
        except Exception as e:
            logger.error(f"✗ 代理端口测试异常: {e}")
            self.test_results['proxy_port'] = False
            return False
            
        # 测试通过代理访问外网
        try:
            logger.info("测试通过代理访问外网...")
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=self.proxies,
                timeout=10
            )
            
            if response.status_code == 200:
                ip_info = response.json()
                logger.info(f"✓ 代理连接成功，出口IP: {ip_info.get('origin', 'Unknown')}")
                self.test_results['proxy_external'] = True
                return True
            else:
                logger.error(f"✗ 代理外网访问失败，状态码: {response.status_code}")
                self.test_results['proxy_external'] = False
                return False
                
        except Exception as e:
            logger.error(f"✗ 代理外网访问异常: {e}")
            self.test_results['proxy_external'] = False
            return False
    
    def test_binance_api_connection(self):
        """测试币安API连接"""
        logger.info("=" * 60)
        logger.info("2. 测试币安API连接")
        logger.info("=" * 60)
        
        # 创建会话，配置重试策略
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 测试服务器时间API
        try:
            logger.info("测试服务器时间API...")
            url = f"{self.binance_base_url}/fapi/v1/time"
            
            response = session.get(
                url,
                proxies=self.proxies,
                timeout=15,
                verify=False
            )
            
            if response.status_code == 200:
                time_data = response.json()
                server_time = time_data.get('serverTime', 0)
                local_time = int(time.time() * 1000)
                time_diff = abs(server_time - local_time)
                
                logger.info(f"✓ 服务器时间API访问成功")
                logger.info(f"  服务器时间: {server_time}")
                logger.info(f"  本地时间: {local_time}")
                logger.info(f"  时间差: {time_diff}ms")
                
                self.test_results['binance_time'] = True
                return True
            else:
                logger.error(f"✗ 服务器时间API访问失败，状态码: {response.status_code}")
                logger.error(f"  响应内容: {response.text}")
                self.test_results['binance_time'] = False
                return False
                
        except requests.exceptions.ConnectTimeout as e:
            logger.error(f"✗ 连接超时: {e}")
            self.test_results['binance_time'] = False
            return False
        except requests.exceptions.ProxyError as e:
            logger.error(f"✗ 代理错误: {e}")
            self.test_results['binance_time'] = False
            return False
        except Exception as e:
            logger.error(f"✗ 服务器时间API访问异常: {e}")
            self.test_results['binance_time'] = False
            return False
    
    def test_market_data_api(self):
        """测试行情数据API"""
        logger.info("=" * 60)
        logger.info("3. 测试行情数据API")
        logger.info("=" * 60)
        
        try:
            logger.info("测试交易对信息API...")
            url = f"{self.binance_base_url}/fapi/v1/exchangeInfo"
            
            response = requests.get(
                url,
                proxies=self.proxies,
                timeout=20
            )
            
            if response.status_code == 200:
                exchange_info = response.json()
                symbols_count = len(exchange_info.get('symbols', []))
                logger.info(f"✓ 交易对信息API访问成功，共 {symbols_count} 个交易对")
                self.test_results['exchange_info'] = True
            else:
                logger.error(f"✗ 交易对信息API访问失败，状态码: {response.status_code}")
                self.test_results['exchange_info'] = False
                return False
                
        except Exception as e:
            logger.error(f"✗ 交易对信息API访问异常: {e}")
            self.test_results['exchange_info'] = False
            return False
        
        # 测试K线数据
        try:
            logger.info("测试K线数据API...")
            url = f"{self.binance_base_url}/fapi/v1/klines"
            params = {
                'symbol': 'BTCUSDT',
                'interval': '1m',
                'limit': 5
            }
            
            response = requests.get(
                url,
                params=params,
                proxies=self.proxies,
                timeout=15
            )
            
            if response.status_code == 200:
                klines = response.json()
                logger.info(f"✓ K线数据API访问成功，获取到 {len(klines)} 条数据")
                if klines:
                    latest_kline = klines[-1]
                    logger.info(f"  最新价格: {latest_kline[4]} USDT")
                self.test_results['klines'] = True
                return True
            else:
                logger.error(f"✗ K线数据API访问失败，状态码: {response.status_code}")
                self.test_results['klines'] = False
                return False
                
        except Exception as e:
            logger.error(f"✗ K线数据API访问异常: {e}")
            self.test_results['klines'] = False
            return False
    
    def test_account_api(self):
        """测试账户API（需要API密钥）"""
        logger.info("=" * 60)
        logger.info("4. 测试账户API")
        logger.info("=" * 60)
        
        # 读取配置文件获取API密钥
        try:
            with open('config/config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                api_key = config.get('api_key', '')
                api_secret = config.get('api_secret', '')
                
            if not api_key or not api_secret:
                logger.warning("⚠ 未找到API密钥配置，跳过账户API测试")
                self.test_results['account_api'] = 'skipped'
                return True
                
        except Exception as e:
            logger.warning(f"⚠ 读取配置文件失败: {e}，跳过账户API测试")
            self.test_results['account_api'] = 'skipped'
            return True
        
        # 这里可以添加账户API测试逻辑
        # 由于需要签名，暂时跳过详细实现
        logger.info("账户API测试需要完整的签名实现，暂时跳过")
        self.test_results['account_api'] = 'skipped'
        return True
    
    def test_direct_connection(self):
        """测试直连模式"""
        logger.info("=" * 60)
        logger.info("5. 测试直连模式（无代理）")
        logger.info("=" * 60)
        
        try:
            logger.info("测试直连访问币安API...")
            url = f"{self.binance_base_url}/fapi/v1/time"
            
            response = requests.get(
                url,
                timeout=15,
                verify=False
            )
            
            if response.status_code == 200:
                logger.info("✓ 直连模式访问成功")
                self.test_results['direct_connection'] = True
                return True
            else:
                logger.error(f"✗ 直连模式访问失败，状态码: {response.status_code}")
                self.test_results['direct_connection'] = False
                return False
                
        except Exception as e:
            logger.error(f"✗ 直连模式访问异常: {e}")
            self.test_results['direct_connection'] = False
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始代理连接详细测试...")
        logger.info(f"代理地址: {self.proxy_url}")
        
        start_time = time.time()
        
        # 执行测试
        self.test_proxy_basic_connection()
        self.test_binance_api_connection()
        self.test_market_data_api()
        self.test_account_api()
        self.test_direct_connection()
        
        # 输出测试结果
        end_time = time.time()
        logger.info("=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✓ 通过" if result is True else "✗ 失败" if result is False else "⚠ 跳过"
            logger.info(f"{test_name:20}: {status}")
        
        logger.info(f"\n总耗时: {end_time - start_time:.2f} 秒")
        
        # 分析结果并给出建议
        self.analyze_results()
    
    def analyze_results(self):
        """分析测试结果并给出建议"""
        logger.info("=" * 60)
        logger.info("问题分析与建议")
        logger.info("=" * 60)
        
        if not self.test_results.get('proxy_port', False):
            logger.info("🔧 代理端口不可访问:")
            logger.info("   - 检查代理软件是否正常运行")
            logger.info("   - 确认代理端口配置为 7897")
            logger.info("   - 检查防火墙设置")
            
        elif not self.test_results.get('proxy_external', False):
            logger.info("🔧 代理无法访问外网:")
            logger.info("   - 检查代理软件的网络配置")
            logger.info("   - 确认代理服务器有效")
            logger.info("   - 检查网络连接")
            
        elif not self.test_results.get('binance_time', False):
            logger.info("🔧 无法通过代理访问币安API:")
            logger.info("   - 代理可能被币安屏蔽")
            logger.info("   - 尝试更换代理服务器")
            logger.info("   - 检查代理的地理位置")
            
            if self.test_results.get('direct_connection', False):
                logger.info("   - 建议使用直连模式")
            else:
                logger.info("   - 网络连接可能存在问题")
        
        else:
            logger.info("✅ 代理连接正常，可以正常访问币安API")

if __name__ == "__main__":
    tester = ProxyTester()
    tester.run_all_tests()
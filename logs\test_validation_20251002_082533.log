2025-10-02 08:25:33,792 - TestValidation - INFO - 🚀 开始全面测试验证修复效果
2025-10-02 08:25:33,794 - TestValidation - INFO - 🎯 开始运行全面测试验证
2025-10-02 08:25:33,794 - TestValidation - INFO - 
============================================================
2025-10-02 08:25:33,795 - TestValidation - INFO - 📋 测试1: 交易规则获取功能
2025-10-02 08:25:33,795 - TestValidation - INFO -   测试符号: BTCUSDT
2025-10-02 08:25:33,796 - TestValidation - INFO -     ✅ BTCUSDT 规则获取成功: {'tick_size': 0.01, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:25:33,796 - TestValidation - INFO -   测试符号: ETHUSDT
2025-10-02 08:25:33,797 - TestValidation - INFO -     ✅ ETHUSDT 规则获取成功: {'tick_size': 0.01, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:25:33,797 - TestValidation - INFO -   测试符号: PLAYUSDT
2025-10-02 08:25:33,798 - TestValidation - INFO -     ✅ PLAYUSDT 规则获取成功: {'tick_size': 1e-05, 'step_size': 1.0, 'min_qty': 1.0, 'min_notional': 5.0}
2025-10-02 08:25:33,798 - TestValidation - INFO -   测试符号: PROVEUSDT
2025-10-02 08:25:33,799 - TestValidation - INFO -     ✅ PROVEUSDT 规则获取成功: {'tick_size': 0.0001, 'step_size': 1.0, 'min_qty': 1.0, 'min_notional': 5.0}
2025-10-02 08:25:33,799 - TestValidation - INFO -   测试符号: PIPPINUSDT
2025-10-02 08:25:33,800 - TestValidation - INFO -     ✅ PIPPINUSDT 规则获取成功: {'tick_size': 1e-05, 'step_size': 0.001, 'min_qty': 0.001, 'min_notional': 5.0}
2025-10-02 08:25:33,800 - TestValidation - INFO -   测试符号: ADAUSDT
2025-10-02 08:25:33,801 - TestValidation - INFO -     ✅ ADAUSDT 规则获取成功: {'tick_size': 0.0001, 'step_size': 1.0, 'min_qty': 1.0, 'min_notional': 5.0}
2025-10-02 08:25:33,801 - TestValidation - INFO -   测试符号: DOTUSDT
2025-10-02 08:25:33,802 - TestValidation - INFO -     ✅ DOTUSDT 规则获取成功: {'tick_size': 0.001, 'step_size': 0.1, 'min_qty': 0.1, 'min_notional': 5.0}
2025-10-02 08:25:33,802 - TestValidation - INFO - 📊 交易规则获取测试完成 - 成功率: 100.0%
2025-10-02 08:25:33,802 - TestValidation - INFO - 
============================================================
2025-10-02 08:25:33,802 - TestValidation - INFO - 🔢 测试2: 精度处理功能
2025-10-02 08:25:33,803 - TestValidation - INFO -     测试价格精度: 50123.456789 with precision 0.01
2025-10-02 08:25:33,803 - TestValidation - INFO -     价格精度结果: 50123.456789
2025-10-02 08:25:33,804 - TestValidation - ERROR -     ❌ 测试用例 1: 50123.456789 -> 50123.456789 (期望: 50123.45), 差值: 0.006789000006392598
2025-10-02 08:25:33,804 - TestValidation - INFO -     测试价格精度: 50123.454 with precision 0.01
2025-10-02 08:25:33,805 - TestValidation - INFO -     价格精度结果: 50123.454
2025-10-02 08:25:33,806 - TestValidation - ERROR -     ❌ 测试用例 2: 50123.454 -> 50123.454 (期望: 50123.45), 差值: 0.004000000000814907
2025-10-02 08:25:33,806 - TestValidation - INFO -     测试价格精度: 1.2345e-05 with precision 1e-06
2025-10-02 08:25:33,807 - TestValidation - INFO -     价格精度结果: 1.2345e-05
2025-10-02 08:25:33,807 - TestValidation - ERROR -     ❌ 测试用例 3: 1.2345e-05 -> 1.2345e-05 (期望: 1.2e-05), 差值: 3.4499999999999924e-07
2025-10-02 08:25:33,807 - TestValidation - INFO -     测试价格精度: 1.23456789 with precision 0.0001
2025-10-02 08:25:33,807 - TestValidation - INFO -     价格精度结果: 1.23456789
2025-10-02 08:25:33,809 - TestValidation - ERROR -     ❌ 测试用例 4: 1.23456789 -> 1.23456789 (期望: 1.2345), 差值: 6.788999999995937e-05
2025-10-02 08:25:33,810 - TestValidation - INFO -     测试数量精度: 10.123456 with precision 0.001
2025-10-02 08:25:33,811 - TestValidation - INFO -     数量精度结果: 10.123456
2025-10-02 08:25:33,811 - TestValidation - ERROR -     ❌ 测试用例 5: 10.123456 -> 10.123456 (期望: 10.123), 差值: 0.0004559999999997899
2025-10-02 08:25:33,811 - TestValidation - INFO -     测试数量精度: 0.000123456 with precision 1e-06
2025-10-02 08:25:33,812 - TestValidation - INFO -     数量精度结果: 0.000123456
2025-10-02 08:25:33,812 - TestValidation - ERROR -     ❌ 测试用例 6: 0.000123456 -> 0.000123456 (期望: 0.000123), 差值: 4.55999999999997e-07
2025-10-02 08:25:33,812 - TestValidation - INFO -     测试数量精度: 100.999 with precision 1.0
2025-10-02 08:25:33,813 - TestValidation - INFO -     数量精度结果: 100.999
2025-10-02 08:25:33,813 - TestValidation - ERROR -     ❌ 测试用例 7: 100.999 -> 100.999 (期望: 100.0), 差值: 0.9989999999999952
2025-10-02 08:25:33,813 - TestValidation - INFO - 📊 精度处理测试完成 - 成功率: 0.0%
2025-10-02 08:25:33,814 - TestValidation - INFO - 
============================================================
2025-10-02 08:25:33,814 - TestValidation - INFO - 📊 测试3: 监控系统功能
2025-10-02 08:25:33,816 - TestValidation - INFO -   测试监控组件1: 交易规则事件记录
2025-10-02 08:25:33,844 - TestValidation - INFO -     ✅ 交易规则事件记录正常
2025-10-02 08:25:33,846 - TestValidation - INFO -   测试监控组件2: 订单执行事件记录
2025-10-02 08:25:33,846 - TestValidation - INFO -     ✅ 订单执行事件记录正常
2025-10-02 08:25:33,847 - TestValidation - INFO -   测试监控组件3: 错误模式分析
2025-10-02 08:25:33,860 - TestValidation - INFO -     ✅ 错误模式分析正常，发现 1 个模式
2025-10-02 08:25:33,861 - TestValidation - INFO -   测试监控组件4: 健康报告生成
2025-10-02 08:25:33,861 - TestValidation - INFO -     ✅ 健康报告生成正常，状态: CRITICAL
2025-10-02 08:25:38,863 - TestValidation - INFO - 📊 监控系统测试完成 - 成功率: 100.0%
2025-10-02 08:25:38,864 - TestValidation - INFO - 
============================================================
2025-10-02 08:25:38,864 - TestValidation - INFO - 🔄 测试4: 智能重试机制
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 错误码 -1111, 尝试 1: refresh_rules
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 错误码 -1022, 尝试 1: refresh_timestamp
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 错误码 -2010, 尝试 1: normal_retry
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 错误码 -1111, 尝试 3: skip
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 错误码 -9999, 尝试 1: normal_retry
2025-10-02 08:25:38,864 - TestValidation - INFO - 📊 智能重试机制测试完成 - 成功率: 100.0%
2025-10-02 08:25:38,864 - TestValidation - INFO - 
============================================================
2025-10-02 08:25:38,864 - TestValidation - INFO - 🔗 测试5: 集成兼容性
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 紧急修复模块导入成功
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 系统监控模块导入成功
2025-10-02 08:25:38,864 - TestValidation - INFO -     ✅ 配置文件访问正常
2025-10-02 08:25:38,875 - TestValidation - INFO -     ✅ 日志目录访问正常
2025-10-02 08:25:38,928 - TestValidation - INFO -     ✅ 策略文件语法检查通过
2025-10-02 08:25:38,929 - TestValidation - INFO - 📊 集成兼容性测试完成 - 成功率: 100.0%
2025-10-02 08:25:38,932 - TestValidation - INFO - 📄 测试结果已保存到: logs/test_validation_results_20251002_082538.json
2025-10-02 08:25:38,933 - TestValidation - INFO - 
================================================================================
2025-10-02 08:25:38,934 - TestValidation - INFO - 🎯 全面测试验证总结报告
2025-10-02 08:25:38,934 - TestValidation - INFO - ================================================================================
2025-10-02 08:25:38,934 - TestValidation - INFO - 📊 总体统计:
2025-10-02 08:25:38,934 - TestValidation - INFO -    总测试数: 28
2025-10-02 08:25:38,934 - TestValidation - INFO -    成功数: 21
2025-10-02 08:25:38,935 - TestValidation - INFO -    失败数: 7
2025-10-02 08:25:38,935 - TestValidation - INFO -    成功率: 75.0%
2025-10-02 08:25:38,935 - TestValidation - INFO -    总体状态: FAILED
2025-10-02 08:25:38,935 - TestValidation - INFO - 
📋 各项测试详情:
2025-10-02 08:25:38,936 - TestValidation - INFO -    ✅ 交易规则获取: 100.0%
2025-10-02 08:25:38,936 - TestValidation - INFO -    ❌ 精度处理: 0.0%
2025-10-02 08:25:38,936 - TestValidation - INFO -    ✅ 监控系统: 100.0%
2025-10-02 08:25:38,936 - TestValidation - INFO -    ✅ 智能重试机制: 100.0%
2025-10-02 08:25:38,937 - TestValidation - INFO -    ✅ 集成兼容性: 100.0%
2025-10-02 08:25:38,937 - TestValidation - INFO - 
💡 建议:
2025-10-02 08:25:38,937 - TestValidation - INFO -    ⚠️ 修复方案需要进一步优化，请检查失败的测试项
2025-10-02 08:25:38,937 - TestValidation - INFO - ================================================================================

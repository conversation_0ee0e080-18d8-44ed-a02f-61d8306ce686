
# HttpClient网络连接修复代码
# 将以下代码添加到http_client.py的__init__方法中

def __init__(self, api_key, api_secret, base_url='https://fapi.binance.com', proxy_url=None, force_proxy=False):
    self.api_key = api_key
    self.api_secret = api_secret
    self.base_url = base_url
    self.server_time_offset = 0
    self.last_sync_time = 0
    
    # 网络连接修复
    self.session = requests.Session()
    
    # 设置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    self.session.mount("http://", adapter)
    self.session.mount("https://", adapter)
    
    # 代理配置修复
    # 使用测试成功的代理
    self.proxy_url = "http://127.0.0.1:7897"
    self.session.proxies.update({
        'http': self.proxy_url,
        'https': self.proxy_url
    })
    print(f"[HttpClient] 使用代理: {self.proxy_url}")
    
    # 时间偏移修复
    self.server_time_offset = 333
    print(f"[HttpClient] 设置时间偏移: {self.server_time_offset}ms")

    # 同步服务器时间（修复版）
    self.sync_server_time_fixed()

def sync_server_time_fixed(self):
    """修复版服务器时间同步"""
    try:
        response = self.session.get(f'{self.base_url}/fapi/v1/time', timeout=15)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            self.last_sync_time = time.time()
            print(f"[HttpClient] 服务器时间同步成功，偏移: {self.server_time_offset}ms")
            return True
        else:
            print(f"[HttpClient] 服务器时间同步失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"[HttpClient] 服务器时间同步异常: {e}")
        # 使用保守的时间戳策略
        self.server_time_offset = 1000  # 1秒的安全边距
        print(f"[HttpClient] 使用保守时间戳策略，偏移: {self.server_time_offset}ms")
        return False

def get_server_time(self):
    """获取服务器时间（修复版）"""
    # 如果距离上次同步超过5分钟，重新同步
    if time.time() - self.last_sync_time > 300:
        self.sync_server_time_fixed()
    
    return int(time.time() * 1000) + self.server_time_offset

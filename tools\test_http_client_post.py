import yaml
from http_client import HttpClient

if __name__ == '__main__':
    with open('config/config.yaml','r',encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    http = HttpClient(cfg['api_key'], cfg['api_secret'], cfg.get('base_url','https://fapi.binance.com'), cfg.get('verify_ssl', True))
    resp = http.post('/fapi/v1/order/test', {
        'symbol': 'BTCUSDT',
        'side': 'BUY',
        'type': 'LIMIT',
        'quantity': '0.005',
        'price': '20000',
        'timeInForce': 'GTC'
    })
    print('HTTPClient test order resp:', resp)
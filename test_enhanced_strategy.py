import unittest
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from strategy.maker_channel_enhanced import MakerChannelEnhanced, Tick3m

class MockHttpClient:
    """模拟HTTP客户端用于测试"""
    def __init__(self):
        self.calls = []
        
    def get(self, endpoint, params=None):
        self.calls.append(('GET', endpoint, params))
        
        if endpoint == '/fapi/v1/exchangeInfo':
            return {
                'symbols': [
                    {
                        'symbol': 'BTCUSDT',
                        'contractType': 'PERPETUAL',
                        'status': 'TRADING',
                        'onboardDate': int((datetime.now() - timedelta(days=30)).timestamp() * 1000),
                        'filters': [
                            {'filterType': 'PRICE_FILTER', 'tickSize': '0.01'},
                            {'filterType': 'LOT_SIZE', 'stepSize': '0.001', 'minQty': '0.001'},
                            {'filterType': 'MIN_NOTIONAL', 'notional': '5'}
                        ]
                    },
                    {
                        'symbol': 'ETHUSDT',
                        'contractType': 'PERPETUAL',
                        'status': 'TRADING',
                        'onboardDate': int((datetime.now() - timedelta(days=180)).timestamp() * 1000),
                        'filters': [
                            {'filterType': 'PRICE_FILTER', 'tickSize': '0.01'},
                            {'filterType': 'LOT_SIZE', 'stepSize': '0.001', 'minQty': '0.001'},
                            {'filterType': 'MIN_NOTIONAL', 'notional': '5'}
                        ]
                    }
                ]
            }
        elif endpoint == '/fapi/v1/ticker/24hr':
            if params and 'symbol' in params:
                symbol = params['symbol']
                return {'lastPrice': '50000.0' if symbol == 'BTCUSDT' else '3000.0'}
            else:
                return [
                    {'symbol': 'BTCUSDT', 'priceChangePercent': '5.0', 'quoteVolume': '100000000'},
                    {'symbol': 'ETHUSDT', 'priceChangePercent': '3.0', 'quoteVolume': '50000000'},
                    {'symbol': 'ADAUSDT', 'priceChangePercent': '8.0', 'quoteVolume': '20000000'}
                ]
        elif endpoint == '/fapi/v1/klines':
            symbol = params['symbol']
            interval = params['interval']
            limit = params['limit']
            
            # 生成模拟K线数据（包含上轨突破和回踩特征）
            base_price = 50000 if symbol == 'BTCUSDT' else 3000
            # 修正pandas频率：将3m映射为3min
            freq = '3min' if interval == '3m' else interval
            dates = pd.date_range(end=datetime.now(), periods=limit, freq=freq)
            
            data = []
            for i, date in enumerate(dates):
                open_price = base_price + i * 10
                # 构造最后两根形成突破迹象
                if i == limit - 2:
                    high_price = open_price + 200  # 突破
                else:
                    high_price = open_price + 50
                low_price = open_price - 30
                # 设置收盘：近20根中每5根回落，降低RSI；最后一根靠近上轨下方0.35%回踩
                if i == limit - 1:
                    prev_open = base_price + (limit - 2) * 10
                    prev_high = prev_open + 200
                    close_price = prev_high * 0.9968  # 稍高于PULLBACK_FACTOR=0.9962
                elif i >= limit - 20 and i % 5 == 0 and i != limit - 2:
                    close_price = open_price - 10  # 制造小幅下跌，降低RSI
                else:
                    close_price = open_price + 20
                # 最后一根量能显著放大以满足VOL_MULT
                volume = 5000 if i == limit - 1 else 1000 + i * 10
                
                data.append([
                    int(date.timestamp() * 1000),  # timestamp
                    str(open_price),              # open
                    str(high_price),              # high
                    str(low_price),               # low
                    str(close_price),             # close
                    str(volume),                  # volume
                    int(date.timestamp() * 1000), # close time
                    str(volume * 10),             # quote asset volume
                    100,                          # number of trades
                    str(volume * 100),            # taker buy base asset volume
                    str(volume * 1000),           # taker buy quote asset volume
                    '0'                           # ignore
                ])
            
            return data
        elif endpoint == '/fapi/v1/depth':
            return {
                'bids': [['49999.0', '1.0'], ['49998.0', '2.0']],
                'asks': [['50001.0', '1.0'], ['50002.0', '2.0']]
            }
        return None
    
    def post(self, endpoint, data):
        self.calls.append(('POST', endpoint, data))
        
        if endpoint == '/fapi/v1/order':
            return {'orderId': '123456', 'status': 'NEW'}
        return None
    
    def delete(self, endpoint, data):
        self.calls.append(('DELETE', endpoint, data))
        return {'orderId': data.get('orderId'), 'status': 'CANCELED'}

class MockOrderQueue:
    """模拟订单队列"""
    def __init__(self):
        self.operations = []
    
    def add_operation(self, operation):
        self.operations.append(operation)
        # 立即执行操作
        operation()

class TestEnhancedStrategy(unittest.TestCase):
    """增强版策略测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.http = MockHttpClient()
        self.config = {
            'max_rate': 15,
            'trade_rate': 10,
            'first_nominal': 100,
            'max_add': 3,
            'add_ratio': [1.5, 2.0, 999]
        }
        
        # 创建策略实例
        self.strategy = MakerChannelEnhanced(self.http, self.config)
        self.strategy.queue = MockOrderQueue()
    
    def test_tick3m_synthesis(self):
        """测试3分钟K线合成"""
        tick3m = Tick3m('BTCUSDT')
        
        # 添加多个tick
        base_time = int(datetime.now().timestamp() * 1000)
        bar1 = tick3m.add(50000, 1.0, base_time)
        bar2 = tick3m.add(50010, 0.5, base_time + 60000)  # 1分钟后
        bar3 = tick3m.add(50020, 0.8, base_time + 120000)  # 2分钟后
        bar4 = tick3m.add(50030, 1.2, base_time + 181000)  # 3分钟+1秒后，应该生成新bar
        
        self.assertIsNotNone(bar1)
        self.assertIsNotNone(bar4)
        # 检查新bar的初始化值（应该是新价格）
        self.assertEqual(bar4['o'], 50030)
        # 检查bar合成功能正常
        self.assertEqual(bar4['h'], 50030)
        self.assertEqual(bar4['l'], 50030)
        self.assertEqual(bar4['c'], 50030)
        self.assertEqual(bar4['v'], 1.2)
    
    def test_cache_management(self):
        """测试缓存管理"""
        # 测试缓存获取
        result = self.strategy.cache_get('test_key', lambda: 'test_data')
        self.assertEqual(result, 'test_data')
        
        # 测试缓存命中
        result2 = self.strategy.cache_get('test_key', lambda: 'new_data')
        self.assertEqual(result2, 'test_data')  # 应该返回缓存数据
    
    def test_symbol_scoring(self):
        """测试币种评分"""
        # 创建测试数据
        dates = pd.date_range(end=datetime.now(), periods=50, freq='15min')
        df = pd.DataFrame({
            't': [int(d.timestamp() * 1000) for d in dates],
            'o': np.linspace(50000, 51000, 50),
            'h': np.linspace(50100, 51100, 50),
            'l': np.linspace(49900, 50900, 50),
            'c': np.linspace(50050, 51050, 50),
            'v': np.linspace(1000, 2000, 50)
        })
        df['t'] = pd.to_datetime(df['t'], unit='ms')
        df = df.set_index('t')
        df.name = 'BTCUSDT'
        
        # 测试评分
        score_card = self.strategy.score_symbol(df, 30)
        self.assertIsNotNone(score_card)
        self.assertTrue(0 <= score_card.total_score <= 10)
        self.assertIn(score_card.M, [0, 1, 2])
    
    def test_market_leaders_selection(self):
        """测试龙头池选择"""
        leaders = self.strategy.get_top50_market_leaders()
        self.assertIsInstance(leaders, list)
        self.assertTrue(len(leaders) <= 50)
        self.assertIn('BTCUSDT', leaders)
        self.assertIn('ETHUSDT', leaders)
    
    def test_entry_condition_check(self):
        """测试入场条件检查"""
        # 模拟突破条件
        entry_price = self.strategy.check_entry('BTCUSDT')
        # 由于是模拟数据，可能返回None或有效价格
        self.assertIsNotNone(entry_price)
    
    def test_risk_management(self):
        """测试风险管理"""
        # 设置测试仓位
        self.strategy.pos = {
            'symbol': 'BTCUSDT',
            'entry': 50000,
            'qty': 0.002,
            'original_nominal': 100,
            'open_time': time.time()
        }
        
        # 测试保本止损
        self.strategy.monitor_position('BTCUSDT', 52500)  # 5%盈利
        self.assertEqual(self.strategy.pos.get('breakeven'), 1)
        
        # 测试移动止损
        self.strategy.monitor_position('BTCUSDT', 51000)  # 2%盈利
        if self.strategy.pos:  # 检查仓位是否还存在
            self.assertIsNotNone(self.strategy.pos.get('trail_stop'))
    
    def test_position_closure(self):
        """测试仓位平仓"""
        self.strategy.pos = {
            'symbol': 'BTCUSDT',
            'entry': 50000,
            'qty': 0.002,
            'original_nominal': 100
        }
        
        self.strategy.close_position('BTCUSDT', 51000)
        self.assertIsNone(self.strategy.pos)
    
    def test_daily_close(self):
        """测试每日强制平仓"""
        self.strategy.pos = {
            'symbol': 'BTCUSDT',
            'entry': 50000,
            'qty': 0.002,
            'original_nominal': 100
        }
        
        self.strategy.daily_close()
        self.assertIsNone(self.strategy.pos)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_full_trade_cycle(self):
        """测试完整交易周期"""
        http = MockHttpClient()
        config = {
            'max_rate': 15,
            'trade_rate': 10,
            'first_nominal': 100,
            'max_add': 3,
            'add_ratio': [1.5, 2.0, 999]
        }
        
        strategy = MakerChannelEnhanced(http, config)
        strategy.queue = MockOrderQueue()
        
        # 使用 run_once 驱动一次迭代
        res = strategy.run_once()
        self.assertIn(res['event'], ['idle', 'opened', 'daily_close'])
        
        # 如果有持仓，继续监控并平仓
        if strategy.pos:
            current_price = 51000
            strategy.monitor_position(strategy.pos['symbol'], current_price)
            strategy.close_position(strategy.pos['symbol'], current_price)
            self.assertIsNone(strategy.pos)

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestEnhancedStrategy))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试报告
    print(f"\n=== 测试报告 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"通过: {result.testsRun - len(result.failures) - len(result.errors)}")
    
    if result.failures:
        print(f"\n=== 失败详情 ===")
        for test, traceback in result.failures:
            print(f"失败: {test}")
            print(traceback)
    
    if result.errors:
        print(f"\n=== 错误详情 ===")
        for test, traceback in result.errors:
            print(f"错误: {test}")
            print(traceback)
    
    return len(result.failures) == 0 and len(result.errors) == 0

if __name__ == '__main__':
    import time
    print("开始运行增强版策略测试...")
    
    success = run_tests()
    
    if success:
        print("\n🎉 所有测试通过！增强版策略功能正常。")
        print("\n测试覆盖范围:")
        print("  ✅ 3分钟K线合成")
        print("  ✅ 四级缓存管理")
        print("  ✅ 增强评分系统")
        print("  ✅ 龙头池筛选")
        print("  ✅ 入场条件检查")
        print("  ✅ 风险管理机制")
        print("  ✅ 完整交易周期")
    else:
        print("\n❌ 部分测试失败，请检查代码逻辑。")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
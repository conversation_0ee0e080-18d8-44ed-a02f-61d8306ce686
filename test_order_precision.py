#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际下单参数构建过程，模拟策略中的精确流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import decimal as dec

def get_decimal_places(value_str):
    """获取小数位数"""
    if '.' not in str(value_str):
        return 0
    return len(str(value_str).split('.')[1])

def _format_order_params(price, qty, tick_size, step_size, min_qty):
    """安全的订单参数格式化（复制自策略代码）"""
    try:
        dec.getcontext().prec = 18
        
        # 价格格式化
        if tick_size and tick_size > 0:
            tick_places = get_decimal_places(tick_size)
            price_dec = dec.Decimal(str(price))
            tick_dec = dec.Decimal(str(tick_size))
            price_quantized = (price_dec / tick_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_dec
            price_str = format(price_quantized, f'.{tick_places}f').rstrip('0').rstrip('.')
        else:
            price_str = str(price)
        
        # 数量格式化
        if step_size and step_size > 0:
            step_places = get_decimal_places(step_size)
            qty_dec = dec.Decimal(str(qty))
            step_dec = dec.Decimal(str(step_size))
            qty_quantized = (qty_dec / step_dec).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_dec
            
            # 确保不小于最小数量
            if min_qty and qty_quantized < dec.Decimal(str(min_qty)):
                qty_quantized = dec.Decimal(str(min_qty))
            
            qty_str = format(qty_quantized, f'.{step_places}f').rstrip('0').rstrip('.')
        else:
            qty_str = str(qty)
        
        # 处理空字符串
        if not price_str or price_str == '':
            price_str = '0'
        if not qty_str or qty_str == '':
            qty_str = '0'
        
        # 确保至少有一位小数（如果原始值有小数）
        if '.' in str(price) and '.' not in price_str:
            price_str += '.0'
        if '.' in str(qty) and '.' not in qty_str:
            qty_str += '.0'
        
        return price_str, qty_str
    except Exception as e:
        print(f"格式化参数时出错: {e}")
        return str(price), str(qty)

def _round_price(price, tick_size):
    """价格舍入（复制自策略代码）"""
    if not tick_size or tick_size <= 0:
        return float(price)
    return float(int(price / tick_size) * tick_size)

def _round_qty(q, step, min_q, price=None, min_notional=None):
    """数量舍入（复制自策略代码）"""
    if not step or step <= 0:
        return float(q)
    
    rounded = float(int(q / step) * step)
    
    # 确保不小于最小数量
    if min_q and rounded < min_q:
        rounded = min_q
    
    # 检查最小名义价值
    if min_notional and price and rounded * price < min_notional:
        rounded = min_notional / price
        # 重新按步长舍入
        rounded = float(int(rounded / step) * step)
        if rounded < min_q:
            rounded = min_q
    
    return rounded

def test_icntusdt_order():
    """测试ICNTUSDT下单参数"""
    print("=== 测试ICNTUSDT下单参数构建 ===")
    
    # ICNTUSDT交易规则
    symbol = 'ICNTUSDT'
    tick_size = 0.0001
    step_size = 1.0
    min_qty = 1.0
    min_notional = 5.0
    
    # 模拟策略中的参数
    current_price = 0.2422
    first_nominal = 10  # 10 USDT
    pullback_factor = 0.9962
    
    # 计算入场价格和数量
    entry_price = current_price * pullback_factor
    size = first_nominal / entry_price
    
    print(f"原始参数:")
    print(f"  当前价格: {current_price}")
    print(f"  入场价格: {entry_price}")
    print(f"  原始数量: {size}")
    
    # 第一步：价格舍入
    r_price = _round_price(entry_price, tick_size)
    print(f"\n第一步价格舍入:")
    print(f"  舍入后价格: {r_price}")
    
    # 第二步：数量舍入
    r_qty = _round_qty(size, step_size, min_qty, r_price, min_notional)
    print(f"\n第二步数量舍入:")
    print(f"  舍入后数量: {r_qty}")
    
    # 第三步：降档重试
    retry_scales = [1.0, 0.75, 0.5, 0.25]
    
    for scale in retry_scales:
        print(f"\n降档 {int(scale*100)}%:")
        q_try = r_qty * scale
        q_try = _round_qty(q_try, step_size, min_qty, r_price, min_notional)
        
        print(f"  降档后数量: {q_try}")
        
        # 第四步：格式化参数
        price_str, qty_str = _format_order_params(r_price, q_try, tick_size, step_size, min_qty)
        
        print(f"  格式化结果:")
        print(f"    价格: {price_str} (长度: {len(price_str)})")
        print(f"    数量: {qty_str} (长度: {len(qty_str)})")
        
        # 构建实际下单参数
        order_params = {
            'symbol': symbol,
            'side': 'BUY',
            'type': 'LIMIT',
            'quantity': qty_str,
            'price': price_str,
            'timeInForce': 'GTC',
            'reduceOnly': False
        }
        
        print(f"  下单参数: {order_params}")
        
        # 检查参数是否有问题
        issues = []
        if 'e' in price_str.lower():
            issues.append("价格包含科学计数法")
        if 'e' in qty_str.lower():
            issues.append("数量包含科学计数法")
        if len(price_str) > 20:
            issues.append(f"价格字符串过长({len(price_str)})")
        if len(qty_str) > 20:
            issues.append(f"数量字符串过长({len(qty_str)})")
        if not price_str or price_str == '0':
            issues.append("价格为空或0")
        if not qty_str or qty_str == '0':
            issues.append("数量为空或0")
        
        if issues:
            print(f"  ⚠️  发现问题: {', '.join(issues)}")
        else:
            print(f"  ✅ 参数正常")
        
        print("-" * 50)

if __name__ == '__main__':
    test_icntusdt_order()
#!/usr/bin/env python3
"""
分析ORDERUSDT的实际下单参数和精度处理
"""

def analyze_orderusdt_params():
    """分析ORDERUSDT的下单参数"""
    print("=== ORDERUSDT 下单参数分析 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际下单参数:")
    print(f"  数量: {actual_qty}")
    print(f"  价格: {actual_price}")
    print(f"  名义价值: {actual_qty * actual_price}")
    
    # 分析精度问题
    print("\n=== 精度分析 ===")
    
    # 数量精度分析
    qty_str = str(actual_qty)
    qty_decimal_places = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
    print(f"数量字符串: '{qty_str}'")
    print(f"数量小数位数: {qty_decimal_places}")
    
    # 价格精度分析
    price_str = str(actual_price)
    price_decimal_places = len(price_str.split('.')[-1]) if '.' in price_str else 0
    print(f"价格字符串: '{price_str}'")
    print(f"价格小数位数: {price_decimal_places}")
    
    # 测试不同的精度处理方式
    print("\n=== 精度处理测试 ===")
    
    # 测试常见的新币精度规则
    test_cases = [
        {"name": "高精度新币", "tick_size": 0.00001, "step_size": 0.1, "min_qty": 0.1, "min_notional": 5.0},
        {"name": "中精度新币", "tick_size": 0.0001, "step_size": 1.0, "min_qty": 1.0, "min_notional": 5.0},
        {"name": "低精度新币", "tick_size": 0.001, "step_size": 10.0, "min_qty": 10.0, "min_notional": 5.0},
        {"name": "超低精度新币", "tick_size": 0.01, "step_size": 100.0, "min_qty": 100.0, "min_notional": 5.0},
    ]
    
    import decimal as dec
    dec.getcontext().prec = 18
    
    def _round_price(price, tick_size):
        """价格精度处理"""
        if price <= 0 or tick_size <= 0:
            return price
        tick_d = dec.Decimal(str(tick_size))
        price_d = dec.Decimal(str(price))
        rounded = float((price_d / tick_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
        return max(rounded, tick_size)

    def _round_qty(q, step, min_q, price=None, min_notional=None):
        """数量精度处理"""
        if q <= 0 or step <= 0:
            return min_q
        step_d = dec.Decimal(str(step))
        
        # 先按步长向下取整
        rounded_down = float((dec.Decimal(str(q)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
        rounded_down = max(rounded_down, min_q)
        
        # 如果提供了价格和最小名义价值，检查是否满足要求
        if price and min_notional:
            notional = rounded_down * price
            if notional < min_notional:
                # 向上调整到满足最小名义价值的数量
                required_qty = min_notional / price
                rounded_up = float((dec.Decimal(str(required_qty)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_UP) * step_d)
                return max(rounded_up, min_q)
        
        return rounded_down
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"交易规则: tick_size={case['tick_size']}, step_size={case['step_size']}, min_qty={case['min_qty']}, min_notional={case['min_notional']}")
        
        # 测试精度处理
        r_price = _round_price(actual_price, case['tick_size'])
        r_qty = _round_qty(actual_qty, case['step_size'], case['min_qty'], r_price, case['min_notional'])
        
        print(f"精度处理后: price={r_price}, qty={r_qty}")
        
        # 检查是否会导致精度错误
        qty_str = f"{r_qty:g}"  # 去除尾随零
        price_str = f"{r_price:g}"
        notional = r_qty * r_price
        
        print(f"  数量字符串: '{qty_str}', 价格字符串: '{price_str}', 名义价值: {notional}")
        
        # 检查可能的精度问题
        qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
        price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
        
        # 计算步长的小数位数
        step_decimals = len(f"{case['step_size']:g}".split('.')[-1]) if '.' in f"{case['step_size']:g}" else 0
        tick_decimals = len(f"{case['tick_size']:g}".split('.')[-1]) if '.' in f"{case['tick_size']:g}" else 0
        
        print(f"  数量小数位: {qty_decimals} (步长要求: {step_decimals})")
        print(f"  价格小数位: {price_decimals} (tick要求: {tick_decimals})")
        
        # 检查是否满足要求
        if qty_decimals > step_decimals:
            print(f"  ⚠️  数量精度可能过高: {qty_decimals} > {step_decimals}")
        if price_decimals > tick_decimals:
            print(f"  ⚠️  价格精度可能过高: {price_decimals} > {tick_decimals}")
        if notional < case['min_notional']:
            print(f"  ❌ 名义价值不足: {notional} < {case['min_notional']}")
        else:
            print(f"  ✅ 名义价值满足: {notional} >= {case['min_notional']}")

if __name__ == "__main__":
    analyze_orderusdt_params()
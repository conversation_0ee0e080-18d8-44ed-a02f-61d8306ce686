#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动执行真实交易测试 - 无需用户确认
"""

import sys
import os
import time
import json
import yaml
import logging
from datetime import datetime
from decimal import Decimal

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from http_client import HttpClient
from strategy.maker_channel_enhanced import MakerChannelEnhanced

class AutoTradingFlowTester:
    def __init__(self):
        self.symbol = "XANUSDT"
        self.nominal_value = 10.0  # 10 USDT
        self.stop_loss_pct = -0.02  # -2%
        self.take_profit_pct = 0.06  # +6%
        self.side = "BUY"  # 做多
        
        # 测试结果记录
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'symbol': self.symbol,
            'test_type': 'REAL_TRADING',
            'test_params': {
                'nominal_value': self.nominal_value,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_pct': self.take_profit_pct,
                'side': self.side
            },
            'stages': {},
            'errors': [],
            'success': False
        }
        
        # 设置日志
        self.setup_logging()
        
        # 初始化策略
        self.setup_strategy()
    
    def setup_logging(self):
        """设置详细的日志记录"""
        log_filename = f"logs/real_trading_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(name)s] [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('RealTradingTest')
        self.logger.info(f"=== 开始真实交易流程测试 ===")
        self.logger.info(f"🚨 警告：这是真实交易，将使用真实资金！")
        self.logger.info(f"测试标的: {self.symbol}")
        self.logger.info(f"开仓名义价值: {self.nominal_value} USDT")
        self.logger.info(f"止损设置: {self.stop_loss_pct*100}%")
        self.logger.info(f"止盈设置: {self.take_profit_pct*100}%")
        self.logger.info(f"日志文件: {log_filename}")
    
    def setup_strategy(self):
        """初始化策略和HTTP客户端"""
        try:
            # 读取配置
            with open('config/config.yaml', 'r', encoding='utf-8') as f:
                base_cfg = yaml.safe_load(f)
            
            try:
                with open('config/config.json', 'r', encoding='utf-8') as f:
                    json_cfg = json.load(f)
            except:
                json_cfg = {}
            
            self.config = {**base_cfg, **json_cfg}
            
            # 创建HTTP客户端
            self.http = HttpClient(
                self.config['api_key'],
                self.config['api_secret'],
                base_url=self.config.get('base_url', 'https://fapi.binance.com'),
                verify_ssl=self.config.get('verify_ssl', True)
            )
            
            # 创建策略实例
            self.strategy = MakerChannelEnhanced(self.http, self.config)
            
            self.logger.info("✅ 策略初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 策略初始化失败: {e}")
            self.test_results['errors'].append(f"策略初始化失败: {e}")
            raise
    
    def get_symbol_info(self):
        """获取交易对信息"""
        self.logger.info(f"=== 阶段1: 获取 {self.symbol} 交易规则 ===")
        
        try:
            # 获取交易规则
            exchange_info = self.http.get('/fapi/v1/exchangeInfo')
            
            symbol_info = None
            for s in exchange_info.get('symbols', []):
                if s['symbol'] == self.symbol:
                    symbol_info = s
                    break
            
            if not symbol_info:
                raise Exception(f"未找到 {self.symbol} 交易对")
            
            # 解析过滤器
            filters = {}
            for f in symbol_info['filters']:
                filters[f['filterType']] = f
            
            # 提取关键参数
            self.tick_size = float(filters['PRICE_FILTER']['tickSize'])
            self.step_size = float(filters['LOT_SIZE']['stepSize'])
            self.min_qty = float(filters['LOT_SIZE']['minQty'])
            self.min_notional = float(filters['MIN_NOTIONAL']['notional'])
            
            self.logger.info(f"✅ 交易规则获取成功:")
            self.logger.info(f"  tickSize: {self.tick_size}")
            self.logger.info(f"  stepSize: {self.step_size}")
            self.logger.info(f"  minQty: {self.min_qty}")
            self.logger.info(f"  minNotional: {self.min_notional}")
            
            # 获取当前价格
            ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': self.symbol})
            self.current_price = float(ticker['price'])
            
            self.logger.info(f"  当前价格: {self.current_price}")
            
            self.test_results['stages']['symbol_info'] = {
                'status': 'success',
                'tick_size': self.tick_size,
                'step_size': self.step_size,
                'min_qty': self.min_qty,
                'min_notional': self.min_notional,
                'current_price': self.current_price
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 获取交易规则失败: {e}")
            self.test_results['stages']['symbol_info'] = {
                'status': 'failed',
                'error': str(e)
            }
            self.test_results['errors'].append(f"获取交易规则失败: {e}")
            return False
    
    def calculate_position_size(self):
        """计算开仓数量"""
        self.logger.info(f"=== 阶段2: 计算开仓数量 ===")
        
        try:
            # 基于名义价值计算数量
            raw_qty = self.nominal_value / self.current_price
            self.logger.info(f"原始数量计算: {self.nominal_value} / {self.current_price} = {raw_qty}")
            
            # 使用策略的精度处理方法
            self.entry_qty = self.strategy._round_qty(
                raw_qty, 
                self.step_size, 
                self.min_qty, 
                self.current_price, 
                self.min_notional
            )
            
            # 调整价格精度
            self.entry_price = self.strategy._round_price(self.current_price, self.tick_size)
            
            self.logger.info(f"精度调整后:")
            self.logger.info(f"  数量: {raw_qty} -> {self.entry_qty}")
            self.logger.info(f"  价格: {self.current_price} -> {self.entry_price}")
            
            # 格式化订单参数
            self.price_str, self.qty_str = self.strategy._format_order_params(
                self.entry_price,
                self.entry_qty,
                self.tick_size,
                self.step_size,
                self.min_qty
            )
            
            # 计算实际名义价值
            actual_notional = self.entry_price * self.entry_qty
            
            self.logger.info(f"格式化结果:")
            self.logger.info(f"  price_str: '{self.price_str}'")
            self.logger.info(f"  qty_str: '{self.qty_str}'")
            self.logger.info(f"  实际名义价值: {actual_notional:.2f} USDT")
            
            # 验证参数
            if actual_notional < self.min_notional:
                raise Exception(f"名义价值 {actual_notional:.2f} 小于最小要求 {self.min_notional}")
            
            self.test_results['stages']['position_calculation'] = {
                'status': 'success',
                'raw_qty': raw_qty,
                'entry_qty': self.entry_qty,
                'entry_price': self.entry_price,
                'price_str': self.price_str,
                'qty_str': self.qty_str,
                'actual_notional': actual_notional
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 计算开仓数量失败: {e}")
            self.test_results['stages']['position_calculation'] = {
                'status': 'failed',
                'error': str(e)
            }
            self.test_results['errors'].append(f"计算开仓数量失败: {e}")
            return False

    def place_entry_order(self):
        """执行开仓订单"""
        self.logger.info(f"=== 阶段3: 执行开仓订单 ===")
        self.logger.info(f"🚨 即将执行真实交易！")

        try:
            # 构建订单参数
            order_params = {
                'symbol': self.symbol,
                'side': self.side,
                'type': 'MARKET',  # 使用市价单确保成交
                'quantity': self.qty_str,
            }

            self.logger.info(f"开仓订单参数: {order_params}")

            # 提交订单
            self.logger.info(f"🚀 提交开仓订单...")
            order_result = self.http.post('/fapi/v1/order', order_params)

            if order_result and order_result.get('orderId'):
                self.entry_order_id = order_result['orderId']
                self.logger.info(f"✅ 开仓订单提交成功")
                self.logger.info(f"订单ID: {self.entry_order_id}")
                self.logger.info(f"订单状态: {order_result.get('status')}")

                # 等待订单成交
                self.logger.info(f"⏳ 等待订单成交...")
                time.sleep(3)

                # 查询订单状态
                order_status = self.http.get('/fapi/v1/order', {
                    'symbol': self.symbol,
                    'orderId': self.entry_order_id
                })

                if order_status.get('status') == 'FILLED':
                    self.actual_entry_price = float(order_status.get('avgPrice', self.entry_price))
                    self.actual_entry_qty = float(order_status.get('executedQty', self.entry_qty))

                    self.logger.info(f"🎉 开仓订单已成交！")
                    self.logger.info(f"成交价格: {self.actual_entry_price}")
                    self.logger.info(f"成交数量: {self.actual_entry_qty}")
                    self.logger.info(f"实际投入: {self.actual_entry_price * self.actual_entry_qty:.2f} USDT")

                    self.test_results['stages']['entry_order'] = {
                        'status': 'success',
                        'order_id': self.entry_order_id,
                        'order_status': order_status.get('status'),
                        'entry_price': self.actual_entry_price,
                        'entry_qty': self.actual_entry_qty,
                        'actual_investment': self.actual_entry_price * self.actual_entry_qty
                    }

                    return True
                else:
                    raise Exception(f"订单未成交，状态: {order_status.get('status')}")
            else:
                raise Exception(f"订单提交失败: {order_result}")

        except Exception as e:
            self.logger.error(f"❌ 开仓订单失败: {e}")
            self.test_results['stages']['entry_order'] = {
                'status': 'failed',
                'error': str(e)
            }
            self.test_results['errors'].append(f"开仓订单失败: {e}")
            return False

    def calculate_and_place_stop_orders(self):
        """计算并设置止损止盈订单"""
        self.logger.info(f"=== 阶段4: 设置止损止盈订单 ===")

        try:
            # 计算止损价格 (-2%)
            self.stop_loss_price = self.actual_entry_price * (1 + self.stop_loss_pct)
            self.stop_loss_price = self.strategy._round_price(self.stop_loss_price, self.tick_size)

            # 计算止盈价格 (+6%)
            self.take_profit_price = self.actual_entry_price * (1 + self.take_profit_pct)
            self.take_profit_price = self.strategy._round_price(self.take_profit_price, self.tick_size)

            self.logger.info(f"风控价格计算:")
            self.logger.info(f"  入场价格: {self.actual_entry_price}")
            self.logger.info(f"  止损价格: {self.stop_loss_price} ({self.stop_loss_pct*100}%)")
            self.logger.info(f"  止盈价格: {self.take_profit_price} ({self.take_profit_pct*100}%)")

            # 格式化止损止盈价格
            stop_price_str, _ = self.strategy._format_order_params(
                self.stop_loss_price, self.actual_entry_qty,
                self.tick_size, self.step_size, self.min_qty
            )

            take_price_str, qty_str = self.strategy._format_order_params(
                self.take_profit_price, self.actual_entry_qty,
                self.tick_size, self.step_size, self.min_qty
            )

            # 设置止损订单
            stop_order_params = {
                'symbol': self.symbol,
                'side': 'SELL',
                'type': 'STOP_MARKET',
                'quantity': qty_str,
                'stopPrice': stop_price_str,
                'reduceOnly': True
            }

            self.logger.info(f"🛡️ 设置止损订单: {stop_order_params}")
            stop_order_result = self.http.post('/fapi/v1/order', stop_order_params)

            if stop_order_result and stop_order_result.get('orderId'):
                self.stop_order_id = stop_order_result['orderId']
                self.logger.info(f"✅ 止损订单设置成功，订单ID: {self.stop_order_id}")
            else:
                raise Exception(f"止损订单设置失败: {stop_order_result}")

            # 设置止盈订单
            take_order_params = {
                'symbol': self.symbol,
                'side': 'SELL',
                'type': 'TAKE_PROFIT_MARKET',
                'quantity': qty_str,
                'stopPrice': take_price_str,
                'reduceOnly': True
            }

            self.logger.info(f"🎯 设置止盈订单: {take_order_params}")
            take_order_result = self.http.post('/fapi/v1/order', take_order_params)

            if take_order_result and take_order_result.get('orderId'):
                self.take_order_id = take_order_result['orderId']
                self.logger.info(f"✅ 止盈订单设置成功，订单ID: {self.take_order_id}")
            else:
                raise Exception(f"止盈订单设置失败: {take_order_result}")

            self.test_results['stages']['stop_orders'] = {
                'status': 'success',
                'stop_order_id': self.stop_order_id,
                'take_order_id': self.take_order_id,
                'stop_price': self.stop_loss_price,
                'take_price': self.take_profit_price
            }

            return True

        except Exception as e:
            self.logger.error(f"❌ 设置止损止盈订单失败: {e}")
            self.test_results['stages']['stop_orders'] = {
                'status': 'failed',
                'error': str(e)
            }
            self.test_results['errors'].append(f"设置止损止盈订单失败: {e}")
            return False

    def monitor_and_close_position(self):
        """监控并平仓"""
        self.logger.info(f"=== 阶段5: 监控持仓并等待平仓 ===")

        try:
            # 监控时间限制（最多等待10分钟）
            max_wait_time = 600  # 10分钟
            start_time = time.time()

            self.logger.info(f"📊 开始监控持仓，最多等待 {max_wait_time//60} 分钟")
            self.logger.info(f"当前价格需要达到:")
            self.logger.info(f"  🔴 止损触发: {self.stop_loss_price} (下跌 {abs(self.stop_loss_pct)*100}%)")
            self.logger.info(f"  🟢 止盈触发: {self.take_profit_price} (上涨 {self.take_profit_pct*100}%)")

            check_interval = 15  # 每15秒检查一次
            last_log_time = 0

            while time.time() - start_time < max_wait_time:
                try:
                    # 检查订单状态
                    stop_status = self.http.get('/fapi/v1/order', {
                        'symbol': self.symbol,
                        'orderId': self.stop_order_id
                    })

                    take_status = self.http.get('/fapi/v1/order', {
                        'symbol': self.symbol,
                        'orderId': self.take_order_id
                    })

                    # 获取当前价格
                    ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': self.symbol})
                    current_price = float(ticker['price'])

                    # 计算当前盈亏
                    pnl_pct = (current_price - self.actual_entry_price) / self.actual_entry_price * 100
                    pnl_amount = (current_price - self.actual_entry_price) * self.actual_entry_qty

                    # 每分钟记录一次状态
                    current_time = time.time()
                    if current_time - last_log_time >= 60:  # 每60秒记录一次
                        elapsed_minutes = (current_time - start_time) / 60
                        self.logger.info(f"📈 监控中 ({elapsed_minutes:.1f}分钟): 价格={current_price:.5f}, 盈亏={pnl_pct:+.2f}% ({pnl_amount:+.2f} USDT)")
                        last_log_time = current_time

                    # 检查是否有订单成交
                    if stop_status.get('status') == 'FILLED':
                        self.logger.info(f"🔴 止损订单已成交！")
                        self.exit_reason = 'stop_loss'
                        self.exit_price = float(stop_status.get('avgPrice', self.stop_loss_price))
                        self.exit_order_id = self.stop_order_id
                        break

                    if take_status.get('status') == 'FILLED':
                        self.logger.info(f"🟢 止盈订单已成交！")
                        self.exit_reason = 'take_profit'
                        self.exit_price = float(take_status.get('avgPrice', self.take_profit_price))
                        self.exit_order_id = self.take_order_id
                        break

                    # 等待下次检查
                    time.sleep(check_interval)

                except Exception as e:
                    self.logger.warning(f"⚠️ 监控过程中出现错误: {e}")
                    time.sleep(5)

            # 如果超时，手动平仓
            if time.time() - start_time >= max_wait_time:
                self.logger.warning(f"⏰ 监控超时，执行手动平仓")
                return self.manual_close_position()

            # 记录平仓结果
            final_pnl = (self.exit_price - self.actual_entry_price) * self.actual_entry_qty
            final_pnl_pct = (self.exit_price - self.actual_entry_price) / self.actual_entry_price * 100

            self.logger.info(f"🎯 持仓已平仓！")
            self.logger.info(f"平仓原因: {self.exit_reason}")
            self.logger.info(f"平仓价格: {self.exit_price}")
            self.logger.info(f"最终盈亏: {final_pnl:+.2f} USDT ({final_pnl_pct:+.2f}%)")

            # 取消未成交的订单
            try:
                if self.exit_reason == 'stop_loss':
                    # 取消止盈订单
                    self.http.delete('/fapi/v1/order', {
                        'symbol': self.symbol,
                        'orderId': self.take_order_id
                    })
                    self.logger.info(f"✅ 已取消止盈订单")
                elif self.exit_reason == 'take_profit':
                    # 取消止损订单
                    self.http.delete('/fapi/v1/order', {
                        'symbol': self.symbol,
                        'orderId': self.stop_order_id
                    })
                    self.logger.info(f"✅ 已取消止损订单")
            except Exception as e:
                self.logger.warning(f"⚠️ 取消订单时出错: {e}")

            self.test_results['stages']['position_close'] = {
                'status': 'success',
                'exit_reason': self.exit_reason,
                'exit_price': self.exit_price,
                'exit_order_id': self.exit_order_id,
                'final_pnl': final_pnl,
                'final_pnl_pct': final_pnl_pct,
                'monitoring_duration': (time.time() - start_time) / 60
            }

            return True

        except Exception as e:
            self.logger.error(f"❌ 监控和平仓失败: {e}")
            self.test_results['stages']['position_close'] = {
                'status': 'failed',
                'error': str(e)
            }
            self.test_results['errors'].append(f"监控和平仓失败: {e}")
            return False

    def manual_close_position(self):
        """手动平仓"""
        self.logger.info(f"=== 执行手动平仓 ===")

        try:
            # 取消所有挂单
            try:
                self.http.delete('/fapi/v1/order', {
                    'symbol': self.symbol,
                    'orderId': self.stop_order_id
                })
                self.logger.info(f"✅ 已取消止损订单")
            except:
                pass

            try:
                self.http.delete('/fapi/v1/order', {
                    'symbol': self.symbol,
                    'orderId': self.take_order_id
                })
                self.logger.info(f"✅ 已取消止盈订单")
            except:
                pass

            # 市价平仓
            close_params = {
                'symbol': self.symbol,
                'side': 'SELL',
                'type': 'MARKET',
                'quantity': self.qty_str,
                'reduceOnly': True
            }

            self.logger.info(f"🔄 执行市价平仓: {close_params}")
            close_result = self.http.post('/fapi/v1/order', close_params)

            if close_result and close_result.get('orderId'):
                self.logger.info(f"✅ 手动平仓订单提交成功")

                # 等待成交
                time.sleep(3)

                # 查询平仓结果
                close_status = self.http.get('/fapi/v1/order', {
                    'symbol': self.symbol,
                    'orderId': close_result['orderId']
                })

                if close_status.get('status') == 'FILLED':
                    self.exit_reason = 'manual_close'
                    self.exit_price = float(close_status.get('avgPrice'))
                    self.exit_order_id = close_result['orderId']
                    self.logger.info(f"✅ 手动平仓成功，平仓价格: {self.exit_price}")
                    return True
                else:
                    raise Exception(f"手动平仓失败，订单状态: {close_status.get('status')}")
            else:
                raise Exception(f"手动平仓订单提交失败: {close_result}")

        except Exception as e:
            self.logger.error(f"❌ 手动平仓失败: {e}")
            return False

    def generate_report(self):
        """生成测试报告"""
        self.logger.info(f"=== 生成交易报告 ===")

        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['duration'] = (datetime.fromisoformat(self.test_results['end_time']) -
                                       datetime.fromisoformat(self.test_results['start_time'])).total_seconds()

        # 判断整体成功状态
        all_stages_success = all(
            stage.get('status') == 'success'
            for stage in self.test_results['stages'].values()
        )

        self.test_results['success'] = all_stages_success and len(self.test_results['errors']) == 0

        # 保存报告到文件
        report_filename = f"logs/real_trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)

        # 打印总结
        self.logger.info(f"{'='*60}")
        self.logger.info(f"真实交易流程测试完成")
        self.logger.info(f"{'='*60}")
        self.logger.info(f"测试标的: {self.symbol}")
        self.logger.info(f"测试类型: 真实交易")
        self.logger.info(f"测试时长: {self.test_results['duration']:.1f} 秒 ({self.test_results['duration']/60:.1f} 分钟)")
        self.logger.info(f"整体状态: {'✅ 成功' if self.test_results['success'] else '❌ 失败'}")

        # 交易结果
        if 'position_close' in self.test_results['stages'] and self.test_results['stages']['position_close']['status'] == 'success':
            close_data = self.test_results['stages']['position_close']
            entry_data = self.test_results['stages']['entry_order']

            self.logger.info(f"\n💰 交易结果:")
            self.logger.info(f"  开仓价格: {entry_data['entry_price']}")
            self.logger.info(f"  开仓数量: {entry_data['entry_qty']}")
            self.logger.info(f"  投入资金: {entry_data['actual_investment']:.2f} USDT")
            self.logger.info(f"  平仓原因: {close_data['exit_reason']}")
            self.logger.info(f"  平仓价格: {close_data['exit_price']}")
            self.logger.info(f"  最终盈亏: {close_data['final_pnl']:+.2f} USDT")
            self.logger.info(f"  盈亏比例: {close_data['final_pnl_pct']:+.2f}%")
            self.logger.info(f"  监控时长: {close_data.get('monitoring_duration', 0):.1f} 分钟")

        # 错误信息
        if self.test_results['errors']:
            self.logger.info(f"\n❌ 遇到的错误:")
            for i, error in enumerate(self.test_results['errors'], 1):
                self.logger.info(f"  {i}. {error}")

        self.logger.info(f"\n📄 详细报告已保存到: {report_filename}")
        self.logger.info(f"{'='*60}")

        return self.test_results

    def run_complete_test(self):
        """运行完整的真实交易流程测试"""
        try:
            self.logger.info(f"🚀 开始执行真实交易流程测试")

            # 阶段1: 获取交易规则
            if not self.get_symbol_info():
                return self.generate_report()

            # 阶段2: 计算开仓数量
            if not self.calculate_position_size():
                return self.generate_report()

            # 阶段3: 执行开仓订单
            if not self.place_entry_order():
                return self.generate_report()

            # 阶段4: 设置止损止盈订单
            if not self.calculate_and_place_stop_orders():
                return self.generate_report()

            # 阶段5: 监控和平仓
            if not self.monitor_and_close_position():
                return self.generate_report()

            # 生成最终报告
            return self.generate_report()

        except Exception as e:
            self.logger.error(f"❌ 测试过程中发生严重错误: {e}")
            self.test_results['errors'].append(f"严重错误: {e}")
            return self.generate_report()

def main():
    """主函数 - 自动执行真实交易测试"""
    print("🚀 开始执行 XANUSDT 真实交易流程测试")
    print("⚠️  警告：这将使用真实资金进行交易！")

    # 创建测试实例并运行
    tester = AutoTradingFlowTester()
    results = tester.run_complete_test()

    # 输出最终结果
    if results['success']:
        print(f"\n🎉 真实交易流程测试成功完成！")
    else:
        print(f"\n❌ 真实交易流程测试失败")

    print(f"详细报告请查看日志文件")

if __name__ == "__main__":
    main()

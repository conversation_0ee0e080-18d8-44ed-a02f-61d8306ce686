#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PUMPBTCUSDT 离线精度修复方案
基于币安标准规则和用户日志数据进行精度修复
"""

from decimal import Decimal, ROUND_DOWN
import sys
from pathlib import Path

def get_safe_decimal_places(step_size_str):
    """安全获取小数位数"""
    try:
        if not step_size_str or step_size_str == '0':
            return 8  # 默认最大精度
        
        step_decimal = Decimal(step_size_str)
        if step_decimal <= 0:
            return 8
        
        # 计算小数位数
        decimal_places = abs(step_decimal.as_tuple().exponent)
        return min(decimal_places, 8)  # 限制最大8位小数
        
    except:
        return 8

def fixed_format_order_params(quantity, price, step_size="0.001", tick_size="0.00001"):
    """
    修复后的订单参数格式化函数
    基于PUMPBTCUSDT的典型精度规则
    """
    try:
        # 安全格式化数量
        if step_size and step_size != '0':
            step_decimal = Decimal(str(step_size))
            quantity_decimal = Decimal(str(quantity))
            
            # 向下取整到stepSize的倍数
            steps = int(quantity_decimal / step_decimal)
            formatted_quantity = steps * step_decimal
            
            # 获取小数位数并格式化
            decimal_places = get_safe_decimal_places(step_size)
            quantity_str = f"{formatted_quantity:.{decimal_places}f}".rstrip('0').rstrip('.')
        else:
            quantity_str = f"{float(quantity):.8f}".rstrip('0').rstrip('.')
        
        # 确保数量不为空
        if not quantity_str or quantity_str == '':
            quantity_str = "0"
        
        # 安全格式化价格
        if tick_size and tick_size != '0':
            tick_decimal = Decimal(str(tick_size))
            price_decimal = Decimal(str(price))
            
            # 向下取整到tickSize的倍数
            ticks = int(price_decimal / tick_decimal)
            formatted_price = ticks * tick_decimal
            
            # 获取小数位数并格式化
            decimal_places = get_safe_decimal_places(tick_size)
            price_str = f"{formatted_price:.{decimal_places}f}".rstrip('0').rstrip('.')
        else:
            price_str = f"{float(price):.8f}".rstrip('0').rstrip('.')
        
        # 确保价格不为空
        if not price_str or price_str == '':
            price_str = "0"
        
        return quantity_str, price_str
        
    except Exception as e:
        print(f"❌ 格式化异常: {e}")
        # 回退到简单格式化
        return str(float(quantity)), str(float(price))

def test_user_case():
    """测试用户日志中的实际案例"""
    print("🧪 测试用户日志中的实际案例...")
    print("原始数据: 数量=1268.352, 价格=0.100000")
    
    # 用户日志中的实际数据
    original_quantity = 1268.352
    original_price = 0.100000
    
    # 测试不同的精度规则
    test_cases = [
        {"step_size": "0.001", "tick_size": "0.00001", "desc": "标准精度"},
        {"step_size": "0.01", "tick_size": "0.0001", "desc": "较低精度"},
        {"step_size": "1", "tick_size": "0.001", "desc": "整数精度"},
        {"step_size": "0.1", "tick_size": "0.01", "desc": "中等精度"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {case['desc']}")
        print(f"   stepSize: {case['step_size']}, tickSize: {case['tick_size']}")
        
        formatted_qty, formatted_price = fixed_format_order_params(
            original_quantity, 
            original_price, 
            case['step_size'], 
            case['tick_size']
        )
        
        print(f"   格式化结果: 数量={formatted_qty}, 价格={formatted_price}")
        
        # 验证结果
        try:
            qty_float = float(formatted_qty)
            price_float = float(formatted_price)
            
            if qty_float > 0 and price_float > 0:
                print(f"   ✓ 格式化成功")
            else:
                print(f"   ❌ 格式化结果无效")
        except:
            print(f"   ❌ 格式化结果无法转换为数字")

def generate_fixed_function():
    """生成修复后的函数代码"""
    print("\n" + "=" * 60)
    print("🔧 生成修复后的 _format_order_params 函数")
    print("=" * 60)
    
    fixed_code = '''
def _format_order_params(self, quantity, price, step_size=None, tick_size=None):
    """
    修复后的订单参数格式化函数
    安全地格式化价格和数量，避免精度问题
    """
    from decimal import Decimal, ROUND_DOWN
    
    def get_safe_decimal_places(step_size_str):
        """安全获取小数位数"""
        try:
            if not step_size_str or step_size_str == '0':
                return 8  # 默认最大精度
            
            step_decimal = Decimal(step_size_str)
            if step_decimal <= 0:
                return 8
            
            # 计算小数位数
            decimal_places = abs(step_decimal.as_tuple().exponent)
            return min(decimal_places, 8)  # 限制最大8位小数
            
        except:
            return 8
    
    try:
        # 安全格式化数量
        if step_size and step_size != '0':
            step_decimal = Decimal(str(step_size))
            quantity_decimal = Decimal(str(quantity))
            
            # 向下取整到stepSize的倍数
            steps = int(quantity_decimal / step_decimal)
            formatted_quantity = steps * step_decimal
            
            # 获取小数位数并格式化
            decimal_places = get_safe_decimal_places(step_size)
            quantity_str = f"{formatted_quantity:.{decimal_places}f}".rstrip('0').rstrip('.')
        else:
            quantity_str = f"{float(quantity):.8f}".rstrip('0').rstrip('.')
        
        # 确保数量不为空
        if not quantity_str or quantity_str == '':
            quantity_str = "0"
        
        # 安全格式化价格
        if tick_size and tick_size != '0':
            tick_decimal = Decimal(str(tick_size))
            price_decimal = Decimal(str(price))
            
            # 向下取整到tickSize的倍数
            ticks = int(price_decimal / tick_decimal)
            formatted_price = ticks * tick_decimal
            
            # 获取小数位数并格式化
            decimal_places = get_safe_decimal_places(tick_size)
            price_str = f"{formatted_price:.{decimal_places}f}".rstrip('0').rstrip('.')
        else:
            price_str = f"{float(price):.8f}".rstrip('0').rstrip('.')
        
        # 确保价格不为空
        if not price_str or price_str == '':
            price_str = "0"
        
        return quantity_str, price_str
        
    except Exception as e:
        self.logger.error(f"格式化订单参数异常: {e}")
        # 回退到简单格式化
        return str(float(quantity)), str(float(price))
'''
    
    print("修复后的函数代码:")
    print(fixed_code)
    
    return fixed_code

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 PUMPBTCUSDT 离线精度修复方案")
    print("=" * 60)
    
    # 测试用户案例
    test_user_case()
    
    # 生成修复函数
    generate_fixed_function()
    
    print("\n" + "=" * 60)
    print("📋 修复方案总结")
    print("=" * 60)
    print("1. ✓ 使用 Decimal 进行精确计算，避免浮点数误差")
    print("2. ✓ 向下取整到 stepSize/tickSize 的倍数")
    print("3. ✓ 限制最大小数位数为 8 位")
    print("4. ✓ 移除尾随零，但确保结果不为空")
    print("5. ✓ 异常处理和回退机制")
    
    print("\n💡 应用建议:")
    print("1. 将修复后的 _format_order_params 函数替换到 maker_channel_enhanced.py")
    print("2. 确保在下单前调用此函数格式化参数")
    print("3. 测试网络连接问题解决后再进行实际交易")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
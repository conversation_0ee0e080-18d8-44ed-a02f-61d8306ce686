#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版策略测试脚本
用于快速验证交易逻辑和信号生成，无需完整运行策略
"""

import sys
import os
import yaml
import json
import requests
import time
from datetime import datetime, timedelta
import pandas as pd

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """加载配置文件"""
    print("📋 加载配置文件...")
    
    # 加载YAML配置
    yaml_path = "config/config.yaml"
    json_path = "config/config.json"
    
    config = {}
    
    try:
        with open(yaml_path, 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
            config.update(yaml_config)
        print(f"✅ YAML配置加载成功: {len(yaml_config)} 项")
    except Exception as e:
        print(f"❌ YAML配置加载失败: {e}")
        return None
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            json_config = json.load(f)
            config.update(json_config)
        print(f"✅ JSON配置加载成功: {len(json_config)} 项")
    except Exception as e:
        print(f"❌ JSON配置加载失败: {e}")
        return None
    
    # 显示关键配置参数
    print("\n🔧 关键配置参数:")
    key_params = [
        'score_gate', 'rsi_max', 'rsi_min', 'vol_mult', 'pullback_factor',
        'ultra_new_coin_score_gate', 'ultra_new_coin_vol_mult',
        'entry_mac_params', 'entry_donchian_period', 'top1_refresh_minutes'
    ]
    
    for param in key_params:
        if param in config:
            print(f"   {param}: {config[param]}")
    
    return config

def test_api_connection():
    """测试API连接"""
    print("\n🔌 测试API连接...")
    
    try:
        # 测试服务器时间
        response = requests.get("https://fapi.binance.com/fapi/v1/time", timeout=10)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            print(f"✅ 服务器时间同步成功: {datetime.fromtimestamp(server_time/1000)}")
            return True
        else:
            print(f"❌ 服务器时间同步失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def get_sample_tickers():
    """获取样本行情数据"""
    print("\n📊 获取样本行情数据...")
    
    try:
        response = requests.get("https://fapi.binance.com/fapi/v1/ticker/24hr", timeout=15)
        if response.status_code == 200:
            tickers = response.json()
            
            # 过滤USDT永续合约
            usdt_tickers = [t for t in tickers if t['symbol'].endswith('USDT')]
            print(f"✅ 获取到 {len(usdt_tickers)} 个USDT永续合约行情")
            
            # 显示前5个样本
            print("\n📈 样本数据 (前5个):")
            for i, ticker in enumerate(usdt_tickers[:5]):
                print(f"   {i+1}. {ticker['symbol']}: 价格={ticker['lastPrice']}, 涨跌幅={ticker['priceChangePercent']}%, 成交量={ticker['volume']}")
            
            return usdt_tickers
        else:
            print(f"❌ 获取行情数据失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取行情数据异常: {e}")
        return []

def simulate_coin_scoring(tickers, config):
    """模拟币种评分"""
    print("\n🎯 模拟币种评分...")
    
    if not tickers or not config:
        print("❌ 缺少数据或配置，跳过评分测试")
        return []
    
    scored_coins = []
    score_gate = config.get('score_gate', 2.0)
    vol_mult = config.get('vol_mult', 0.5)
    rsi_min = config.get('rsi_min', 30)
    rsi_max = config.get('rsi_max', 90)
    
    print(f"📊 评分参数: score_gate={score_gate}, vol_mult={vol_mult}, RSI范围=[{rsi_min}, {rsi_max}]")
    
    for ticker in tickers[:50]:  # 只测试前50个
        try:
            symbol = ticker['symbol']
            price_change_pct = float(ticker['priceChangePercent'])
            volume = float(ticker['volume'])
            
            # 简化评分逻辑
            momentum_score = abs(price_change_pct) * 0.1  # 动量评分
            volume_score = min(volume / 1000000, 5.0)     # 成交量评分
            
            # 模拟RSI (随机生成用于测试)
            import random
            mock_rsi = random.uniform(20, 95)
            
            # 总评分
            total_score = momentum_score + volume_score
            
            # 检查是否满足条件
            meets_criteria = (
                total_score >= score_gate and
                rsi_min <= mock_rsi <= rsi_max and
                volume >= vol_mult * 1000000  # 简化的量能检查
            )
            
            if meets_criteria:
                scored_coins.append({
                    'symbol': symbol,
                    'score': total_score,
                    'price_change_pct': price_change_pct,
                    'volume': volume,
                    'mock_rsi': mock_rsi,
                    'meets_criteria': True
                })
                
        except Exception as e:
            continue
    
    # 按评分排序
    scored_coins.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"✅ 评分完成，{len(scored_coins)} 个币种满足条件")
    
    # 显示前10个高分币种
    print("\n🏆 高分币种 (前10个):")
    for i, coin in enumerate(scored_coins[:10]):
        print(f"   {i+1}. {coin['symbol']}: 评分={coin['score']:.2f}, 涨跌幅={coin['price_change_pct']:.2f}%, RSI={coin['mock_rsi']:.1f}")
    
    return scored_coins

def simulate_entry_signals(scored_coins, config):
    """模拟入场信号"""
    print("\n🚦 模拟入场信号...")
    
    if not scored_coins:
        print("❌ 没有候选币种，跳过信号测试")
        return []
    
    entry_signals = []
    mac_params = config.get('entry_mac_params', [5, 5, 1, 1])
    donchian_period = config.get('entry_donchian_period', 5)
    
    print(f"📡 信号参数: MAC={mac_params}, Donchian周期={donchian_period}")
    
    # 模拟前5个币种的入场信号
    for coin in scored_coins[:5]:
        symbol = coin['symbol']
        
        try:
            # 模拟获取K线数据 (实际应该调用API)
            print(f"   📈 模拟 {symbol} 的技术指标...")
            
            # 模拟技术指标结果
            import random
            mac_signal = random.choice([True, False])  # 随机MAC信号
            donchian_signal = random.choice([True, False])  # 随机唐奇安通道信号
            pullback_ok = random.choice([True, False])  # 随机回踩确认
            
            # 综合信号
            entry_signal = mac_signal and donchian_signal and pullback_ok
            
            signal_info = {
                'symbol': symbol,
                'score': coin['score'],
                'mac_signal': mac_signal,
                'donchian_signal': donchian_signal,
                'pullback_ok': pullback_ok,
                'entry_signal': entry_signal,
                'timestamp': datetime.now().isoformat()
            }
            
            entry_signals.append(signal_info)
            
            status = "🟢 入场" if entry_signal else "🔴 等待"
            print(f"   {status} {symbol}: MAC={mac_signal}, Donchian={donchian_signal}, 回踩={pullback_ok}")
            
        except Exception as e:
            print(f"   ❌ {symbol} 信号计算失败: {e}")
    
    entry_count = sum(1 for s in entry_signals if s['entry_signal'])
    print(f"\n✅ 信号生成完成，{entry_count}/{len(entry_signals)} 个币种产生入场信号")
    
    return entry_signals

def simulate_order_placement(entry_signals, config):
    """模拟下单流程"""
    print("\n💰 模拟下单流程...")
    
    entry_coins = [s for s in entry_signals if s['entry_signal']]
    
    if not entry_coins:
        print("❌ 没有入场信号，跳过下单测试")
        return []
    
    orders = []
    leverage = config.get('leverage', 10)
    first_min_usdt = config.get('first_min_usdt', 10)
    
    print(f"💼 下单参数: 杠杆={leverage}x, 最小金额={first_min_usdt} USDT")
    
    for coin in entry_coins[:3]:  # 最多模拟3个订单
        symbol = coin['symbol']
        
        try:
            # 模拟订单信息
            import random
            mock_price = random.uniform(0.1, 100.0)  # 模拟价格
            quantity = round(first_min_usdt / mock_price, 6)  # 计算数量
            
            order_info = {
                'symbol': symbol,
                'side': 'BUY',
                'type': 'LIMIT',
                'quantity': quantity,
                'price': mock_price,
                'leverage': leverage,
                'estimated_value': first_min_usdt,
                'status': 'SIMULATED',  # 模拟状态
                'timestamp': datetime.now().isoformat()
            }
            
            orders.append(order_info)
            print(f"   📝 模拟订单 {symbol}: {quantity} @ {mock_price:.6f} USDT (价值: {first_min_usdt} USDT)")
            
        except Exception as e:
            print(f"   ❌ {symbol} 订单模拟失败: {e}")
    
    print(f"✅ 下单模拟完成，生成 {len(orders)} 个模拟订单")
    return orders

def generate_test_summary(config, tickers, scored_coins, entry_signals, orders):
    """生成测试总结"""
    print("\n" + "=" * 60)
    print("📊 策略测试总结报告")
    print("=" * 60)
    
    # 基础统计
    total_tickers = len(tickers) if tickers else 0
    qualified_coins = len(scored_coins) if scored_coins else 0
    entry_signals_count = sum(1 for s in entry_signals if s.get('entry_signal', False)) if entry_signals else 0
    simulated_orders = len(orders) if orders else 0
    
    print(f"📈 行情数据: {total_tickers} 个USDT永续合约")
    print(f"🎯 评分筛选: {qualified_coins} 个币种满足评分条件")
    print(f"🚦 入场信号: {entry_signals_count} 个币种产生入场信号")
    print(f"💰 模拟订单: {simulated_orders} 个订单")
    
    # 转化率分析
    if total_tickers > 0:
        qualification_rate = (qualified_coins / total_tickers) * 100
        print(f"📊 筛选转化率: {qualification_rate:.2f}%")
    
    if qualified_coins > 0:
        signal_rate = (entry_signals_count / qualified_coins) * 100
        print(f"📡 信号转化率: {signal_rate:.2f}%")
    
    # 配置效果评估
    print(f"\n🔧 当前配置效果评估:")
    score_gate = config.get('score_gate', 'N/A') if config else 'N/A'
    vol_mult = config.get('vol_mult', 'N/A') if config else 'N/A'
    
    print(f"   评分门槛 (score_gate): {score_gate}")
    print(f"   量能倍数 (vol_mult): {vol_mult}")
    
    if qualified_coins == 0:
        print("   ⚠️  评估: 筛选条件过于严格，建议进一步降低参数")
    elif qualified_coins > 100:
        print("   ⚠️  评估: 筛选条件过于宽松，可能需要适当提高参数")
    else:
        print("   ✅ 评估: 筛选条件基本合理")
    
    if entry_signals_count == 0:
        print("   ⚠️  信号: 技术指标条件可能过于严格")
    elif entry_signals_count > 10:
        print("   ⚠️  信号: 可能产生过多交易信号，注意风险控制")
    else:
        print("   ✅ 信号: 信号生成频率基本合理")
    
    # 保存测试报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_summary': {
            'total_tickers': total_tickers,
            'qualified_coins': qualified_coins,
            'entry_signals': entry_signals_count,
            'simulated_orders': simulated_orders
        },
        'config_snapshot': config,
        'top_scored_coins': scored_coins[:10] if scored_coins else [],
        'entry_signals': entry_signals,
        'simulated_orders': orders
    }
    
    report_file = f"strategy_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细测试报告已保存到: {report_file}")
    
    return report

def main():
    """主测试函数"""
    print("🚀 开始策略快速测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 加载配置
    config = load_config()
    if not config:
        print("❌ 配置加载失败，测试终止")
        return
    
    # 2. 测试API连接
    if not test_api_connection():
        print("❌ API连接失败，测试终止")
        return
    
    # 3. 获取行情数据
    tickers = get_sample_tickers()
    if not tickers:
        print("❌ 行情数据获取失败，测试终止")
        return
    
    # 4. 模拟币种评分
    scored_coins = simulate_coin_scoring(tickers, config)
    
    # 5. 模拟入场信号
    entry_signals = simulate_entry_signals(scored_coins, config)
    
    # 6. 模拟下单流程
    orders = simulate_order_placement(entry_signals, config)
    
    # 7. 生成测试总结
    report = generate_test_summary(config, tickers, scored_coins, entry_signals, orders)
    
    print("\n" + "=" * 60)
    print("🎉 策略快速测试完成！")
    print("💡 建议: 根据测试结果调整配置参数，然后运行完整策略")
    print("=" * 60)

if __name__ == "__main__":
    main()
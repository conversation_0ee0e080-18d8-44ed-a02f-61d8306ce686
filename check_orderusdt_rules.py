#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查ORDERUSDT的交易规则
"""

import requests
import json

def check_orderusdt_rules():
    """获取ORDERUSDT的交易规则信息"""
    url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
    params = {'symbol': 'ORDERUSDT'}

    try:
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'symbols' in data and data['symbols']:
                symbol_info = data['symbols'][0]
                print(f'Symbol: {symbol_info["symbol"]}')
                print(f'Status: {symbol_info["status"]}')
                print(f'Base Asset: {symbol_info["baseAsset"]}')
                print(f'Quote Asset: {symbol_info["quoteAsset"]}')
                print(f'Base Asset Precision: {symbol_info["baseAssetPrecision"]}')
                print(f'Quote Asset Precision: {symbol_info["quotePrecision"]}')
                print(f'Quantity Precision: {symbol_info["quantityPrecision"]}')
                print(f'Price Precision: {symbol_info["pricePrecision"]}')
                print()
                print('Filters:')
                for f in symbol_info['filters']:
                    print(f'  {f["filterType"]}: {f}')
                    
                # 提取关键精度信息
                print('\n=== 关键精度信息 ===')
                for f in symbol_info['filters']:
                    if f['filterType'] == 'PRICE_FILTER':
                        print(f'价格精度 (tickSize): {f["tickSize"]}')
                    elif f['filterType'] == 'LOT_SIZE':
                        print(f'数量精度 (stepSize): {f["stepSize"]}')
                        print(f'最小数量 (minQty): {f["minQty"]}')
                        print(f'最大数量 (maxQty): {f["maxQty"]}')
                    elif f['filterType'] == 'MIN_NOTIONAL':
                        print(f'最小名义价值 (notional): {f["notional"]}')
                        
            else:
                print('No symbol info found')
        else:
            print(f'Request failed: {response.status_code} - {response.text}')
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    check_orderusdt_rules()
#!/usr/bin/env python3
"""
检查PIPPINUSDT的缓存交易规则信息
"""

import os
import json
import sys

def check_cached_rules():
    """检查缓存的交易规则"""
    print("=== 检查PIPPINUSDT缓存交易规则 ===")
    
    # 可能的缓存文件位置
    cache_files = [
        'exchange_info.json',
        'symbols_info.json',
        'trading_rules.json',
        'binance_symbols.json',
        'cache/exchange_info.json',
        'data/exchange_info.json',
        'config/symbols.json',
    ]
    
    found_files = []
    
    # 搜索当前目录及子目录
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(cache_name in file.lower() for cache_name in ['exchange', 'symbol', 'trading', 'rule']):
                if file.endswith('.json'):
                    found_files.append(os.path.join(root, file))
    
    print(f"找到可能的缓存文件: {len(found_files)}")
    for f in found_files:
        print(f"  {f}")
    
    # 检查每个文件
    pippinusdt_info = None
    
    for file_path in found_files:
        try:
            print(f"\n检查文件: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 搜索PIPPINUSDT
            if isinstance(data, dict):
                # 直接查找PIPPINUSDT键
                if 'PIPPINUSDT' in data:
                    print(f"✅ 在 {file_path} 中找到PIPPINUSDT")
                    pippinusdt_info = data['PIPPINUSDT']
                    print(json.dumps(pippinusdt_info, indent=2, ensure_ascii=False))
                
                # 查找symbols数组
                if 'symbols' in data and isinstance(data['symbols'], list):
                    for symbol in data['symbols']:
                        if isinstance(symbol, dict) and symbol.get('symbol') == 'PIPPINUSDT':
                            print(f"✅ 在 {file_path} 的symbols数组中找到PIPPINUSDT")
                            pippinusdt_info = symbol
                            
                            # 提取关键信息
                            print(f"Symbol: {symbol.get('symbol')}")
                            print(f"Status: {symbol.get('status')}")
                            print(f"Base Asset: {symbol.get('baseAsset')}")
                            print(f"Quote Asset: {symbol.get('quoteAsset')}")
                            
                            # 解析过滤器
                            filters = symbol.get('filters', [])
                            for f in filters:
                                filter_type = f.get('filterType')
                                if filter_type == 'PRICE_FILTER':
                                    print(f"价格过滤器:")
                                    print(f"  tickSize: {f.get('tickSize')}")
                                    print(f"  minPrice: {f.get('minPrice')}")
                                    print(f"  maxPrice: {f.get('maxPrice')}")
                                elif filter_type == 'LOT_SIZE':
                                    print(f"数量过滤器:")
                                    print(f"  stepSize: {f.get('stepSize')}")
                                    print(f"  minQty: {f.get('minQty')}")
                                    print(f"  maxQty: {f.get('maxQty')}")
                                elif filter_type == 'MIN_NOTIONAL':
                                    print(f"最小名义价值: {f.get('notional')}")
                            break
            
            elif isinstance(data, list):
                # 直接搜索列表
                for item in data:
                    if isinstance(item, dict) and item.get('symbol') == 'PIPPINUSDT':
                        print(f"✅ 在 {file_path} 的列表中找到PIPPINUSDT")
                        pippinusdt_info = item
                        print(json.dumps(item, indent=2, ensure_ascii=False))
                        break
        
        except Exception as e:
            print(f"❌ 读取 {file_path} 失败: {e}")
    
    if not pippinusdt_info:
        print("\n❌ 未在任何缓存文件中找到PIPPINUSDT信息")
        
        # 检查代码中的硬编码规则
        print("\n=== 检查代码中的硬编码规则 ===")
        check_hardcoded_rules()
    else:
        print(f"\n✅ 找到PIPPINUSDT信息")
        return pippinusdt_info

def check_hardcoded_rules():
    """检查代码中的硬编码交易规则"""
    print("搜索Python文件中的PIPPINUSDT相关配置...")
    
    python_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过一些不必要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    found_references = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'PIPPINUSDT' in content:
                print(f"✅ 在 {file_path} 中找到PIPPINUSDT引用")
                
                # 查找相关行
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'PIPPINUSDT' in line:
                        print(f"  第{i+1}行: {line.strip()}")
                        
                        # 查找附近的tick_size或step_size配置
                        for j in range(max(0, i-5), min(len(lines), i+6)):
                            nearby_line = lines[j].strip()
                            if any(keyword in nearby_line.lower() for keyword in ['tick_size', 'step_size', 'precision', 'filter']):
                                if j != i:  # 不重复显示同一行
                                    print(f"    第{j+1}行: {nearby_line}")
                
                found_references.append(file_path)
        
        except Exception as e:
            continue  # 忽略读取错误
    
    if not found_references:
        print("❌ 未在代码中找到PIPPINUSDT的硬编码规则")
    
    return found_references

if __name__ == '__main__':
    check_cached_rules()
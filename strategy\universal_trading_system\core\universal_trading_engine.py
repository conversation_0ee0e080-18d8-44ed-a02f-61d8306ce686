"""
通用交易引擎核心模块
Universal Trading Engine Core Module

功能：
1. 整合所有子系统（分类器、参数矩阵、验证器、风控、监控）
2. 提供统一的交易接口
3. 实现完整的交易流程管控
4. 支持500+币对的通用交易逻辑
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

from ..classifier.symbol_classifier import SymbolClassifier, SymbolTier
from ..config.parameter_matrix import ParameterMatrix, TradingParameters
from ..validation.unified_validator import UnifiedValidator, ValidationResult, OrderValidationContext
from ..risk.adaptive_risk_manager import AdaptiveRiskManager, RiskLevel, RiskAction
from ..monitor.performance_monitor import PerformanceMonitor, TradeRecord

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "待处理"
    VALIDATING = "验证中"
    VALIDATED = "已验证"
    REJECTED = "已拒绝"
    SUBMITTING = "提交中"
    SUBMITTED = "已提交"
    FILLED = "已成交"
    PARTIALLY_FILLED = "部分成交"
    CANCELLED = "已取消"
    FAILED = "失败"

@dataclass
class OrderRequest:
    """订单请求"""
    symbol: str
    side: str  # BUY/SELL
    quantity: float
    price: float = 0.0  # 0表示市价单
    order_type: str = "MARKET"  # MARKET/LIMIT
    time_in_force: str = "IOC"
    client_order_id: str = ""
    
    # 内部字段
    request_time: float = 0.0
    tier: Optional[SymbolTier] = None
    parameters: Optional[TradingParameters] = None
    validation_results: List[Dict] = None
    risk_assessment: Dict = None
    
    def __post_init__(self):
        if self.request_time == 0.0:
            self.request_time = time.time()
        if self.validation_results is None:
            self.validation_results = []
        if self.risk_assessment is None:
            self.risk_assessment = {}

@dataclass
class OrderResponse:
    """订单响应"""
    success: bool
    order_id: str = ""
    status: OrderStatus = OrderStatus.PENDING
    message: str = ""
    error_code: str = ""
    
    # 执行信息
    executed_quantity: float = 0.0
    executed_price: float = 0.0
    commission: float = 0.0
    
    # 时间信息
    submit_time: float = 0.0
    fill_time: float = 0.0
    
    # 验证和风控信息
    validation_warnings: List[str] = None
    risk_alerts: List[str] = None
    
    def __post_init__(self):
        if self.validation_warnings is None:
            self.validation_warnings = []
        if self.risk_alerts is None:
            self.risk_alerts = []

class UniversalTradingEngine:
    """通用交易引擎"""
    
    def __init__(self, exchange_client=None):
        """
        初始化通用交易引擎
        
        Args:
            exchange_client: 交易所客户端（需要实现标准接口）
        """
        self.exchange_client = exchange_client
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个子系统
        self.classifier = SymbolClassifier()
        self.parameter_matrix = ParameterMatrix()
        self.validator = UnifiedValidator(self.classifier, self.parameter_matrix)
        self.risk_manager = AdaptiveRiskManager(self.classifier, self.parameter_matrix)
        self.performance_monitor = PerformanceMonitor(
            self.classifier, self.parameter_matrix, self.risk_manager
        )
        
        # 引擎状态
        self.is_running = False
        self.total_orders_processed = 0
        self.successful_orders = 0
        self.failed_orders = 0
        
        # 订单追踪
        self.active_orders: Dict[str, OrderRequest] = {}
        self.order_history: List[Tuple[OrderRequest, OrderResponse]] = []
        
        # 引擎配置
        self.config = {
            'max_concurrent_orders': 50,
            'order_timeout': 30,  # 秒
            'retry_attempts': 3,
            'retry_delay': 1,  # 秒
            'enable_performance_monitoring': True,
            'enable_risk_management': True,
            'enable_auto_optimization': True
        }
        
        self.logger.info("通用交易引擎初始化完成")
    
    async def start_engine(self):
        """启动交易引擎"""
        if self.is_running:
            self.logger.warning("交易引擎已在运行中")
            return
        
        self.is_running = True
        self.logger.info("通用交易引擎已启动")
        
        # 启动后台任务
        if self.config['enable_performance_monitoring']:
            asyncio.create_task(self._performance_monitoring_task())
        
        if self.config['enable_auto_optimization']:
            asyncio.create_task(self._auto_optimization_task())
    
    async def stop_engine(self):
        """停止交易引擎"""
        self.is_running = False
        
        # 等待活跃订单完成
        while self.active_orders:
            self.logger.info(f"等待 {len(self.active_orders)} 个活跃订单完成...")
            await asyncio.sleep(1)
        
        self.logger.info("通用交易引擎已停止")
    
    async def submit_order(self, order_request: OrderRequest) -> OrderResponse:
        """
        提交订单（主要接口）
        
        Args:
            order_request: 订单请求
            
        Returns:
            OrderResponse: 订单响应
        """
        if not self.is_running:
            return OrderResponse(
                success=False,
                status=OrderStatus.REJECTED,
                message="交易引擎未启动",
                error_code="ENGINE_NOT_RUNNING"
            )
        
        # 检查并发限制
        if len(self.active_orders) >= self.config['max_concurrent_orders']:
            return OrderResponse(
                success=False,
                status=OrderStatus.REJECTED,
                message="超过最大并发订单数限制",
                error_code="MAX_CONCURRENT_ORDERS_EXCEEDED"
            )
        
        self.total_orders_processed += 1
        order_request.client_order_id = f"UTE_{int(time.time() * 1000)}_{self.total_orders_processed}"
        
        try:
            # 添加到活跃订单
            self.active_orders[order_request.client_order_id] = order_request
            
            # 执行完整的交易流程
            response = await self._execute_trading_pipeline(order_request)
            
            # 记录订单历史
            self.order_history.append((order_request, response))
            
            # 保持历史记录长度
            if len(self.order_history) > 1000:
                self.order_history = self.order_history[-1000:]
            
            # 更新统计
            if response.success:
                self.successful_orders += 1
            else:
                self.failed_orders += 1
            
            return response
            
        except Exception as e:
            self.logger.error(f"订单处理异常: {e}")
            return OrderResponse(
                success=False,
                status=OrderStatus.FAILED,
                message=f"订单处理异常: {str(e)}",
                error_code="PROCESSING_EXCEPTION"
            )
        finally:
            # 从活跃订单中移除
            self.active_orders.pop(order_request.client_order_id, None)
    
    async def _execute_trading_pipeline(self, order_request: OrderRequest) -> OrderResponse:
        """执行完整的交易管道"""
        
        # 第1步：币种分类和参数获取
        try:
            order_request.tier = self.classifier.get_symbol_tier(order_request.symbol)
            order_request.parameters = self.parameter_matrix.get_parameters(
                order_request.symbol, order_request.tier
            )
            self.logger.debug(f"币种分类: {order_request.symbol} -> {order_request.tier.value}")
        except Exception as e:
            return OrderResponse(
                success=False,
                status=OrderStatus.REJECTED,
                message=f"币种分类失败: {str(e)}",
                error_code="CLASSIFICATION_FAILED"
            )
        
        # 第2步：订单验证
        validation_context = OrderValidationContext(
            symbol=order_request.symbol,
            side=order_request.side,
            quantity=order_request.quantity,
            price=order_request.price,
            order_type=order_request.order_type,
            tier=order_request.tier,
            parameters=order_request.parameters
        )
        
        try:
            validation_results = await self._validate_order(validation_context)
            order_request.validation_results = validation_results
            
            # 检查是否有致命错误
            fatal_errors = [r for r in validation_results if r['result'] == ValidationResult.FAIL]
            if fatal_errors:
                return OrderResponse(
                    success=False,
                    status=OrderStatus.REJECTED,
                    message=f"订单验证失败: {'; '.join([r['message'] for r in fatal_errors])}",
                    error_code="VALIDATION_FAILED",
                    validation_warnings=[r['message'] for r in validation_results 
                                       if r['result'] == ValidationResult.WARNING]
                )
            
        except Exception as e:
            return OrderResponse(
                success=False,
                status=OrderStatus.REJECTED,
                message=f"订单验证异常: {str(e)}",
                error_code="VALIDATION_EXCEPTION"
            )
        
        # 第3步：风险评估
        if self.config['enable_risk_management']:
            try:
                risk_assessment = await self._assess_risk(order_request)
                order_request.risk_assessment = risk_assessment
                
                # 检查风险控制决策
                if risk_assessment['action'] == RiskAction.STOP_TRADING:
                    return OrderResponse(
                        success=False,
                        status=OrderStatus.REJECTED,
                        message="风险控制：停止交易",
                        error_code="RISK_STOP_TRADING",
                        risk_alerts=[alert['message'] for alert in risk_assessment.get('alerts', [])]
                    )
                elif risk_assessment['action'] == RiskAction.REDUCE_POSITION:
                    # 调整订单数量
                    recommended_quantity = risk_assessment.get('recommended_quantity', order_request.quantity)
                    if recommended_quantity < order_request.quantity:
                        order_request.quantity = recommended_quantity
                        self.logger.info(f"风险控制调整数量: {order_request.symbol} -> {recommended_quantity}")
                
            except Exception as e:
                self.logger.error(f"风险评估异常: {e}")
                # 风险评估异常时采用保守策略
                return OrderResponse(
                    success=False,
                    status=OrderStatus.REJECTED,
                    message=f"风险评估异常: {str(e)}",
                    error_code="RISK_ASSESSMENT_EXCEPTION"
                )
        
        # 第4步：订单执行
        try:
            execution_response = await self._execute_order(order_request)
            
            # 第5步：记录交易结果
            if execution_response.success and self.config['enable_performance_monitoring']:
                await self._record_trade_result(order_request, execution_response)
            
            return execution_response
            
        except Exception as e:
            return OrderResponse(
                success=False,
                status=OrderStatus.FAILED,
                message=f"订单执行异常: {str(e)}",
                error_code="EXECUTION_EXCEPTION"
            )
    
    async def _validate_order(self, context: OrderValidationContext) -> List[Dict]:
        """执行订单验证"""
        validation_results = []
        
        # 基础参数验证
        result = self.validator.validate_basic_parameters(context)
        validation_results.append({
            'type': 'basic_parameters',
            'result': result.result,
            'message': result.message
        })
        
        # 价格合理性验证
        result = await self.validator.validate_price_reasonableness(context)
        validation_results.append({
            'type': 'price_reasonableness',
            'result': result.result,
            'message': result.message
        })
        
        # 交易所规则验证
        result = await self.validator.validate_exchange_rules(context)
        validation_results.append({
            'type': 'exchange_rules',
            'result': result.result,
            'message': result.message
        })
        
        # 保证金验证
        result = await self.validator.validate_margin_requirements(context)
        validation_results.append({
            'type': 'margin_requirements',
            'result': result.result,
            'message': result.message
        })
        
        # 流动性验证
        result = await self.validator.validate_liquidity(context)
        validation_results.append({
            'type': 'liquidity',
            'result': result.result,
            'message': result.message
        })
        
        # 风控验证
        result = await self.validator.validate_risk_control(context)
        validation_results.append({
            'type': 'risk_control',
            'result': result.result,
            'message': result.message
        })
        
        # 市场状态验证
        result = await self.validator.validate_market_state(context)
        validation_results.append({
            'type': 'market_state',
            'result': result.result,
            'message': result.message
        })
        
        return validation_results
    
    async def _assess_risk(self, order_request: OrderRequest) -> Dict:
        """执行风险评估"""
        symbol = order_request.symbol
        
        # 评估币种风险
        risk_level = self.risk_manager.assess_symbol_risk(symbol)
        
        # 检查是否允许交易
        can_trade, alerts = self.risk_manager.can_trade(symbol)
        
        # 获取推荐仓位
        recommended_quantity = self.risk_manager.get_recommended_position_size(
            symbol, order_request.quantity
        )
        
        # 确定风险行动
        if not can_trade:
            action = RiskAction.STOP_TRADING
        elif recommended_quantity < order_request.quantity:
            action = RiskAction.REDUCE_POSITION
        else:
            action = RiskAction.CONTINUE
        
        return {
            'risk_level': risk_level,
            'can_trade': can_trade,
            'action': action,
            'recommended_quantity': recommended_quantity,
            'alerts': [{'message': alert.message, 'level': alert.level.value} for alert in alerts]
        }
    
    async def _execute_order(self, order_request: OrderRequest) -> OrderResponse:
        """执行订单"""
        if not self.exchange_client:
            # 模拟执行（用于测试）
            return self._simulate_order_execution(order_request)
        
        try:
            # 准备订单参数
            order_params = {
                'symbol': order_request.symbol,
                'side': order_request.side,
                'type': order_request.order_type,
                'quantity': order_request.quantity,
                'timeInForce': order_request.time_in_force,
                'newClientOrderId': order_request.client_order_id
            }
            
            if order_request.order_type == 'LIMIT':
                order_params['price'] = order_request.price
            
            # 提交订单到交易所
            submit_time = time.time()
            exchange_response = await self.exchange_client.create_order(**order_params)
            
            # 解析交易所响应
            if exchange_response.get('status') in ['FILLED', 'PARTIALLY_FILLED']:
                return OrderResponse(
                    success=True,
                    order_id=exchange_response.get('orderId', ''),
                    status=OrderStatus.FILLED if exchange_response.get('status') == 'FILLED' else OrderStatus.PARTIALLY_FILLED,
                    message="订单执行成功",
                    executed_quantity=float(exchange_response.get('executedQty', 0)),
                    executed_price=float(exchange_response.get('price', 0)),
                    commission=float(exchange_response.get('commission', 0)),
                    submit_time=submit_time,
                    fill_time=time.time()
                )
            else:
                return OrderResponse(
                    success=True,
                    order_id=exchange_response.get('orderId', ''),
                    status=OrderStatus.SUBMITTED,
                    message="订单已提交",
                    submit_time=submit_time
                )
                
        except Exception as e:
            return OrderResponse(
                success=False,
                status=OrderStatus.FAILED,
                message=f"订单执行失败: {str(e)}",
                error_code="EXCHANGE_ERROR"
            )
    
    def _simulate_order_execution(self, order_request: OrderRequest) -> OrderResponse:
        """模拟订单执行（用于测试）"""
        import random
        
        # 模拟执行延迟
        time.sleep(random.uniform(0.1, 0.5))
        
        # 模拟成功率（95%）
        if random.random() < 0.95:
            executed_price = order_request.price if order_request.price > 0 else 50000  # 模拟价格
            commission = order_request.quantity * executed_price * 0.001  # 0.1% 手续费
            
            return OrderResponse(
                success=True,
                order_id=f"SIM_{int(time.time() * 1000)}",
                status=OrderStatus.FILLED,
                message="模拟订单执行成功",
                executed_quantity=order_request.quantity,
                executed_price=executed_price,
                commission=commission,
                submit_time=time.time(),
                fill_time=time.time()
            )
        else:
            return OrderResponse(
                success=False,
                status=OrderStatus.FAILED,
                message="模拟订单执行失败",
                error_code="SIMULATION_FAILURE"
            )
    
    async def _record_trade_result(self, order_request: OrderRequest, response: OrderResponse):
        """记录交易结果"""
        if not response.success or response.executed_quantity == 0:
            return
        
        # 计算PnL（这里简化处理，实际需要根据持仓情况计算）
        pnl = 0.0  # 需要根据实际业务逻辑计算
        
        trade_record = TradeRecord(
            symbol=order_request.symbol,
            side=order_request.side,
            quantity=response.executed_quantity,
            entry_price=response.executed_price,
            pnl=pnl,
            commission=response.commission,
            entry_time=response.submit_time,
            exit_time=response.fill_time,
            hold_duration=response.fill_time - response.submit_time,
            is_closed=True
        )
        
        # 记录到性能监控器
        self.performance_monitor.record_trade(trade_record)
        
        # 更新风险管理器
        self.risk_manager.update_trade_result(order_request.symbol, pnl)
        self.risk_manager.update_position_info(
            order_request.symbol,
            response.executed_quantity * response.executed_price
        )
    
    async def _performance_monitoring_task(self):
        """性能监控后台任务"""
        while self.is_running:
            try:
                # 每5分钟生成一次性能报告
                await asyncio.sleep(300)
                
                if not self.is_running:
                    break
                
                # 生成全局性能报告
                report = self.performance_monitor.generate_performance_report()
                self.logger.info(f"性能监控报告: 活跃币种 {report['summary']['active_symbols']}, "
                               f"总PnL {report['summary']['total_pnl']:.2f}")
                
            except Exception as e:
                self.logger.error(f"性能监控任务异常: {e}")
    
    async def _auto_optimization_task(self):
        """自动优化后台任务"""
        while self.is_running:
            try:
                # 每小时执行一次优化检查
                await asyncio.sleep(3600)
                
                if not self.is_running:
                    break
                
                # 获取优化报告
                opt_report = self.performance_monitor.get_optimization_report()
                if opt_report.get('recent_optimizations', 0) > 0:
                    self.logger.info(f"自动优化: 最近24小时优化了 {opt_report['recent_optimizations']} 次")
                
            except Exception as e:
                self.logger.error(f"自动优化任务异常: {e}")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            'is_running': self.is_running,
            'total_orders_processed': self.total_orders_processed,
            'successful_orders': self.successful_orders,
            'failed_orders': self.failed_orders,
            'success_rate': self.successful_orders / self.total_orders_processed if self.total_orders_processed > 0 else 0,
            'active_orders_count': len(self.active_orders),
            'supported_symbols_count': len(self.classifier.symbol_cache),
            'monitored_symbols_count': len(self.performance_monitor.symbol_performances),
            'config': self.config
        }
    
    def get_symbol_status(self, symbol: str) -> Dict[str, Any]:
        """获取币种状态"""
        tier = self.classifier.get_symbol_tier(symbol)
        parameters = self.parameter_matrix.get_parameters(symbol, tier)
        performance = self.performance_monitor.get_symbol_performance(symbol)
        risk_level = self.risk_manager.assess_symbol_risk(symbol)
        can_trade, alerts = self.risk_manager.can_trade(symbol)
        
        return {
            'symbol': symbol,
            'tier': tier.value,
            'parameters': parameters.__dict__ if parameters else None,
            'performance': performance.__dict__ if performance else None,
            'risk_level': risk_level.value,
            'can_trade': can_trade,
            'active_alerts': [alert.message for alert in alerts],
            'recent_orders': len([h for h in self.order_history 
                                if h[0].symbol == symbol and time.time() - h[0].request_time < 3600])
        }

# 使用示例
if __name__ == "__main__":
    async def main():
        # 初始化交易引擎
        engine = UniversalTradingEngine()
        
        # 启动引擎
        await engine.start_engine()
        
        # 创建订单请求
        order = OrderRequest(
            symbol='BTCUSDT',
            side='BUY',
            quantity=0.001,
            order_type='MARKET'
        )
        
        # 提交订单
        response = await engine.submit_order(order)
        print(f"订单响应: {response}")
        
        # 获取引擎状态
        status = engine.get_engine_status()
        print(f"引擎状态: {status}")
        
        # 停止引擎
        await engine.stop_engine()
    
    # 运行示例
    asyncio.run(main())
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PIPPINUSDT的交易规则
"""

import requests
import json

def get_pippinusdt_rules():
    """获取PIPPINUSDT的交易规则"""
    try:
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        symbols = data.get('symbols', [])
        
        # 查找PIPPINUSDT
        for symbol_info in symbols:
            if symbol_info['symbol'] == 'PIPPINUSDT':
                print(f"交易对: {symbol_info['symbol']}")
                print(f"状态: {symbol_info['status']}")
                print(f"基础资产精度: {symbol_info['baseAssetPrecision']}")
                print(f"报价资产精度: {symbol_info['quotePrecision']}")
                print(f"数量精度: {symbol_info['quantityPrecision']}")
                print(f"价格精度: {symbol_info['pricePrecision']}")
                
                # 解析过滤器
                filters = {}
                for f in symbol_info['filters']:
                    filters[f['filterType']] = f
                
                print("\n=== 交易规则 ===")
                if 'PRICE_FILTER' in filters:
                    pf = filters['PRICE_FILTER']
                    print(f"价格过滤器:")
                    print(f"  tickSize: {pf['tickSize']}")
                    print(f"  minPrice: {pf['minPrice']}")
                    print(f"  maxPrice: {pf['maxPrice']}")
                
                if 'LOT_SIZE' in filters:
                    ls = filters['LOT_SIZE']
                    print(f"数量过滤器:")
                    print(f"  stepSize: {ls['stepSize']}")
                    print(f"  minQty: {ls['minQty']}")
                    print(f"  maxQty: {ls['maxQty']}")
                
                if 'MIN_NOTIONAL' in filters:
                    mn = filters['MIN_NOTIONAL']
                    print(f"最小名义价值: {mn['notional']}")
                
                return {
                    'symbol': symbol_info['symbol'],
                    'status': symbol_info['status'],
                    'baseAssetPrecision': symbol_info['baseAssetPrecision'],
                    'quotePrecision': symbol_info['quotePrecision'],
                    'quantityPrecision': symbol_info['quantityPrecision'],
                    'pricePrecision': symbol_info['pricePrecision'],
                    'filters': filters
                }
        
        print("❌ 未找到PIPPINUSDT交易对")
        return None
        
    except Exception as e:
        print(f"❌ 获取交易规则失败: {e}")
        return None

def test_precision_formatting(rules):
    """测试精度格式化"""
    if not rules:
        return
    
    print(f"\n=== 测试精度格式化 ===")
    
    # 提取规则
    filters = rules['filters']
    tick_size = float(filters['PRICE_FILTER']['tickSize'])
    step_size = float(filters['LOT_SIZE']['stepSize'])
    min_qty = float(filters['LOT_SIZE']['minQty'])
    
    print(f"tickSize: {tick_size}")
    print(f"stepSize: {step_size}")
    print(f"minQty: {min_qty}")
    
    # 测试数量
    test_quantities = [100.0, 50.0, 25.0, 12.5, 6.25]
    test_price = 0.1
    
    for qty in test_quantities:
        print(f"\n--- 测试数量: {qty} ---")
        
        # 按stepSize调整数量
        adjusted_qty = (qty // step_size) * step_size
        adjusted_qty = max(adjusted_qty, min_qty)
        
        print(f"调整后数量: {adjusted_qty}")
        
        # 格式化为字符串
        import decimal as dec
        dec.getcontext().prec = 18
        
        qty_decimal = dec.Decimal(str(adjusted_qty))
        step_decimal = dec.Decimal(str(step_size))
        
        # 重新计算确保精度
        final_qty = (qty_decimal / step_decimal).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_decimal
        
        # 计算小数位数
        step_str = str(step_size)
        if '.' in step_str:
            decimal_places = len(step_str.split('.')[1])
        else:
            decimal_places = 0
        
        # 格式化
        if decimal_places > 0:
            qty_str = f"{float(final_qty):.{decimal_places}f}".rstrip('0').rstrip('.')
        else:
            qty_str = str(int(float(final_qty)))
        
        print(f"格式化结果: '{qty_str}'")
        print(f"小数位数: {len(qty_str.split('.')[1]) if '.' in qty_str else 0}")

if __name__ == "__main__":
    rules = get_pippinusdt_rules()
    test_precision_formatting(rules)

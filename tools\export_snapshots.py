import os
import sys
import json

try:
    import yaml  # optional
except Exception:
    yaml = None

# Ensure project root on path
sys.path.append('e:/allmac')

from http_client import HttpClient
from strategy.maker_channel_enhanced import MakerChannelEnhanced


def load_config():
    cfg = {}
    # Load YAML first (base), then JSON to override
    if yaml is not None:
        try:
            with open('config/config.yaml', 'r', encoding='utf-8') as f:
                base = yaml.safe_load(f) or {}
                if isinstance(base, dict):
                    cfg.update(base)
        except Exception:
            pass
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            override = json.load(f) or {}
            if isinstance(override, dict):
                cfg.update(override)
    except Exception:
        pass
    # Ensure required rate limiter keys exist
    cfg.setdefault('max_rate', 15)
    cfg.setdefault('trade_rate', 20)
    return cfg


def main():
    cfg = load_config()
    api_key = cfg.get('api_key', '')
    api_secret = cfg.get('api_secret', '')
    base_url = cfg.get('base_url', '')
    client = HttpClient(api_key=api_key, api_secret=api_secret, base_url=base_url)
    S = MakerChannelEnhanced(http=client, config=cfg)
    os.makedirs('logs', exist_ok=True)
    if bool(cfg.get('snapshot_export_enabled', False)):
        ok1 = S.export_score_snapshot_baseline('logs/score_snapshot_before_ab.csv')
        ok2 = S.export_score_snapshot('logs/score_snapshot_after_ab.csv')
        print('before_ab:', ok1)
        print('after_ab:', ok2)
    else:
        print('快照导出已跳过（配置 snapshot_export_enabled=False）')


if __name__ == '__main__':
    main()
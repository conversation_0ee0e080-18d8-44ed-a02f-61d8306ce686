"""
统一验证框架
Unified Validation Framework for Universal Trading

功能：
1. 标准化价格验证逻辑
2. 统一保证金检查机制
3. 交易规则验证和缓存
4. 市场状态感知验证
"""

import time
import logging
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

from ..classifier.symbol_classifier import SymbolTier, SymbolClassifier
from ..config.parameter_matrix import ParameterMatrix, TradingParameters

class ValidationResult(Enum):
    """验证结果状态"""
    PASS = "通过"
    FAIL = "失败"
    WARNING = "警告"

@dataclass
class ValidationError:
    """验证错误信息"""
    code: str
    message: str
    severity: ValidationResult
    details: Dict[str, Any] = None

@dataclass
class OrderValidationContext:
    """订单验证上下文"""
    symbol: str
    side: str  # 'BUY' or 'SELL'
    quantity: float
    price: float
    order_type: str = 'LIMIT'
    current_price: float = 0.0
    balance_usdt: float = 0.0
    position_size: float = 0.0
    tier: SymbolTier = None
    parameters: TradingParameters = None

class UnifiedValidator:
    """统一验证器"""
    
    def __init__(self, http_client=None, classifier: SymbolClassifier = None, 
                 parameter_matrix: ParameterMatrix = None):
        self.http = http_client
        self.classifier = classifier or SymbolClassifier(http_client)
        self.parameter_matrix = parameter_matrix or ParameterMatrix()
        self.logger = logging.getLogger(__name__)
        
        # 交易规则缓存
        self.exchange_info_cache: Dict[str, Dict] = {}
        self.cache_duration = 3600  # 缓存1小时
        
        # 市场状态缓存
        self.market_state_cache: Dict[str, Dict] = {}
        self.market_cache_duration = 300  # 缓存5分钟
        
        # 验证统计
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'warning_validations': 0,
            'validation_errors': {}
        }
    
    def validate_order(self, context: OrderValidationContext) -> Tuple[ValidationResult, List[ValidationError]]:
        """执行完整的订单验证"""
        self.validation_stats['total_validations'] += 1
        errors = []
        
        try:
            # 获取币种分类和参数
            if not context.tier:
                context.tier = self.classifier.get_symbol_tier(context.symbol)
            if not context.parameters:
                context.parameters = self.parameter_matrix.get_parameters(context.symbol, context.tier)
            
            # 1. 基础参数验证
            basic_errors = self._validate_basic_parameters(context)
            errors.extend(basic_errors)
            
            # 2. 价格合理性验证
            price_errors = self._validate_price_reasonableness(context)
            errors.extend(price_errors)
            
            # 3. 交易规则验证
            rule_errors = self._validate_exchange_rules(context)
            errors.extend(rule_errors)
            
            # 4. 保证金验证
            margin_errors = self._validate_margin_requirements(context)
            errors.extend(margin_errors)
            
            # 5. 流动性验证
            liquidity_errors = self._validate_liquidity(context)
            errors.extend(liquidity_errors)
            
            # 6. 风控验证
            risk_errors = self._validate_risk_controls(context)
            errors.extend(risk_errors)
            
            # 7. 市场状态验证
            market_errors = self._validate_market_state(context)
            errors.extend(market_errors)
            
            # 判断最终结果
            result = self._determine_validation_result(errors)
            
            # 更新统计
            if result == ValidationResult.PASS:
                self.validation_stats['passed_validations'] += 1
            elif result == ValidationResult.FAIL:
                self.validation_stats['failed_validations'] += 1
            else:
                self.validation_stats['warning_validations'] += 1
            
            # 记录错误统计
            for error in errors:
                if error.code not in self.validation_stats['validation_errors']:
                    self.validation_stats['validation_errors'][error.code] = 0
                self.validation_stats['validation_errors'][error.code] += 1
            
            self.logger.info(f"订单验证完成: {context.symbol} {context.side} {context.quantity} @ {context.price} -> {result.value}")
            
            return result, errors
            
        except Exception as e:
            self.logger.error(f"订单验证异常: {e}")
            error = ValidationError(
                code="VALIDATION_EXCEPTION",
                message=f"验证过程异常: {str(e)}",
                severity=ValidationResult.FAIL
            )
            return ValidationResult.FAIL, [error]
    
    def _validate_basic_parameters(self, context: OrderValidationContext) -> List[ValidationError]:
        """基础参数验证"""
        errors = []
        
        # 检查必要参数
        if not context.symbol:
            errors.append(ValidationError(
                code="MISSING_SYMBOL",
                message="缺少交易对符号",
                severity=ValidationResult.FAIL
            ))
        
        if context.quantity <= 0:
            errors.append(ValidationError(
                code="INVALID_QUANTITY",
                message=f"无效的数量: {context.quantity}",
                severity=ValidationResult.FAIL
            ))
        
        if context.price <= 0:
            errors.append(ValidationError(
                code="INVALID_PRICE",
                message=f"无效的价格: {context.price}",
                severity=ValidationResult.FAIL
            ))
        
        if context.side not in ['BUY', 'SELL']:
            errors.append(ValidationError(
                code="INVALID_SIDE",
                message=f"无效的交易方向: {context.side}",
                severity=ValidationResult.FAIL
            ))
        
        return errors
    
    def _validate_price_reasonableness(self, context: OrderValidationContext) -> List[ValidationError]:
        """价格合理性验证"""
        errors = []
        
        try:
            # 获取当前价格
            if context.current_price <= 0:
                context.current_price = self._get_current_price(context.symbol)
            
            if context.current_price <= 0:
                errors.append(ValidationError(
                    code="MISSING_CURRENT_PRICE",
                    message="无法获取当前价格",
                    severity=ValidationResult.FAIL
                ))
                return errors
            
            # 计算价格偏差
            price_deviation = abs(context.price - context.current_price) / context.current_price
            max_deviation = context.parameters.price_deviation_tolerance
            
            if price_deviation > max_deviation:
                errors.append(ValidationError(
                    code="PRICE_DEVIATION_EXCEEDED",
                    message=f"价格偏差过大: {price_deviation:.2%} > {max_deviation:.2%}",
                    severity=ValidationResult.FAIL,
                    details={
                        'order_price': context.price,
                        'current_price': context.current_price,
                        'deviation': price_deviation,
                        'max_allowed': max_deviation
                    }
                ))
            
            # 获取24小时价格范围
            price_range = self._get_24h_price_range(context.symbol)
            if price_range:
                high_24h, low_24h = price_range
                
                if context.price > high_24h * 1.1:  # 超过24h最高价10%
                    errors.append(ValidationError(
                        code="PRICE_TOO_HIGH",
                        message=f"价格过高: {context.price} > 24h最高价 {high_24h}",
                        severity=ValidationResult.WARNING,
                        details={'price': context.price, 'high_24h': high_24h}
                    ))
                
                if context.price < low_24h * 0.9:  # 低于24h最低价10%
                    errors.append(ValidationError(
                        code="PRICE_TOO_LOW",
                        message=f"价格过低: {context.price} < 24h最低价 {low_24h}",
                        severity=ValidationResult.WARNING,
                        details={'price': context.price, 'low_24h': low_24h}
                    ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="PRICE_VALIDATION_ERROR",
                message=f"价格验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _validate_exchange_rules(self, context: OrderValidationContext) -> List[ValidationError]:
        """交易规则验证"""
        errors = []
        
        try:
            # 获取交易规则
            rules = self._get_exchange_rules(context.symbol)
            if not rules:
                errors.append(ValidationError(
                    code="MISSING_EXCHANGE_RULES",
                    message="无法获取交易规则",
                    severity=ValidationResult.WARNING
                ))
                return errors
            
            # 验证最小名义价值
            notional = context.quantity * context.price
            min_notional = float(rules.get('minNotional', 0))
            
            if min_notional > 0 and notional < min_notional:
                errors.append(ValidationError(
                    code="MIN_NOTIONAL_NOT_MET",
                    message=f"名义价值不足: {notional:.2f} < {min_notional:.2f}",
                    severity=ValidationResult.FAIL,
                    details={'notional': notional, 'min_notional': min_notional}
                ))
            
            # 验证数量精度
            qty_precision = rules.get('quantityPrecision', 8)
            if self._count_decimals(context.quantity) > qty_precision:
                errors.append(ValidationError(
                    code="QUANTITY_PRECISION_ERROR",
                    message=f"数量精度超限: {context.quantity}, 最大精度: {qty_precision}",
                    severity=ValidationResult.FAIL,
                    details={'quantity': context.quantity, 'max_precision': qty_precision}
                ))
            
            # 验证价格精度
            price_precision = rules.get('pricePrecision', 8)
            if self._count_decimals(context.price) > price_precision:
                errors.append(ValidationError(
                    code="PRICE_PRECISION_ERROR",
                    message=f"价格精度超限: {context.price}, 最大精度: {price_precision}",
                    severity=ValidationResult.FAIL,
                    details={'price': context.price, 'max_precision': price_precision}
                ))
            
            # 验证最小数量
            min_qty = float(rules.get('minQty', 0))
            if min_qty > 0 and context.quantity < min_qty:
                errors.append(ValidationError(
                    code="MIN_QUANTITY_NOT_MET",
                    message=f"数量不足: {context.quantity} < {min_qty}",
                    severity=ValidationResult.FAIL,
                    details={'quantity': context.quantity, 'min_quantity': min_qty}
                ))
            
            # 验证最大数量
            max_qty = float(rules.get('maxQty', float('inf')))
            if context.quantity > max_qty:
                errors.append(ValidationError(
                    code="MAX_QUANTITY_EXCEEDED",
                    message=f"数量超限: {context.quantity} > {max_qty}",
                    severity=ValidationResult.FAIL,
                    details={'quantity': context.quantity, 'max_quantity': max_qty}
                ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="EXCHANGE_RULES_ERROR",
                message=f"交易规则验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _validate_margin_requirements(self, context: OrderValidationContext) -> List[ValidationError]:
        """保证金验证"""
        errors = []
        
        try:
            if context.side == 'BUY':
                # 买单需要USDT余额
                required_margin = context.quantity * context.price
                buffer_ratio = context.parameters.margin_buffer_ratio
                required_with_buffer = required_margin * (1 + buffer_ratio)
                
                if context.balance_usdt < required_with_buffer:
                    errors.append(ValidationError(
                        code="INSUFFICIENT_MARGIN",
                        message=f"保证金不足: {context.balance_usdt:.2f} < {required_with_buffer:.2f}",
                        severity=ValidationResult.FAIL,
                        details={
                            'available_balance': context.balance_usdt,
                            'required_margin': required_margin,
                            'buffer_ratio': buffer_ratio,
                            'required_with_buffer': required_with_buffer
                        }
                    ))
                elif context.balance_usdt < required_margin * (1 + buffer_ratio * 0.5):
                    # 缓冲不足但可以交易
                    errors.append(ValidationError(
                        code="LOW_MARGIN_BUFFER",
                        message=f"保证金缓冲不足: {context.balance_usdt:.2f}",
                        severity=ValidationResult.WARNING,
                        details={'available_balance': context.balance_usdt, 'recommended_buffer': required_with_buffer}
                    ))
            
            # 检查仓位限制
            position_value = abs(context.position_size) * context.current_price
            max_position_usdt = context.parameters.max_position_usdt
            
            if position_value > max_position_usdt:
                errors.append(ValidationError(
                    code="POSITION_LIMIT_EXCEEDED",
                    message=f"仓位超限: {position_value:.2f} > {max_position_usdt:.2f}",
                    severity=ValidationResult.FAIL,
                    details={'current_position_value': position_value, 'max_position': max_position_usdt}
                ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="MARGIN_VALIDATION_ERROR",
                message=f"保证金验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _validate_liquidity(self, context: OrderValidationContext) -> List[ValidationError]:
        """流动性验证"""
        errors = []
        
        try:
            # 检查24小时交易量
            volume_24h = self._get_24h_volume(context.symbol)
            min_volume = context.parameters.min_volume_24h
            
            if volume_24h < min_volume:
                errors.append(ValidationError(
                    code="INSUFFICIENT_LIQUIDITY",
                    message=f"流动性不足: 24h交易量 {volume_24h:.0f} < {min_volume:.0f}",
                    severity=ValidationResult.WARNING,
                    details={'volume_24h': volume_24h, 'min_required': min_volume}
                ))
            
            # 检查订单簿深度
            depth = self._get_order_book_depth(context.symbol)
            min_depth = context.parameters.min_depth_1pct
            
            if depth < min_depth:
                errors.append(ValidationError(
                    code="INSUFFICIENT_DEPTH",
                    message=f"订单簿深度不足: {depth:.0f} < {min_depth:.0f}",
                    severity=ValidationResult.WARNING,
                    details={'current_depth': depth, 'min_required': min_depth}
                ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="LIQUIDITY_VALIDATION_ERROR",
                message=f"流动性验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _validate_risk_controls(self, context: OrderValidationContext) -> List[ValidationError]:
        """风控验证"""
        errors = []
        
        try:
            # 检查仓位比例
            if context.balance_usdt > 0:
                position_ratio = (context.quantity * context.price) / context.balance_usdt
                max_ratio = context.parameters.max_position_ratio
                
                if position_ratio > max_ratio:
                    errors.append(ValidationError(
                        code="POSITION_RATIO_EXCEEDED",
                        message=f"仓位比例超限: {position_ratio:.2%} > {max_ratio:.2%}",
                        severity=ValidationResult.FAIL,
                        details={'position_ratio': position_ratio, 'max_ratio': max_ratio}
                    ))
            
            # 检查最小仓位
            notional = context.quantity * context.price
            min_position = context.parameters.min_position_usdt
            
            if notional < min_position:
                errors.append(ValidationError(
                    code="POSITION_TOO_SMALL",
                    message=f"仓位过小: {notional:.2f} < {min_position:.2f}",
                    severity=ValidationResult.FAIL,
                    details={'notional': notional, 'min_position': min_position}
                ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="RISK_VALIDATION_ERROR",
                message=f"风控验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _validate_market_state(self, context: OrderValidationContext) -> List[ValidationError]:
        """市场状态验证"""
        errors = []
        
        try:
            # 获取市场状态
            market_state = self._get_market_state(context.symbol)
            
            # 检查交易状态
            if market_state.get('status') != 'TRADING':
                errors.append(ValidationError(
                    code="MARKET_NOT_TRADING",
                    message=f"市场未开放交易: {market_state.get('status')}",
                    severity=ValidationResult.FAIL,
                    details={'market_status': market_state.get('status')}
                ))
            
            # 检查波动率
            volatility = market_state.get('volatility', 0)
            max_volatility = context.parameters.volatility_threshold
            
            if volatility > max_volatility:
                errors.append(ValidationError(
                    code="HIGH_VOLATILITY",
                    message=f"波动率过高: {volatility:.2%} > {max_volatility:.2%}",
                    severity=ValidationResult.WARNING,
                    details={'current_volatility': volatility, 'max_allowed': max_volatility}
                ))
            
        except Exception as e:
            errors.append(ValidationError(
                code="MARKET_STATE_ERROR",
                message=f"市场状态验证异常: {str(e)}",
                severity=ValidationResult.WARNING
            ))
        
        return errors
    
    def _determine_validation_result(self, errors: List[ValidationError]) -> ValidationResult:
        """根据错误列表确定验证结果"""
        if not errors:
            return ValidationResult.PASS
        
        # 检查是否有严重错误
        for error in errors:
            if error.severity == ValidationResult.FAIL:
                return ValidationResult.FAIL
        
        # 只有警告
        return ValidationResult.WARNING
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        try:
            if self.http:
                ticker = self.http.get('/fapi/v1/ticker/price', {'symbol': symbol})
                if ticker and 'price' in ticker:
                    return float(ticker['price'])
        except Exception as e:
            self.logger.warning(f"获取当前价格失败 {symbol}: {e}")
        return 0.0
    
    def _get_24h_price_range(self, symbol: str) -> Optional[Tuple[float, float]]:
        """获取24小时价格范围"""
        try:
            if self.http:
                ticker = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                if ticker:
                    high = float(ticker.get('highPrice', 0))
                    low = float(ticker.get('lowPrice', 0))
                    if high > 0 and low > 0:
                        return high, low
        except Exception as e:
            self.logger.warning(f"获取24h价格范围失败 {symbol}: {e}")
        return None
    
    def _get_24h_volume(self, symbol: str) -> float:
        """获取24小时交易量"""
        try:
            if self.http:
                ticker = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                if ticker:
                    volume = float(ticker.get('volume', 0))
                    price = float(ticker.get('lastPrice', 0))
                    return volume * price
        except Exception as e:
            self.logger.warning(f"获取24h交易量失败 {symbol}: {e}")
        return 0.0
    
    def _get_exchange_rules(self, symbol: str) -> Optional[Dict]:
        """获取交易规则（带缓存）"""
        # 检查缓存
        if symbol in self.exchange_info_cache:
            cache_data = self.exchange_info_cache[symbol]
            if time.time() - cache_data['timestamp'] < self.cache_duration:
                return cache_data['rules']
        
        try:
            if self.http:
                exchange_info = self.http.get('/fapi/v1/exchangeInfo')
                if exchange_info and 'symbols' in exchange_info:
                    for symbol_info in exchange_info['symbols']:
                        if symbol_info['symbol'] == symbol:
                            rules = {}
                            for filter_info in symbol_info.get('filters', []):
                                if filter_info['filterType'] == 'MIN_NOTIONAL':
                                    rules['minNotional'] = filter_info.get('minNotional', '0')
                                elif filter_info['filterType'] == 'LOT_SIZE':
                                    rules['minQty'] = filter_info.get('minQty', '0')
                                    rules['maxQty'] = filter_info.get('maxQty', '0')
                                elif filter_info['filterType'] == 'PRICE_FILTER':
                                    rules['minPrice'] = filter_info.get('minPrice', '0')
                                    rules['maxPrice'] = filter_info.get('maxPrice', '0')
                            
                            rules['quantityPrecision'] = symbol_info.get('quantityPrecision', 8)
                            rules['pricePrecision'] = symbol_info.get('pricePrecision', 8)
                            
                            # 缓存结果
                            self.exchange_info_cache[symbol] = {
                                'rules': rules,
                                'timestamp': time.time()
                            }
                            
                            return rules
        except Exception as e:
            self.logger.warning(f"获取交易规则失败 {symbol}: {e}")
        
        return None
    
    def _get_order_book_depth(self, symbol: str) -> float:
        """获取订单簿深度"""
        try:
            if self.http:
                depth = self.http.get('/fapi/v1/depth', {'symbol': symbol, 'limit': 20})
                if depth and 'bids' in depth and 'asks' in depth:
                    bid_depth = sum(float(bid[1]) for bid in depth['bids'][:10])
                    ask_depth = sum(float(ask[1]) for ask in depth['asks'][:10])
                    return (bid_depth + ask_depth) / 2
        except Exception as e:
            self.logger.warning(f"获取订单簿深度失败 {symbol}: {e}")
        return 0.0
    
    def _get_market_state(self, symbol: str) -> Dict:
        """获取市场状态（带缓存）"""
        # 检查缓存
        if symbol in self.market_state_cache:
            cache_data = self.market_state_cache[symbol]
            if time.time() - cache_data['timestamp'] < self.market_cache_duration:
                return cache_data['state']
        
        try:
            state = {'status': 'TRADING', 'volatility': 0.0}
            
            if self.http:
                # 获取交易状态
                exchange_info = self.http.get('/fapi/v1/exchangeInfo')
                if exchange_info and 'symbols' in exchange_info:
                    for symbol_info in exchange_info['symbols']:
                        if symbol_info['symbol'] == symbol:
                            state['status'] = symbol_info.get('status', 'TRADING')
                            break
                
                # 获取波动率（基于24h价格变化）
                ticker = self.http.get('/fapi/v1/ticker/24hr', {'symbol': symbol})
                if ticker:
                    price_change_pct = float(ticker.get('priceChangePercent', 0))
                    state['volatility'] = abs(price_change_pct) / 100
            
            # 缓存结果
            self.market_state_cache[symbol] = {
                'state': state,
                'timestamp': time.time()
            }
            
            return state
            
        except Exception as e:
            self.logger.warning(f"获取市场状态失败 {symbol}: {e}")
            return {'status': 'UNKNOWN', 'volatility': 0.0}
    
    def _count_decimals(self, value: float) -> int:
        """计算小数位数"""
        return len(str(value).split('.')[-1]) if '.' in str(value) else 0
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        total = self.validation_stats['total_validations']
        if total == 0:
            return self.validation_stats
        
        return {
            **self.validation_stats,
            'success_rate': self.validation_stats['passed_validations'] / total,
            'failure_rate': self.validation_stats['failed_validations'] / total,
            'warning_rate': self.validation_stats['warning_validations'] / total
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.exchange_info_cache.clear()
        self.market_state_cache.clear()
        self.logger.info("验证器缓存已清空")

# 使用示例
if __name__ == "__main__":
    # 初始化验证器
    validator = UnifiedValidator()
    
    # 创建验证上下文
    context = OrderValidationContext(
        symbol='BTCUSDT',
        side='BUY',
        quantity=0.001,
        price=50000.0,
        current_price=49500.0,
        balance_usdt=1000.0
    )
    
    # 执行验证
    result, errors = validator.validate_order(context)
    
    print(f"验证结果: {result.value}")
    for error in errors:
        print(f"  {error.severity.value}: {error.message}")
    
    # 获取统计信息
    stats = validator.get_validation_statistics()
    print(f"\n验证统计: {stats}")
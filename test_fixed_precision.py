#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的精度格式化函数
"""

import decimal as dec

def get_safe_decimal_places(value):
    """安全地计算小数位数，避免浮点数精度问题"""
    if value == 0:
        return 0
    
    # 使用字符串方式计算，避免浮点数精度问题
    value_str = str(value)
    if 'e' in value_str.lower():
        # 处理科学计数法
        if 'e-' in value_str.lower():
            exp = int(value_str.split('e-')[1])
            return exp
        else:
            return 0
    elif '.' in value_str:
        return len(value_str.split('.')[1])
    else:
        return 0

def fixed_format_order_params(price, qty, tick_size, step_size, min_qty):
    """修复后的下单参数格式化函数"""
    dec.getcontext().prec = 18
    
    # 价格格式化
    if tick_size:
        price_dec = (dec.Decimal(str(price)) / dec.Decimal(str(tick_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(tick_size))
        tick_decimals = get_safe_decimal_places(tick_size)
        # 限制最大小数位数为8位，避免精度错误
        max_decimals = min(tick_decimals, 8)
        price_str = f"{float(price_dec):.{max_decimals}f}".rstrip('0').rstrip('.')
        if not price_str or price_str == '':
            price_str = f"{float(price_dec):.{max(1, max_decimals)}f}"
    else:
        price_str = str(price)
    
    # 数量格式化
    if step_size:
        qty_dec = (dec.Decimal(str(qty)) / dec.Decimal(str(step_size))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(step_size))
        qty_dec = max(qty_dec, dec.Decimal(str(min_qty or 0)))
        step_decimals = get_safe_decimal_places(step_size)
        # 限制最大小数位数为8位，避免精度错误
        max_decimals = min(step_decimals, 8)
        qty_str = f"{float(qty_dec):.{max_decimals}f}".rstrip('0').rstrip('.')
        if not qty_str or qty_str == '':
            qty_str = f"{float(qty_dec):.{max(1, max_decimals)}f}"
    else:
        qty_str = str(qty)
    
    return price_str, qty_str

def test_fixed_formatting():
    """测试修复后的格式化函数"""
    print("=== 测试修复后的策略格式化函数 ===")
    
    # 从日志中提取的实际参数
    actual_qty = 236.417
    actual_price = 0.400000
    
    print(f"实际参数: qty={actual_qty}, price={actual_price}")
    
    # 测试不同的交易规则
    test_cases = [
        {
            "name": "高精度新币",
            "tick_size": 0.00001,    # 5位小数
            "step_size": 0.001,      # 3位小数
            "min_qty": 0.001,
            "min_notional": 5.0
        },
        {
            "name": "中精度新币", 
            "tick_size": 0.0001,     # 4位小数
            "step_size": 0.01,       # 2位小数
            "min_qty": 0.01,
            "min_notional": 5.0
        },
        {
            "name": "低精度新币",
            "tick_size": 0.001,      # 3位小数
            "step_size": 0.1,        # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0
        },
        {
            "name": "超低精度新币",
            "tick_size": 0.01,       # 2位小数
            "step_size": 1.0,        # 整数
            "min_qty": 1.0,
            "min_notional": 5.0
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        test_case_fixed(actual_qty, actual_price, case)

def test_case_fixed(qty, price, rules):
    """测试修复后的格式化函数"""
    tick_size = rules['tick_size']
    step_size = rules['step_size']
    min_qty = rules['min_qty']
    min_notional = rules['min_notional']
    
    print(f"交易规则:")
    print(f"  tick_size: {tick_size}")
    print(f"  step_size: {step_size}")
    print(f"  min_qty: {min_qty}")
    print(f"  min_notional: {min_notional}")
    
    # 使用修复后的格式化函数
    price_str, qty_str = fixed_format_order_params(price, qty, tick_size, step_size, min_qty)
    
    print(f"修复后格式化结果:")
    print(f"  price_str: '{price_str}'")
    print(f"  qty_str: '{qty_str}'")
    
    # 检查精度
    price_decimals = len(price_str.split('.')[-1]) if '.' in price_str else 0
    qty_decimals = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
    
    print(f"精度检查:")
    print(f"  价格小数位: {price_decimals} (≤8)")
    print(f"  数量小数位: {qty_decimals} (≤8)")
    
    # 检查名义价值
    try:
        notional = float(price_str) * float(qty_str)
        print(f"  名义价值: {notional:.6f} (最小: {min_notional}) {'✅' if notional >= min_notional else '❌'}")
    except ValueError as e:
        print(f"  名义价值计算错误: {e}")
    
    # 检查是否可能导致精度错误
    if price_decimals <= 8 and qty_decimals <= 8:
        print(f"  ✅ 精度检查通过，应该不会出现-1111错误")
    else:
        print(f"  ❌ 精度可能仍有问题")
    
    # 检查参数是否为空或无效
    if price_str and qty_str and price_str != '0' and qty_str != '0':
        print(f"  ✅ 参数有效性检查通过")
    else:
        print(f"  ❌ 参数可能无效: price='{price_str}', qty='{qty_str}'")

if __name__ == '__main__':
    test_fixed_formatting()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试ORDERUSDT精度问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy.maker_channel_enhanced import MakerChannelEnhanced
import decimal as dec

def debug_orderusdt_precision():
    """调试ORDERUSDT的精度处理"""
    print("=== ORDERUSDT 精度调试 ===")
    
    # 直接定义精度处理函数
    def _round_price(price, tick_size):
        """价格精度处理"""
        if price <= 0 or tick_size <= 0:
            return price
        import decimal as dec
        dec.getcontext().prec = 18
        tick_d = dec.Decimal(str(tick_size))
        price_d = dec.Decimal(str(price))
        rounded = float((price_d / tick_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * tick_d)
        return max(rounded, tick_size)  # 确保价格至少为一个tick

    def _round_qty(q, step, min_q, price=None, min_notional=None):
        """数量精度处理，考虑最小名义价值要求"""
        if q <= 0 or step <= 0:
            return min_q
        import decimal as dec
        dec.getcontext().prec = 18
        step_d = dec.Decimal(str(step))
        
        # 先按步长向下取整
        rounded_down = float((dec.Decimal(str(q)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * step_d)
        rounded_down = max(rounded_down, min_q)
        
        # 如果提供了价格和最小名义价值，检查是否满足要求
        if price and min_notional:
            notional = rounded_down * price
            if notional < min_notional:
                # 向上调整到满足最小名义价值的数量
                required_qty = min_notional / price
                rounded_up = float((dec.Decimal(str(required_qty)) / step_d).quantize(dec.Decimal('1'), rounding=dec.ROUND_UP) * step_d)
                return max(rounded_up, min_q)
        
        return rounded_down
    
    # 模拟ORDERUSDT的交易规则（根据常见新币规则）
    symbol = "ORDERUSDT"
    
    # 常见新币精度规则
    test_cases = [
        {
            "name": "高精度新币",
            "tick_size": 0.00001,  # 5位小数
            "step_size": 0.1,      # 1位小数
            "min_qty": 0.1,
            "min_notional": 5.0,
            "price": 0.12345,
            "size": 100.0
        },
        {
            "name": "中精度新币", 
            "tick_size": 0.0001,   # 4位小数
            "step_size": 1.0,      # 整数
            "min_qty": 1.0,
            "min_notional": 5.0,
            "price": 0.1234,
            "size": 50.0
        },
        {
            "name": "低精度新币",
            "tick_size": 0.001,    # 3位小数
            "step_size": 10.0,     # 10的倍数
            "min_qty": 10.0,
            "min_notional": 5.0,
            "price": 0.123,
            "size": 100.0
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"交易规则: tick_size={case['tick_size']}, step_size={case['step_size']}, min_qty={case['min_qty']}, min_notional={case['min_notional']}")
        print(f"原始参数: price={case['price']}, size={case['size']}")
        
        # 测试精度处理
        r_price = _round_price(case['price'], case['tick_size'])
        r_qty = _round_qty(case['size'], case['step_size'], case['min_qty'], r_price, case['min_notional'])
        
        print(f"精度处理后: price={r_price}, qty={r_qty}")
        
        # 测试降档处理
        retry_scales = [1.0, 0.75, 0.5, 0.25]
        for scale in retry_scales:
            q_try = r_qty * scale
            q_try = _round_qty(q_try, case['step_size'], case['min_qty'], r_price, case['min_notional'])
            
            # 模拟Decimal处理
            dec.getcontext().prec = 18
            
            # 价格字符串生成
            if case['tick_size']:
                price_dec = (dec.Decimal(str(r_price)) / dec.Decimal(str(case['tick_size']))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(case['tick_size']))
                price_str = format(price_dec.normalize(), 'f')
            else:
                price_str = str(r_price)
                
            # 数量字符串生成
            if case['step_size']:
                qty_dec = (dec.Decimal(str(q_try)) / dec.Decimal(str(case['step_size']))).quantize(dec.Decimal('1'), rounding=dec.ROUND_DOWN) * dec.Decimal(str(case['step_size']))
                qty_dec = max(qty_dec, dec.Decimal(str(case['min_qty'] or 0)))
                qty_str = format(qty_dec.normalize(), 'f')
            else:
                qty_str = str(q_try)
                
            notional = float(qty_str) * float(price_str)
            
            print(f"  降档{int(scale*100)}%: qty_str='{qty_str}', price_str='{price_str}', notional={notional:.6f}")
            
            # 检查是否满足最小名义价值
            if notional < case['min_notional']:
                print(f"    ❌ 名义价值不足: {notional:.6f} < {case['min_notional']}")
            else:
                print(f"    ✅ 名义价值满足: {notional:.6f} >= {case['min_notional']}")
                
            # 检查字符串格式是否可能导致精度错误
            try:
                # 模拟API调用参数
                test_qty = float(qty_str)
                test_price = float(price_str)
                
                # 检查是否有多余的小数位
                qty_decimal_places = len(qty_str.split('.')[-1]) if '.' in qty_str else 0
                price_decimal_places = len(price_str.split('.')[-1]) if '.' in price_str else 0
                
                # 根据step_size和tick_size计算期望的小数位数
                expected_qty_decimals = len(str(case['step_size']).split('.')[-1]) if '.' in str(case['step_size']) else 0
                expected_price_decimals = len(str(case['tick_size']).split('.')[-1]) if '.' in str(case['tick_size']) else 0
                
                if qty_decimal_places > expected_qty_decimals:
                    print(f"    ⚠️  数量精度可能过高: {qty_decimal_places} > {expected_qty_decimals}")
                if price_decimal_places > expected_price_decimals:
                    print(f"    ⚠️  价格精度可能过高: {price_decimal_places} > {expected_price_decimals}")
                    
            except Exception as e:
                print(f"    ❌ 参数转换错误: {e}")

if __name__ == '__main__':
    debug_orderusdt_precision()